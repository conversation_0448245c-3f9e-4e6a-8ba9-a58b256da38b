<template>
	<view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
		<!-- 顶部标题 -->
		<CustomHead title="债券详情" />

		<!-- 债券详情卡片 -->
		<scroll-view 
			scroll-y 
			class="detail-list" 
			:scroll-with-animation="true"
			:enhanced="true"
			:show-scrollbar="false"
			:refresher-enabled="true" 
			:refresher-triggered="isRefreshing"
			@refresherpulling="onPulling" 
			@refresherrefresh="onRefresh" 
			@refresherrestore="onRestore" 
			@refresherabort="onAbort">

			<!-- 空状态提示 -->
			<view v-if="bondList.length === 0" class="empty-state">
				<view class="empty-icon">📋</view>
				<view class="empty-text">暂无债券详情数据</view>
			</view>

			<!-- 债券详情卡片列表 -->
			<view v-else v-for="(item, index) in bondList" :key="index" class="detail-card">
				<view class="card-header">
					<view class="card-title">{{ item.s_info_name }}</view>
					<view class="status-tag" :style="{backgroundColor: item.statusColor, color: item.statusTextColor}">{{ item.status }}</view>
				</view>

				<view class="card-body">
					<!-- 主要信息 -->
					<view class="main-info-grid">
						<view class="main-info-item">
							<view class="main-info-value number-font">{{ formatDecimal(item.bIssueAmountact) }}</view>
							<view class="main-info-label">规模(亿)</view>
						</view>
						<view class="main-info-item">
							<view class="main-info-value number-font">{{ formatDecimal(item.bInfoOutstandingbalance) }}</view>
							<view class="main-info-label">占用额度(亿)</view>
						</view>
						<view class="main-info-item">
							<view class="main-info-value number-font">{{ formatDecimal(item.latestCouponrate) }}</view>
							<view class="main-info-label">票面利率(%)</view>
						</view>
					</view>

					<!-- 额外信息 -->
					<view class="extra-info">
						<view class="info-row">
							<view class="row-label">债券代码</view>
							<view class="row-value">{{ item.sInfoWindcode }}</view>
						</view>
						<view class="info-row">
							<view class="row-label">债券类型</view>
							<view class="row-value">{{ item.bondTypeName2 }}</view>
						</view>
						<view class="info-row">
							<view class="row-label">发行日期</view>
							<view class="row-value number-font">{{ item.bIssueLastissue }}</view>
						</view>
						<view class="info-row">
							<view class="row-label">兑付日期</view>
							<view class="row-value number-font">{{ item.bInfoPaymentdate }}</view>
						</view>
						<view class="info-row">
							<view class="row-label">发行期限</view>
							<view class="row-value number-font">{{ item.termStr || '--' }}</view>
						</view>
						<view class="info-row">
							<view class="row-label">主承销商</view>
							<view class="row-value">{{ item.allLu }}</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import CustomHead from '@/components/head/head.vue';
import { getRegisterQuotaDetail } from '@/api/registerAbove';
import { getAssetUrl } from '@/config/assets';



// 页面参数
const pageParams = ref({});
// 当前页码
const pageNo = ref(1);
// 每页条数
const pageSize = ref(10);

// 获取注册额度详情
const getRegisterQuotaDetailData = () => {
	
	const data = {
		params: {
			ownedModuleid: "708631605142536192",
			ccid: 'befb38cb2c6d45fe883e0203bf79bb15',
			filenum: pageParams.value.filenum
		},
		page: {
			pageNo: pageNo.value,
			pageSize: pageSize.value,
			// sort: null,
			// direction: null
		}
	}
	getRegisterQuotaDetail(data).then(res => {
		console.log(res);
		if (res.data?.data?.pageInfo?.list?.length > 0) {
			bondList.value = res.data?.data?.pageInfo?.list;
		}
	})
}
// 模拟数据
const bondList = ref([]);

// 页面数据
const isRefreshing = ref(false);

// 格式化数值方法 - 保留小数点后四位
const formatDecimal = (value) => {
	// 如果值为null、undefined或空字符串，返回默认值
	if (value === null || value === undefined || value === '') {
		return '0.0000';
	}
	
	// 如果是字符串类型，先去除前后空格
	if (typeof value === 'string') {
		value = value.trim();
		// 如果去除空格后为空字符串，返回默认值
		if (value === '') {
			return '0.0000';
		}
	}
	
	// 转换为数字类型
	const num = Number(value);
	
	// 如果转换失败（NaN），返回默认值
	if (isNaN(num)) {
		return '0.0000';
	}
	
	// 保留四位小数，不足时自动补0
	return num.toFixed(4);
};

// 下拉刷新相关函数
const onPulling = () => {
	// 下拉刷新触发中
};

const onRefresh = () => {
	isRefreshing.value = true;
	// 模拟网络请求延迟
	setTimeout(() => {
		isRefreshing.value = false;
	}, 1000);
};

const onRestore = () => {
	// 下拉刷新恢复
};

const onAbort = () => {
	// 下拉刷新中止
};

// 获取页面参数


// 初始化
onMounted(() => {
	// 获取当前页面参数的另一种方式（如果onLoad不工作）
	const pages = getCurrentPages();
	const currentPage = pages[pages.length - 1];
	const options = currentPage.options || {};
	
	console.log('当前页面参数：', options);
	pageParams.value = options;
	getRegisterQuotaDetailData();
	// 使用模拟数据，无需API调用
});
</script>

<style lang="scss" scoped>
.container {
	padding: 0 20rpx;
	background: linear-gradient(to bottom, #ffeac3, #fff);
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	height: 100vh;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}


// 列表样式
.detail-list {
	flex: 1;
	height: 0;
	overflow: hidden;
	
	/* 隐藏滚动条 - 通用浏览器 */
	scrollbar-width: none;
	-ms-overflow-style: none;
	
	/* 隐藏滚动条 - Webkit浏览器 */
	&::-webkit-scrollbar {
		display: none;
		width: 0 !important;
		height: 0 !important;
		background: transparent;
		color: transparent;
	}
	
	/* 特定于微信小程序的样式 */
	::-webkit-scrollbar {
		width: 0;
		height: 0;
		color: transparent;
		display: none;
	}
}

/* #ifdef MP-WEIXIN */
/* 微信小程序特定样式 */
::-webkit-scrollbar {
	width: 0;
	height: 0;
	color: transparent;
	display: none;
}
/* #endif */

// 空状态样式
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	text-align: center;
}

.empty-icon {
	font-size: 200rpx;
	margin-bottom: 40rpx;
	opacity: 0.5;
	line-height: 1;
}

.empty-text {
	font-size: 32rpx;
	color: #999;
	margin-bottom: 16rpx;
	font-weight: 500;
}


// 详情卡片样式
.detail-card {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin: 20rpx 0;
	box-shadow: 0rpx 14rpx 28rpx 0rpx rgba(219, 219, 219, 0.48);
}

.card-header {
	margin-bottom: 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.card-title {
	font-size: 36rpx;
	font-weight: bold;
	line-height: 1.4;
}

.status-tag {
	padding: 6rpx 16rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
}

.card-body {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

// 主要信息网格
.main-info-grid {
	display: flex;
	justify-content: space-between;
	padding-bottom: 30rpx;
}

.main-info-item {
	display: flex;
	flex-direction: column;
}

.main-info-value {
	font-size: 40rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 8rpx;
}

.main-info-label {
	font-size: 26rpx;
	color: #999;
}

// 额外信息部分
.extra-info {
	border-top: 1px solid #f0f0f0;
	padding-top: 30rpx;
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

.info-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.row-label {
	font-size: 28rpx;
	color: #999;
}

.row-value {
	font-size: 28rpx;
	color: #333;
    max-width:400rpx;
	text-align: right;
	font-weight: 500;
}
</style> 