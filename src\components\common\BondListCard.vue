<template>
	<view class="bond-list-card">
		<view class="card-header">
			<view class="bond-title">
				<view class="title-icon"></view>
				<text class="title-text">{{ title }}</text>
			</view>
			<view class="more-link" @click="onMoreClick" v-if="showMoreLink">
				<text class="more-text">更多</text>
                <uni-icons type="right" size="14" color="#FF8E2B"></uni-icons>
				<!-- <image class="icon-arrow" lazy-load
					:src="getAssetUrl('/home/<USER>')" mode=""></image> -->
			</view>
		</view>

		<view class="bond-list">
			<!-- 表头 -->
			<view class="list-header">
				<text class="header-item" v-for="(item, index) in headerItems" :key="index">{{ item }}</text>
			</view>

			<!-- 债券列表 -->
			<view class="list-content" :class="{ expanded: isExpanded }" @scroll="onBondListScroll">
				<view class="list-item" v-for="(item, index) in visibleBondList" :key="index">
					<view class="bond-name">{{ item[fieldNames.name] }}</view>
					<view class="bond-rate-container">
						<text class="bond-rate number-font">{{ formatRate(item[fieldNames.rate]) }}</text>
					</view>
					<view class="bond-rate-container">
						<text class="bond-change number-font" :class="item[fieldNames.changeType]">{{ formatRate(item[fieldNames.change]) }}</text>
						<!-- <image class="icon-arrow" lazy-load
							:src="getAssetUrl('/home/<USER>')" mode=""></image> -->
					</view>
				</view>

				<!-- 加载中提示 -->
				<view class="loading-more" v-if="isExpanded && bondListLoading">
					<text class="loading-text">加载中...</text>
				</view>
			</view>

			<!-- 查看更多 - 仅当数据大于5条时显示 -->
			<view class="view-more" @click="showMoreBonds" v-if="bondListData.length > defaultPageSize">
				<image class="icon-arrow" lazy-load :class="{ 'rotate-180': isExpanded }"
					:src="getAssetUrl('/home/<USER>')" mode=""></image>
				<text class="more-text">{{ isExpanded ? '收起' : '查看更多' }}</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { getAssetUrl } from '@/config/assets';

// 组件接收的属性
const props = defineProps({
	// 卡片标题
	title: {
		type: String,
		default: '债券列表'
	},
	// 表头名称数组
	headerItems: {
		type: Array,
		default: () => ['债券简称', '最新估值(%)', '票面利率(%)']
	},
	// 列表数据
	bondList: {
		type: Array,
		default: () => []
	},
	// 数据总条数
	total: {
		type: Number,
		default: 0
	},
	// 默认每页显示数量
	defaultPageSize: {
		type: Number,
		default: 3
	},
	// 是否显示更多链接
	showMoreLink: {
		type: Boolean,
		default: true
	},
	// 更多链接跳转的URL
	moreUrl: {
		type: String,
		default: ''
	},
	// 字段名称映射，用于自定义列表项数据字段名
	fieldNames: {
		type: Object,
		default: () => ({
			name: 'name',       // 债券名称字段
			rate: 'rate',       // 最新估值字段
			change: 'change',   // 票面利率字段
			changeType: 'changeType' // 变化类型字段，用于样式
		})
	}
});

// 定义事件
const emit = defineEmits(['loadMore', 'more']);

// 组件内部状态
const isExpanded = ref(false);
const bondListLoading = ref(false);
const currentPage = ref(1);
const bondListData = ref([]);

// 监听外部传入的bondList变化
watch(() => props.bondList, (newVal) => {
	if (currentPage.value === 1) {
		// 如果是第一页，直接替换数据
		bondListData.value = [...newVal];
	} else {
		// 如果不是第一页，追加数据
		bondListData.value = [...bondListData.value, ...newVal];
	}
	bondListLoading.value = false;
}, { immediate: true });

// 计算需要显示的债券列表
const visibleBondList = computed(() => {
	return isExpanded.value
		? bondListData.value
		: bondListData.value.slice(0, props.defaultPageSize);
});

// 格式化最新估值，保留四位小数
const formatRate = (rate) => {
	if (rate === null || rate === undefined || rate === '') {
		return '--';
	}
	const numRate = parseFloat(rate);
	return isNaN(numRate) ? rate : numRate.toFixed(4);
};

// 展开/收起列表
const showMoreBonds = () => {
	if (!isExpanded.value) {
		isExpanded.value = true;
		// 如果当前只有默认数量的数据，加载更多
		if (bondListData.value.length <= props.defaultPageSize) {
			loadMoreData();
		}
	} else {
		isExpanded.value = false;
	}
};

// 加载更多数据
const loadMoreData = () => {
	// 避免重复加载
	if (bondListLoading.value) return;
	
	// 如果已加载的数据等于或超过总数，不再加载
	if (bondListData.value.length >= props.total) return;
	
	bondListLoading.value = true;
	currentPage.value++;
	
	// 触发加载更多事件，由父组件处理实际加载逻辑
	emit('loadMore', {
		page: currentPage.value,
		pageSize: props.defaultPageSize
	});
};

// 滚动到底部加载更多
const onBondListScroll = (e) => {
	if (!isExpanded.value) return;

	const { scrollTop, scrollHeight, height } = e.detail;
	// 滚动到底部的阈值，设为50px
	if (scrollHeight - scrollTop - height < 50) {
		loadMoreData();
	}
};

// 点击"更多"按钮
const onMoreClick = () => {
	if (props.moreUrl) {
		// 如果提供了URL，则进行页面跳转
		uni.navigateTo({
			url: props.moreUrl
		});
	}
	// 同时触发more事件，保持向后兼容
	emit('more');
};

// 导出组件方法给父组件使用
defineExpose({
	// 重置组件状态
	reset() {
		isExpanded.value = false;
		currentPage.value = 1;
		bondListData.value = [];
		bondListLoading.value = false;
	},
	// 设置加载中状态
	setLoading(loading) {
		bondListLoading.value = loading;
	}
});
</script>

<style lang="scss" scoped>
.bond-list-card {
	background-color: #fff;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;
	margin-bottom: 20rpx;

	// 卡片标题
	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 40rpx;

		.bond-title {
			display: flex;
			align-items: center;

			.title-icon {
				width: 48rpx;
				height: 52rpx;
				position: relative;
				top: -10rpx;

				&::before {
					content: '';
					position: absolute;
					inset: 0;
					background: linear-gradient(135deg, rgba(133, 111, 254, 0.6) 0%, rgba(255, 237, 221, 0) 100%);
					clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
				}
			}

			.title-text {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				transform: translateX(-25rpx);
			}
		}

		.more-link {
			display: flex;
			align-items: center;
			color: #FF8E2B;

			.more-text {
				font-size: 28rpx;
				margin-right: 10rpx;
			}

			.icon-arrow {
				width: 28rpx;
				height: 28rpx;
			}
		}
	}

	// 债券列表
	.bond-list {
		padding: 0 30rpx;

		.list-header {
			display: flex;
			justify-content: space-between;
			padding: 30rpx;
			background: linear-gradient(180deg, #FAFAFA 0%, #F4F4F4 100%);
			box-shadow: inset 0rpx -2rpx 0rpx 0rpx #EAE9E9;

			.header-item {
				font-size: 28rpx;
				color: #000;
				font-weight: 500;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				
				&:nth-child(1) {
					width: 30%;
					text-align: left;
					padding-left: 10rpx;
				}
				
				&:nth-child(2),
				&:nth-child(3) {
					width: 30%;
					text-align: right;
					padding-right: 10rpx;
				}
			}
		}

		.list-content {
			max-height: 500rpx;
			overflow: hidden;
			transition: max-height 0.3s ease;

			&.expanded {
				max-height: 1200rpx;
				overflow-y: auto;
				scrollbar-width: none; /* Firefox */
				-ms-overflow-style: none; /* IE and Edge */
			}

			/* 隐藏滚动条 */
			&.expanded::-webkit-scrollbar {
				display: none; /* Chrome, Safari, Opera */
			}

			.list-item {
				display: flex;
				justify-content: space-between;
				padding: 30rpx;
				border-bottom: 2rpx solid #EAE9E9;

				.bond-name {
					font-size: 28rpx;
					color: #333;
					width: 30%;
					text-align: left;
					padding-left: 10rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				.bond-rate-container {
					display: flex;
					width: 30%;
					justify-content: flex-end;
					align-items: center;
					padding-right: 10rpx;

					.bond-rate,
					.bond-change {
						font-size: 28rpx;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						text-align: right;
						max-width: 90%;
					}
					
					.bond-rate {
						color: #333;
					}

					.bond-change {
						&.rate-up {
							color: #67c23a;
						}

						&.rate-down {
							color: #f56c6c;
						}
					}
					
					.icon-arrow {
						width: 32rpx;
						height: 32rpx;
						flex-shrink: 0;
					}
				}
			}
		}

		// 查看更多
		.view-more {
			display: flex;
			justify-content: center;
			flex-direction: column;
			align-items: center;
			padding: 30rpx;
			cursor: pointer;

			.more-text {
				font-size: 28rpx;
				color: #666;
			}

			.icon-arrow {
				width: 24rpx;
				height: 26rpx;
				margin-bottom: 10rpx;
			}

			.rotate-180 {
				transform: rotate(180deg);
			}
		}

		.loading-more {
			text-align: center;
			padding: 20rpx 0;

			.loading-text {
				color: #999;
				font-size: 24rpx;
			}
		}
	}
}

</style> 