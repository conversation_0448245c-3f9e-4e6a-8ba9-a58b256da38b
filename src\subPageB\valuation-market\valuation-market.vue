<template>
    <view class="container">
        <!-- 固定的头部区域 -->
        <view class="fixed-header">
            <CustomHead title="估值行情" />
        </view>

        <!-- 可滚动的内容区域 -->
        <view class="content-wrapper">
            <scroll-view class="scrollable-content" scroll-y :show-scrollbar="false" enhanced>
                <!-- 债券信息卡片 -->
                <view class="bond-info-card"
                    :style="{ backgroundImage: `url('${getAssetUrl('/scard-bg.png')}')` }">
                    <view class="bond-title">
                        <text class="bond-name">{{ pageParams.bondName }}</text>
                        <text class="bond-code">{{ pageParams.bondCode }}</text>
                    </view>
                    <view class="bond-main-value">
                        <text class="main-rate-value number-font">{{ parseFloat(pageParams.rate || '0').toFixed(4)
                            }}%</text>
                        <text class="main-rate-label">最新估值({{ pageParams.date }})</text>
                    </view>
                    <view class="bond-bottom-data">
                        <view class="bond-period">
                            <text class="period-value number-font">{{ pageParams.remainTerm || '28.9233Y' }}</text>
                            <text class="period-label">剩余期限</text>
                        </view>
                        <view class="bond-change">
                            <view class="rate-change" :class="parseFloat(pageParams.change || '0') > 0 ? 'up' : 'down'">
                                <view v-if="parseFloat(pageParams.change || '0') > 0"
                                    class="rate-change-icon-container">
                                    <text class="number-font">{{ parseFloat(pageParams.change || '0').toFixed(2)
                                    }}</text>
                                    <image class="rate-add-icon"
                                        :src="getAssetUrl('/add-red.png')" mode=""></image>
                                </view>
                                <view v-else class="rate-change-icon-container">
                                    <text class="number-font">{{ parseFloat(pageParams.change || '0').toFixed(2)
                                        }}</text>
                                    <image class="rate-down-icon"
                                        :src="getAssetUrl('/down-green.png')" mode=""></image>
                                </view>
                            </view>
                            <text class="change-label">涨跌BP</text>
                        </view>
                    </view>
                </view>

                <view class="chart-card">
                    <view class="card-content">
                        <!-- ==================== 时间筛选器 ==================== -->
                        <TimeFilterSelect :isShow="true" @filterChange="handleTimeFilterChange"
                            @openCalendar="toggleModal" />

                        <!-- ==================== 图表上方当前数据点信息 ==================== -->
                        <view class="chart-data-point">
                            <view class="point-date">
                                <view class="point-indicator"></view>
                                <text class="number-font">{{ latestDataDate }}</text>
                            </view>
                            <view class="point-value">估值: <text class="number-font">{{ latestDataValue }}</text></view>
                            <view class="point-change" :class="latestDataChange > 0 ? 'up' : (latestDataChange < 0 ? 'down' : 'flat')">
                                较昨日: <text class="number-font">{{ latestDataChange > 0 ? '+' : '' }}{{ latestDataChange }}BP</text>
                            </view>
                        </view>

                        <!-- ==================== 图表展示区 ==================== -->
                        <view class="chart-component">
                            <RateChart :chartData="chartData?.series?.[0]?.rawData || []" @point-touch="handlePointClick" />
                        </view>
                    </view>
                </view>
            </scroll-view>
        </view>

        <!-- 日期选择组件（浮动在内容上方） -->
        <DateRangePicker v-model:visible="showModal" :defaultStartDate="startDate" :defaultEndDate="endDate"
            @dateRangeChange="handleDateRangeChange" @update:visible="handleModalVisibleChange" />
    </view>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import CustomHead from '@/components/head/head.vue';
import DateRangePicker from '@/components/calendar/DateRangePicker.vue';
import TimeFilterSelect from '@/components/common/TimeFilterSelect.vue';
import RateChart from '@/components/MarketRate/RateChart.vue';
import { getCompanyValuationLine } from '@/api/marketRate';
import { getAssetUrl } from '@/config/assets';

const isShowChart = ref(true);

const pageParams = ref({
    bondName: '',
    bondCode: '',
    rate: '',
    change: '',
    date: '',
    remainTerm: ''
});

onLoad((options) => {
    const params = JSON.parse(decodeURIComponent(options.params || '{}'));
    console.log('页面参数:', params);

    pageParams.value = {
        bondName: params.sInfoName,
        bondCode: params.sInfoWindcode,
        rate: params.latestYtm,
        change: params.riseFallBp,
        date: params.bIssueFirstissue,
        remainTerm: params.remainTerm,
        bIsRight: params.bIsRight
    };

    if (pageParams.value.bondCode) {
        s_info_windcode.value = pageParams.value.bondCode;
    }
});


const fetchCompanyValuationLine = async () => {
    const data = {
        sInfoWindcode: s_info_windcode.value,
        tradeDtStart: tradeDtStart.value,
        tradeDtEnd: tradeDtEnd.value,
        valtype: 'YIELD',
        bIsRight: pageParams.value.bIsRight
    };
    try {
        const res = await getCompanyValuationLine(data);
        console.log('估值行情数据:', res);

        if (res.data && res.data.data && Array.isArray(res.data.data)) {
            updateChartDataFromApi(res.data.data);
        } else {
            clearChartData();
        }
    } catch (error) {
        console.error('获取估值行情数据失败:', error);
        clearChartData();
    }
}


const s_info_windcode = ref("")
const showModal = ref(false);

const toggleModal = () => {
    showModal.value = !showModal.value;
    isShowChart.value = !showModal.value;
};

const startDate = ref(oneYearAgo);
const endDate = ref(today);

const handleDateRangeChange = (dateRange) => {
    tradeDtStart.value = dateRange[0];
    tradeDtEnd.value = dateRange[1];
    fetchCompanyValuationLine();
};

const handleModalVisibleChange = (visible) => {
    isShowChart.value = !visible;
};

const handleTimeFilterChange = (data) => {
    activeTimeFilter.value = data.filter;
    tradeDtStart.value = data.dateRange[0];
    tradeDtEnd.value = data.dateRange[1];
    fetchCompanyValuationLine();
};

watch(() => showModal.value, (newValue) => {
    isShowChart.value = !newValue;
});

// 处理图表点击事件 - 与市场利率页面保持一致
const handlePointClick = (pointInfo) => {
    if (pointInfo) {
        // 更新图表上方的数据点信息
        latestDataDate.value = pointInfo.date;
        latestDataValue.value = `${pointInfo.value.toFixed(4)}%`;
        latestDataChange.value = pointInfo.bpChange === 'N/A' ? 0 : pointInfo.bpChange;
    }
};



// 计算近一年的起始日期和结束日期
const today = new Date();
const oneYearAgo = new Date();
oneYearAgo.setFullYear(today.getFullYear() - 1);

// 格式化日期为YYYY-MM-DD格式
const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};

const tradeDtStart = ref(formatDate(oneYearAgo));
const tradeDtEnd = ref(formatDate(today));

// 当前选中的时间筛选器
const activeTimeFilter = ref('1year');

// 图表数据
const chartData = ref({
    categories: [],
    series: [{
        name: '估值收益率',
        data: [],
        color: '#FF9900',
        rawData: []
    }]
});

// 图表上方数据点信息
const latestDataDate = ref('--');
const latestDataValue = ref('--');
const latestDataChange = ref(0);



// 特殊点（最新数据点）
const chartSpecialPoints = computed(() => {
    const categories = chartData.value.categories;
    const series = chartData.value.series;

    if (!categories || !categories.length || !series || !series.length) {
        return [];
    }

    // 标记最后一个点为特殊点
    const lastIndex = categories.length - 1;

    return [
        {
            index: lastIndex,
            value: series[0].data[lastIndex],
            color: '#FF9800',
            label: ''
        }
    ];
});

// 移除了原来的 currentDataPoint computed 属性，现在使用响应式变量 latestDataDate, latestDataValue, latestDataChange

// 处理接口返回的数据格式
const updateChartDataFromApi = (apiData) => {
    if (!apiData || !Array.isArray(apiData) || apiData.length === 0) {
        clearChartData();
        return;
    }

    // 转换接口数据为图表数据格式 - 适配 MarketRate/RateChart 组件
    const chartStaticData = apiData.map(item => ({
        date: item.tradeDt,
        value: parseFloat(item.bAnalYield || 0),
        bpChange: parseFloat(item.bAnalYieldAdd || 0)
    }));
    
    const categories = apiData.map(item => item.tradeDt);
    const seriesData = apiData.map(item => parseFloat(item.bAnalYield || 0));

    chartData.value = {
        categories: categories,
        series: [{
            name: '估值收益率',
            data: seriesData,
            color: '#FF9900',
            rawData: chartStaticData
        }]
    };

    // 显示最新数据
    
    // 更新图表上方最新数据点信息
    if (apiData.length > 0) {
        const latestData = apiData[apiData.length - 1];
        latestDataDate.value = latestData.tradeDt;
        latestDataValue.value = `${parseFloat(latestData.bAnalYield || 0).toFixed(4)}%`;
        latestDataChange.value = parseFloat(latestData.bAnalYieldAdd || 0);
    }
};

// 清空图表数据
const clearChartData = () => {
    chartData.value = {
        categories: [],
        series: [{
            name: '估值收益率',
            data: [],
            color: '#FF9900',
            rawData: []
        }]
    };
    
    // 重置图表上方数据点信息
    latestDataDate.value = '--';
    latestDataValue.value = '--';
    latestDataChange.value = 0;
};

onMounted(() => {
    fetchCompanyValuationLine();
});
</script>

<style lang="scss" scoped>
/* ====================== 1. 页面布局相关样式 ====================== */
/* 1.1 页面容器 */
.container {
    padding: 0 20rpx;
    height: 100vh;
    box-sizing: border-box;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    /* 防止整体页面滚动 */
}

/* 1.2 头部区域 */
.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

/* 1.3 内容区域 */
.content-wrapper {
    flex: 1;
    padding-top: 180rpx;
    /* 给固定头部留出空间 */
    overflow: hidden;
}

/* 1.4 滚动区域 */
.scrollable-content {
    height: 100%;
    padding: 10rpx 0 0;

    ::-webkit-scrollbar {
        display: none;
        width: 0;
        height: 0;
        color: transparent;
    }
}


/* ====================== 2. 债券信息卡片样式 ====================== */
/* 2.1 卡片容器 */
.bond-info-card {
    background-color: #FEF8F0;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 2.2 标题区域 */
.bond-title {
    margin-bottom: 50rpx;
}

.bond-name {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    display: block;
}

.bond-code {
    font-size: 24rpx;
    color: #999;
    display: block;
    margin-top: 4rpx;
}

/* 2.3 主要数值区域 */
.bond-main-value {
    margin-bottom: 50rpx;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.main-rate-value {
    font-size: 68rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
}

.main-rate-label {
    font-size: 28rpx;
    color: #999;
}

/* 2.4 底部数据区域 */
.bond-bottom-data {
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
}

.bond-period {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.period-value {
    font-size: 40rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 8rpx;
}

.period-label {
    font-size: 28rpx;
    color: #999;
}

.bond-change {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.rate-change {
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 8rpx;

    &.up {
        color: #E76056;
    }

    &.down {
        color: #4CAF50;
    }

    .rate-change-icon-container {
        display: flex;
        align-items: center;
    }

    .rate-add-icon {
        width: 28rpx;
        height: 28rpx;
        transform: rotate(-20deg);
        margin-left: 8rpx;
    }

    .rate-down-icon {
        width: 28rpx;
        height: 28rpx;
        transform: rotate(34deg);
        margin-left: 8rpx;
    }
}

.change-label {
    font-size: 28rpx;
    color: #999;
}

/* ====================== 3. 图表卡片样式 ====================== */
/* 3.1 卡片容器 */
.chart-card {
    background-color: white;
    border-radius: 20rpx;
    overflow: hidden;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 3.2 卡片内容 */
.card-content {
    padding: 30rpx;
}

/* 3.3 图表组件 */
.chart-component {
    background-color: white;
    border-radius: 16rpx;
    overflow: hidden;
    margin-top: 20rpx;
}

/* ====================== 4. 辅助样式 ====================== */
/* 4.1 加载状态 */
.loading-container {
    height: 400rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.8);
    margin: 20rpx 0;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);

    text {
        font-size: 28rpx;
        color: #999999;
    }
}

/* ==================== 5. 图表数据点信息样式 ==================== */
.chart-data-point {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    background-color: #F5F7FF;
    padding: 15rpx 20rpx;
    border: 4rpx solid #fafafa;
    box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(172, 172, 172, 0.2);
    border-radius: 10rpx;
    font-size: 22rpx;
}

.point-date {
    display: flex;
    align-items: center;
}

.point-indicator {
    width: 14rpx;
    height: 14rpx;
    border-radius: 50%;
    background-color: #6F7CD1;
    margin-right: 10rpx;
}

.point-value {
    color: #333;
}

.point-change {
    &.up {
        color: #E76056;
    }

    &.down {
        color: #62BB37;
    }
    
    &.flat {
        color: #FEB249;
    }
}
</style>