<template>
    <view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
        <!-- 固定的头部区域 -->
        <view class="fixed-header">
            <CustomHead title="债券收益率" />
        </view>

        <!-- 顶部tab切换 -->
        <view class="tab-container">
            <view class="tab-item" :class="{ active: activeTab === 'corporate' }" @click="switchTab('corporate')">中短期票据
            </view>
            <view class="tab-item" :class="{ active: activeTab === 'financial' }" @click="switchTab('financial')">城投债
            </view>
            <view class="tab-item" :class="{ active: activeTab === 'enterprise' }" @click="switchTab('enterprise')">企业债
            </view>
        </view>

        <!-- 可滚动的内容区域 -->
        <view class="content-wrapper">
            <scroll-view class="scrollable-content" scroll-y :show-scrollbar="false" enhanced>
                <view class="chart-card">
                    <view class="card-content">
                        <!-- 评级和期限选择 -->
                        <view class="rating-period-selector">
                            <view class="selector-item" @click="showRatingPickerDialog">
                                <text>评级:</text>
                                <view class="dropdown-wrapper">
                                    <text class="selected-value">{{ selectedRating }}</text>
                                    <text class="dropdown-arrow">▼</text>
                                </view>
                            </view>

                            <view class="divider"></view>

                            <view class="selector-item" @click="showPeriodPickerDialog">
                                <text>期限:</text>
                                <view class="dropdown-wrapper">
                                    <text class="selected-value">{{ selectedPeriod }}</text>
                                    <text class="dropdown-arrow">▼</text>
                                </view>
                            </view>
                        </view>

                        <!-- 评级和期限选择弹出框 -->
                        <SelectionPopup v-model:visible="showRatingPicker" title="选择评级" :options="ratingOptions"
                            :defaultSelected="selectedRating" @confirm="handleRatingConfirm" />

                        <SelectionPopup v-model:visible="showPeriodPicker" title="发行期限" :options="periodOptions"
                            :defaultSelected="selectedPeriod" @confirm="handlePeriodConfirm" />

                        <!-- 利率信息展示 -->
                        <RateInfoDisplay rateLabel="最新估值" :rateValue="currentRateData.bAnalYield"
                            :changeValue="currentRateData.bAnalYieldAdd" :timeValue="currentRateData.tradeDt" />

                        <!-- 时间筛选器 -->
                        <TimeFilterSelect :isShow="true" @filterChange="handleTimeFilterChange"
                            @openCalendar="toggleModal" />

                        <!-- 图表上方当前数据点信息 -->
                        <view class="chart-data-point">
                            <view class="point-date">
                                <view class="point-indicator"></view>
                                <text class="number-font">{{ latestDataDate }}</text>
                            </view>
                            <view class="point-value">估值: <text class="number-font">{{ latestDataValue }}</text></view>
                            <view class="point-change"
                                :class="latestDataChange > 0 ? 'up' : (latestDataChange < 0 ? 'down' : 'flat')">
                                较昨日: <text class="number-font">{{ latestDataChange > 0 ? '+' : '' }}{{ latestDataChange
                                    }}BP</text>
                            </view>
                        </view>

                        <!-- 图表展示区 -->
                        <view class="chart-component" v-show="isShowChart && chartData.length > 0">
                            <RateChart :chartData="chartData" @point-touch="handleChartPointTouch" />
                        </view>

                        <!-- 无数据时的图表占位区 -->
                        <view class="chart-placeholder" v-if="isShowChart && chartData.length === 0">
                            <view class="empty-chart">
                                <text>暂无图表数据</text>
                            </view>
                        </view>
                    </view>
                </view>

                <!-- 期限列表卡片 -->
                <PeriodListCard :bondTabType="activeTab" :bondType="bondTypeMap[activeTab]" :periodList="periodList"
                    :rating="selectedRating" :columns="[
                        { title: '期限', field: 'period' },
                        { title: '最新估值(%)', field: 'value' },
                        { title: '涨跌BP', field: 'change' }
                    ]" />

                <!-- 底部留白区域 -->
                <view class="bottom-space"></view>
            </scroll-view>
        </view>

        <!-- 日期选择组件 -->
        <DateRangePicker v-model:visible="showModal" :defaultStartDate="startDate" :defaultEndDate="endDate"
            @dateRangeChange="handleDateRangeChange" @update:visible="handleModalVisibleChange" />
    </view>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import PeriodListCard from '@/components/MarketRate/PeriodListCard.vue';
import CustomHead from '@/components/head/head.vue';
import DateRangePicker from '@/components/calendar/DateRangePicker.vue';
import TimeFilterSelect from '@/components/common/TimeFilterSelect.vue';
import SelectionPopup from '@/components/common/SelectionPopup.vue';
import RateChart from '@/components/MarketRate/RateChart.vue';
import RateInfoDisplay from '@/components/MarketRate/RateInfoDisplay.vue';
import { getBondYieldCurve } from '@/api/marketRate';
import { getSql } from '@/api/common';
import { getBondYieldTerm } from '@/api/dataDict';
import { getAssetUrl } from '@/config/assets';

// 通用日期格式化函数，支持两种格式：YYYYMMDD 和 YYYY/MM/DD，统一转换为YYYY/MM/DD格式
const formatDateString = (dateStr) => {
    if (!dateStr) return '--';

    // 如果是YYYY/MM/DD格式，保持原样
    if (dateStr.includes('/')) {
        return dateStr;
    }

    // 如果是YYYYMMDD格式，转换为YYYY/MM/DD
    if (dateStr.length === 8) {
        return `${dateStr.substring(0, 4)}/${dateStr.substring(4, 6)}/${dateStr.substring(6, 8)}`;
    }
    // 如果是YYYY-MM-DD格式,转换为YYYY/MM/DD
    if (dateStr.includes('-')) {
        return dateStr.replace(/-/g, '/');
    }

    // 其他情况直接返回原始字符串
    return dateStr;
};

// 图表日期格式化函数（和市场利率页面一样）
const formatDateToChart = (dateStr) => {
    if (!dateStr) return '';

    // 如果已经是YYYY/MM/DD格式，直接返回
    if (dateStr.includes('/') && dateStr.split('/').length === 3) {
        return dateStr;
    }

    // 如果是YYYYMMDD格式，转换为YYYY/MM/DD
    if (dateStr.length === 8 && /^\d{8}$/.test(dateStr)) {
        return `${dateStr.substring(0, 4)}/${dateStr.substring(4, 6)}/${dateStr.substring(6, 8)}`;
    }

    // 其他情况尝试解析日期
    try {
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}/${month}/${day}`;
        }
    } catch (e) {
        console.error('日期格式化失败:', dateStr, e);
    }

    return dateStr; // 如果无法处理，返回原始字符串
};

// 通用的bAnalYieldAdd字段处理函数
const formatYieldChange = (yieldAddValue) => {
    if (yieldAddValue === null || yieldAddValue === undefined) {
        return 0;
    }

    // 如果是数字类型，直接返回
    if (typeof yieldAddValue === 'number') {
        return yieldAddValue;
    }

    // 如果是字符串类型，需要处理
    if (typeof yieldAddValue === 'string') {
        // 去除可能的加号，然后转换为数字
        const cleanValue = yieldAddValue.replace(/^\+/, '');
        const numValue = parseFloat(cleanValue);
        return isNaN(numValue) ? 0 : numValue;
    }

    // 其他情况返回0
    return 0;
};

// 标签页配置
const activeTab = ref('corporate'); // 默认显示中短期票据

// 配置映射
const bondConfig = {
    corporate: {
        curveName: '中债中短期票据收益率曲线',
        bondTerm: 'BOND_TERM_MIDSHORT_RATIO',
        ratings: ['AAA+', 'AAA', 'AAA-', 'AA+', 'AA', 'AA-', 'A+', 'A', 'A-'],
        periods: ['7D', '14D', '1M', '2M', '3M', '6M', '9M', '1Y', '2Y', '3Y', '4Y', '5Y', '6Y', '7Y', '8Y', '9Y', '10Y', '15Y'],
        defaultRating: 'AAA-',
        defaultPeriod: '6M'
    },
    financial: {
        curveName: '中债城投债收益率曲线',
        bondTerm: 'BOND_TERM_CITYINVEST_RATIO',
        ratings: ['AAA', 'AA+', 'AA(2)', 'AA', 'AA-'],
        periods: ['1M', '3M', '6M', '9M', '1Y', '2Y', '3Y', '4Y', '5Y', '6Y', '7Y', '8Y', '9Y', '10Y', '15Y', '20Y', '30Y'],
        defaultRating: 'AA(2)',
        defaultPeriod: '6M'
    },
    enterprise: {
        curveName: '中债企业债收益率曲线',
        bondTerm: 'BOND_TERM_CORPORATE_RATIO',
        ratings: ['AAA', 'AAA-', 'AA+', 'AA', 'AA-', 'A+', 'A', 'A-'],
        periods: ['1M', '3M', '6M', '9M', '1Y', '2Y', '3Y', '4Y', '5Y', '6Y', '7Y', '8Y', '9Y', '10Y', '15Y', '20Y', '30Y'],
        defaultRating: 'AA+',
        defaultPeriod: '6M'
    }
};

// 债券类型映射
const bondTypeMap = {
    corporate: '中短期票据',
    financial: '城投债',
    enterprise: '企业债'
};

// 评级和期限选择相关
const selectedRating = ref(bondConfig.corporate.defaultRating);
const selectedPeriod = ref(bondConfig.corporate.defaultPeriod);
const showRatingPicker = ref(false);
const showPeriodPicker = ref(false);
const ratingOptions = ref(bondConfig.corporate.ratings);
const periodOptions = ref(bondConfig.corporate.periods);

// API请求参数
const ccid = ref("ee39f68a9cb541839e6e419e79629a65");
const ownedModuleid = ref("708631605142536192");
const bAnalCurveName = ref(bondConfig.corporate.curveName);
const bondTerm = ref(bondConfig.corporate.bondTerm);

// 获取当前日期
const now = new Date();
const oneYearAgo = new Date();
oneYearAgo.setFullYear(now.getFullYear() - 1);

// 格式化日期为YYYY-MM-DD格式
const formatDateToString = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};

const tradeDtStart = ref(formatDateToString(oneYearAgo));
const tradeDtEnd = ref(formatDateToString(now));
const activeTimeFilter = ref('1year');

// 期限字典数据
const periodDictData = ref({});

// 图表和数据显示相关
const isShowChart = ref(true);
const periodList = ref([]);
const currentRateData = ref({
    bAnalYield: 0,
    bAnalYieldAdd: 0,
    tradeDt: '--'
});

// 图表上方数据点信息
const latestDataDate = ref('--');
const latestDataValue = ref('--%');
const latestDataChange = ref(0);

// 图表数据（和市场利率页面一样的数组格式）
const chartData = ref([]);

// 弹窗和日期相关
const showModal = ref(false);
const startDate = ref(oneYearAgo);
const endDate = ref(now);



// 从期限列表中获取当前期限的数据
const updateCurrentRateData = () => {
    const matchedPeriod = periodList.value.find(item => item.period === selectedPeriod.value);
    if (matchedPeriod) {
        currentRateData.value = {
            bAnalYield: matchedPeriod.value,
            bAnalYieldAdd: matchedPeriod.change,
            tradeDt: formatDateString(matchedPeriod.time) || '--'
        };
    } else {
        // 没有匹配数据时，重置为默认值
        currentRateData.value = {
            bAnalYield: 0,
            bAnalYieldAdd: 0,
            tradeDt: '--'
        };
    }
};

// 监听selectedPeriod和periodList的变化，更新当前利率数据
watch([() => selectedPeriod.value, () => periodList.value], () => {
    updateCurrentRateData();
});

// 监听showModal的变化
watch(() => showModal.value, (newValue) => {
    isShowChart.value = !newValue;
});

// 切换标签页
const switchTab = (tab) => {
    activeTab.value = tab;
    const config = bondConfig[tab];

    bAnalCurveName.value = config.curveName;
    bondTerm.value = config.bondTerm;
    ratingOptions.value = config.ratings;
    periodOptions.value = config.periods;
    selectedRating.value = config.defaultRating;
    selectedPeriod.value = config.defaultPeriod;

    // 立即重置当前利率数据
    currentRateData.value = {
        bAnalYield: 0,
        bAnalYieldAdd: 0,
        tradeDt: '--'
    };

    // 获取新的期限字典数据后刷新数据
    fetchPeriodDict().then(() => {
        fetchMarketRateData();
        fetchPeriodList();
    });
};

// 处理评级确认
const handleRatingConfirm = (rating) => {
    selectedRating.value = rating;
    fetchMarketRateData();
    fetchPeriodList();
};

// 处理期限确认
const handlePeriodConfirm = (period) => {
    selectedPeriod.value = period;
    fetchMarketRateData();
    fetchPeriodList();
};

// 获取期限字典数据
const fetchPeriodDict = async () => {
    try {
        const res = await getBondYieldTerm([bondTerm.value]);

        if (res.data?.data?.[bondTerm.value]?.length > 0) {
            // 存储期限字典数据
            periodDictData.value = res.data.data[bondTerm.value].reduce((acc, item) => {
                acc[item.cnname] = item.itemcode;
                return acc;
            }, {});
        }
        return res;
    } catch (error) {
        console.error('获取期限字典失败', error);
    }
}

// 获取期限列表
const fetchPeriodList = async () => {
    const data = {
        params: {
            b_anal_curvename: bAnalCurveName.value,
            ownedModuleid: ownedModuleid.value,
            ccid: ccid.value,
            main_rating: selectedRating.value,
            bond_term: bondTerm.value,
            b_anal_curveterm: []
        },
        page: {
            pageNo: 1,
            pageSize: 20
        }
    }
    try {
        const res = await getSql(data);
        if (res.data?.data?.pageInfo?.list?.length > 0) {
            periodList.value = res.data.data.pageInfo.list.map(item => ({
                period: item.bAnalCurveterm,
                value: parseFloat(item.bAnalYield) || 0,
                change: formatYieldChange(item.bAnalYieldAdd),
                time: formatDateString(item.tradeDt)
            }));
        } else {
            periodList.value = [];
        }
        // 无论是否有数据，都要更新当前利率数据
        updateCurrentRateData();
    } catch (error) {
        console.error('获取期限列表数据失败', error);
        // 请求失败时也要重置数据
        periodList.value = [];
        updateCurrentRateData();
    }
}

// 获取债券收益率曲线
const fetchMarketRateData = async () => {

    const periodCode = periodDictData.value?.[selectedPeriod.value]
        ? [periodDictData.value[selectedPeriod.value].toString()]
        : ['0'];



    const data = {
        ownedModuleid: ownedModuleid.value,
        bAnalCurveterm: periodCode,
        date: [tradeDtStart.value, tradeDtEnd.value],
        radioDate: 12,
        mainRating: [selectedRating.value],
        bAnalCurveName: bAnalCurveName.value,
        tradeDtStart: tradeDtStart.value,
        tradeDtEnd: tradeDtEnd.value
    }

    try {
        const res = await getBondYieldCurve(data);

        const periodKey = selectedPeriod.value;
        const periodData = res.data?.data?.[periodKey] || [];

        updateChartData(periodData);
    } catch (error) {
        console.error('获取债券收益率曲线失败', error);
    }
}

// 更新图表数据（和市场利率页面一样的格式）
const updateChartData = (periodData) => {

    // 转换接口数据为图表数据格式
    chartData.value = periodData.map(item => ({
        date: formatDateToChart(item.tradeDt),
        value: parseFloat(item.bAnalYield) || 0,
        bpChange: formatYieldChange(item.bAnalYieldAdd)
    }));


    // 更新图表上方最新数据点信息
    if (chartData.value.length > 0) {
        const latestData = chartData.value[chartData.value.length - 1];
        latestDataDate.value = latestData.date;
        latestDataValue.value = `${latestData.value.toFixed(4)}%`;
        latestDataChange.value = latestData.bpChange;
    } else {
        latestDataDate.value = '--';
        latestDataValue.value = '--%';
        latestDataChange.value = 0;
    }
}

// 处理图表触摸事件（和市场利率页面一样的逻辑）
const handleChartPointTouch = (pointData) => {
    // 更新图表上方的数据点信息
    latestDataDate.value = pointData.date;
    latestDataValue.value = `${pointData.value}%`;
    latestDataChange.value = pointData.bpChange === 'N/A' ? 0 : pointData.bpChange;
};

// 显示评级选择弹出框
const showRatingPickerDialog = () => {
    showRatingPicker.value = true;
};

// 显示期限选择弹出框
const showPeriodPickerDialog = () => {
    showPeriodPicker.value = true;
};

// 切换弹窗显示状态
const toggleModal = () => {
    showModal.value = !showModal.value;
    isShowChart.value = !showModal.value;
};

// 处理日期区间变更
const handleDateRangeChange = (dateRange) => {
    tradeDtStart.value = dateRange[0];
    tradeDtEnd.value = dateRange[1];
    fetchMarketRateData();
};

// 处理弹窗可见性变化
const handleModalVisibleChange = (visible) => {
    showModal.value = visible;
    isShowChart.value = !visible;
};

// 处理时间筛选变更
const handleTimeFilterChange = (data) => {
    activeTimeFilter.value = data.filter;
    tradeDtStart.value = data.dateRange[0];
    tradeDtEnd.value = data.dateRange[1];
    fetchMarketRateData();
};

// 在组件挂载时调用
onMounted(() => {
    fetchPeriodDict().then(() => {
        fetchMarketRateData();
        fetchPeriodList();
    });
});
</script>

<style lang="scss" scoped>
/* 页面布局相关样式 */
.container {
    padding: 0 20rpx;
    height: 100vh;
    box-sizing: border-box;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

.content-wrapper {
    flex: 1;
    overflow: hidden;
}

.scrollable-content {
    height: 100%;
    padding: 10rpx 0 0;

    ::-webkit-scrollbar {
        display: none;
        width: 0;
        height: 0;
        color: transparent;
    }
}

.bottom-space {
    height: 100rpx;
    width: 100%;
}

/* Tab切换样式 */
.tab-container {
    display: flex;
    margin-bottom: 20rpx;
    border-radius: 10rpx;
    margin-top: 180rpx;

    .tab-item {
        flex: 1;
        padding: 30rpx 0;
        text-align: center;
        position: relative;
        font-size: 32rpx;
        color: #999;
        transition: all 0.3s ease;
        font-weight: bold;

        &.active {
            color: #FF9900;
            font-weight: bold;

            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 40%;
                height: 6rpx;
                background-color: #FF9900;
                border-radius: 6rpx 6rpx 0 0;
            }
        }
    }
}

/* 图表卡片样式 */
.chart-card {
    background-color: white;
    border-radius: 20rpx;
    overflow: hidden;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-content {
    padding: 30rpx;
}

.chart-component {
    background-color: white;
    border-radius: 16rpx;
    overflow: hidden;
    margin-top: 20rpx;
}


/* 图表数据点信息样式 */
.chart-data-point {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    background-color: #F5F7FF;
    padding: 15rpx 20rpx;
    border: 4rpx solid #fafafa;
    box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(172, 172, 172, 0.2);
    border-radius: 10rpx;
    font-size: 22rpx;
}

.point-date {
    display: flex;
    align-items: center;
}

.point-indicator {
    width: 14rpx;
    height: 14rpx;
    border-radius: 50%;
    background-color: #6F7CD1;
    margin-right: 10rpx;
}

.point-value {
    color: #333;
}

.point-change {
    &.up {
        color: #E76056;
    }

    &.down {
        color: #52C41A;
    }

    &.flat {
        color: #FEB249;
    }
}

/* 评级期限选择器样式 */
.rating-period-selector {
    display: flex;
    justify-content: center;
    margin-bottom: 20rpx;
    align-items: center;
}

.selector-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;

    text {
        color: #666;
        font-size: 28rpx;
        margin-right: 10rpx;
    }
}

.divider {
    width: 2rpx;
    height: 36rpx;
    background-color: #DDDDDD;
    margin: 0 30rpx;
}

.dropdown-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    text:nth-child(2) {
        font-size: 20rpx !important;
    }
}

.selected-value {
    color: #333;
    font-weight: bold;
}

.dropdown-arrow {
    font-size: 20rpx;
    color: #999;
    margin-left: 10rpx;
}

/* 利率信息卡片样式 */
.rate-info-card {
    background-color: #f5f5f5;
    border-radius: 16rpx;
    border: 2rpx solid #fff;
    margin-bottom: 40rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.03);
}

.rate-info {
    display: flex;
    justify-content: space-between;

    .info-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .rate-value,
        .rate-change {
            font-size: 36rpx;
            font-weight: bold;
            height: 50rpx;
            line-height: 50rpx;
            color: #333;
            margin-bottom: 6rpx;
        }

        .update-time {
            font-size: 30rpx;
            color: #333;
            height: 50rpx;
            line-height: 50rpx;
            margin-bottom: 6rpx;
        }

        .rate-change {
            &.up {
                color: #E76056;
            }

            &.down {
                color: #62BB37;
            }
        }

        .rate-label {
            font-size: 24rpx;
            color: #999;
            height: 34rpx;
            line-height: 34rpx;
        }
    }
}

/* 无数据时的图表占位区样式 */
.chart-placeholder {
    height: 400rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: white;
    border-radius: 16rpx;
    margin-top: 20rpx;
    padding: 30rpx;
}

.empty-chart {
    text-align: center;
    color: #999;
    font-size: 28rpx;
}
</style>