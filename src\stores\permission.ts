import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getPermissionList, getMpUserInfo } from '@/api/common'

interface UserInfo {
    companyVerList: Array<{ id: string, name: string }>;
    outCompCode?: string;
    [key: string]: any;
}

interface PermissionItem {
    combinationName: string;
    modulename: string;
    moduletype: string;
    showflag: string;
    [key: string]: any;
}

export const usePermissionStore = defineStore('permission', () => {
    // State
    const token = ref('')
    const mpUserInfo = ref<UserInfo>({} as UserInfo)
    const tabBarPermissions = ref<PermissionItem[]>([])
    const homePagePermissions = ref<PermissionItem[]>([])
    const sysVersion = ref('')

    // Getters
    const getToken = computed(() => token.value)
    const getUserInfo = computed(() => mpUserInfo.value)
    const getTabBarPermissions = computed(() => tabBarPermissions.value)
    const getHomePagePermissions = computed(() => homePagePermissions.value)
    const getSysVersion = computed(() => sysVersion.value)

    // 获取可用版本列表
    const getAvailableVersions = computed(() => {
        const companyVerList = mpUserInfo.value.companyVerList || [];
        return companyVerList;
    })

    // 权限检查相关的计算属性
    const hasPermission = computed(() => {
        return (moduleName: string, combinationName: string = '首页') => {
            const permissions = combinationName === '首页' ? homePagePermissions.value : tabBarPermissions.value;
            const permission = permissions.find(item => 
                item.modulename === moduleName && 
                item.moduletype !== 'directory'
            );
            return permission?.showflag === 'Y';
        }
    })

    // 首页权限检查
    const hasHomePermission = computed(() => {
        return (moduleName: string) => hasPermission.value(moduleName, '首页')
    })

    // TabBar权限检查
    const hasTabBarPermission = computed(() => {
        return (moduleName: string) => hasPermission.value(moduleName, '小程序')
    })

    // 具体的首页权限计算属性
    const hasBondPanelPermission = computed(() => hasHomePermission.value('债券面板'))
    const hasResearchTopicPermission = computed(() => hasHomePermission.value('专题研究'))
    const hasMarketNewsPermission = computed(() => hasHomePermission.value('债市资讯'))
    const hasGroupBondPermission = computed(() => hasHomePermission.value('集团债券'))
    const hasCompanyBondPermission = computed(() => hasHomePermission.value('我司债券'))

    // 显示研究与资讯卡片
    const showResearchCard = computed(() => 
        hasResearchTopicPermission.value || hasMarketNewsPermission.value
    )

    // 显示债券列表卡片
    const showBondListCard = computed(() => 
        hasGroupBondPermission.value || hasCompanyBondPermission.value
    )

    // 债券列表卡片标题
    const bondListCardTitle = computed(() => {
        const version = sysVersion.value;
        // 根据当前选择的版本显示对应标题
        if (version === 'group_mp') {
            return '集团债券';
        } else if (version === 'company_mp') {
            return '我司债券';
        }
        // 默认返回我司债券
        return '我司债券';
    })

    // 获取有权限的应用列表
    const getVisibleApps = computed(() => {
        return (allApps: Array<{name: string, [key: string]: any}>) => {
            if (homePagePermissions.value.length === 0) {
                return allApps;
            }
            return allApps.filter(app => hasHomePermission.value(app.name));
        }
    })

    // Actions
    // 设置token
    const setToken = (newToken: string) => {
        token.value = newToken
    }

    // 设置用户信息
    const setUserInfo = (userInfo: UserInfo) => {
        mpUserInfo.value = userInfo

        // 设置系统版本
        const companyVerList = userInfo.companyVerList
        if (companyVerList && companyVerList.length > 0) {
            if (companyVerList.length === 1) {
                sysVersion.value = companyVerList[0].id
            } else if (companyVerList.length >= 2) {
                sysVersion.value = companyVerList[0].id
            }
        }
    }

    // 设置权限信息
    const setPermissions = (permissionData: PermissionItem[]) => {
        // 提取TabBar权限(combinationName为'小程序'的权限)
        tabBarPermissions.value = permissionData.filter(item => item.combinationName === '小程序')
        // 提取首页模块权限(combinationName为'首页'的权限) 
        homePagePermissions.value = permissionData.filter(item => item.combinationName === '首页')
    }

    // 设置系统版本
    const setSysVersion = (version: string) => {
        sysVersion.value = version
    }

    // 版本切换方法 - 简化版
    const switchVersion = async (targetVersionId: string) => {
        try {
            setSysVersion(targetVersionId)
            const response = await getPermissionList({})
            
            if (response.data.code === '000') {
                setPermissions(response.data.data)
                return { success: true }
            } else {
                throw new Error('获取权限数据失败')
            }
        } catch (error) {
            console.error('版本切换失败:', error)
            return { success: false, error }
        }
    }

    // 获取用户信息和权限
    const getUserInfoAndPermissions = async () => {
        try {
            // 获取用户信息
            const mpUserInfoRes = await getMpUserInfo({})
            if (mpUserInfoRes.data.code == '000') {
                setUserInfo(mpUserInfoRes.data.data)

                // 获取权限列表
                const permissionList = await getPermissionList({})
                if (permissionList.data.code == '000') {
                    setPermissions(permissionList.data.data)
                }

                return true
            }
            return false
        } catch (error) {
            console.error('获取用户信息和权限失败：', error)
            return false
        }
    }

    // 清除所有状态
    const clearState = () => {
        token.value = ''
        mpUserInfo.value = {} as UserInfo
        tabBarPermissions.value = []
        homePagePermissions.value = []
        sysVersion.value = ''
    }

    return {
        // State
        token,
        mpUserInfo,
        tabBarPermissions,
        homePagePermissions,
        sysVersion,
        // Getters
        getToken,
        getUserInfo,
        getTabBarPermissions,
        getHomePagePermissions,
        getSysVersion,
        getAvailableVersions,
        // 权限检查方法
        hasPermission,
        hasHomePermission,
        hasTabBarPermission,
        // 具体权限计算属性
        hasBondPanelPermission,
        hasResearchTopicPermission,
        hasMarketNewsPermission,
        hasGroupBondPermission,
        hasCompanyBondPermission,
        showResearchCard,
        showBondListCard,
        bondListCardTitle,
        getVisibleApps,
        // Actions
        setToken,
        setUserInfo,
        setPermissions,
        setSysVersion,
        switchVersion,
        getUserInfoAndPermissions,
        clearState
    }
}, {
    persist: true // 数据持久化
}) 