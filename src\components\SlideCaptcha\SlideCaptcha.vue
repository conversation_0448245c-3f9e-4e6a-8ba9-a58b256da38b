<template>
	<view class="slide-captcha-overlay" v-if="visible" @click.self="handleClose">
		<view class="slide-captcha-container">
			<view class="slide-captcha-header">
				<text class="slide-captcha-title">安全验证</text>
				<view class="slide-captcha-close" @click="handleClose">×</view>
			</view>

			<view class="slide-captcha-content">
				<view class="captcha-image-container">
					<!-- 背景图 -->
					<image class="background-image" :src="backgroundImage" mode="aspectFill"></image>

					<!-- 滑块缺口 -->
					<view class="puzzle-hole" :style="{ left: targetLeft + 'px', top: puzzleTop + 'px' }"></view>
					
					<!-- 滑块 -->
					<view class="puzzle-piece" :style="{ left: puzzleLeft + 'px', top: puzzleTop + 'px' }"></view>
					
					<!-- 刷新按钮 -->
					<view class="refresh-btn" @tap.stop="refreshCaptcha">
						<text class="refresh-icon">↻</text>
					</view>
				</view>

				<!-- 滑动条 -->
				<view class="slider-container">
					<view class="slider-track">
						<view class="slider-mask" :style="{ width: sliderLeft + 'px' }"></view>
						<view class="slider-button" :style="{ left: sliderLeft + 'px' }"
							@touchstart.prevent="sliderTouchStart"
							@touchmove.prevent="sliderTouchMove"
							@touchend.prevent="sliderTouchEnd"
							@mousedown.prevent="sliderTouchStart"
							@mousemove.prevent="sliderTouchMove"
							@mouseup.prevent="sliderTouchEnd">
							<view class="slider-bars">
								<view class="slider-bar"></view>
								<view class="slider-bar"></view>
								<view class="slider-bar"></view>
							</view>
						</view>
					</view>
					<view class="slider-text">请向右拖动滑块完成拼图</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { getAssetUrl } from '@/config/assets';

// 接收父组件传递的参数
const props = defineProps({
	visible: {
		type: Boolean,
		default: false
	}
});

// 向父组件发送事件
const emit = defineEmits(['close', 'success']);

// 响应式数据
const backgroundImage = ref(getAssetUrl('/1.png')); // 使用模糊效果的背景图
const puzzleSize = ref(40);
const puzzleLeft = ref(0); // 拼图的初始位置
const puzzleTop = ref(60);
const targetLeft = ref(200); // 拼图应该移动到的目标位置
const sliderLeft = ref(0);
const isDragging = ref(false);
const startX = ref(0);
const sliderText = ref('请向右拖动滑块完成拼图');
const tolerance = 10; // 允许的误差范围

// 监听visible变化，当组件显示时重置状态
watch(() => props.visible, (newValue) => {
	console.log('SlideCaptcha visible changed:', newValue);
	if (newValue) {
		console.log('重置验证码状态');
		resetCaptcha();
	}
});

// 组件挂载时初始化
onMounted(() => {
	console.log('SlideCaptcha mounted');
	generateRandomPosition();
});

// 生成随机位置
const generateRandomPosition = () => {
	puzzleTop.value = Math.floor(Math.random() * (120 - puzzleSize.value)) + 20;
	targetLeft.value = Math.floor(Math.random() * (240 - puzzleSize.value - 40)) + 80;
};

// 完全重置验证码状态
const resetCaptcha = () => {
	// 重置滑块位置
	sliderLeft.value = 0;
	puzzleLeft.value = 0;
	
	// 生成随机图片序号(1-5之间的整数)
	const randomImageNum = Math.floor(Math.random() * 4) + 1;
	
	// 重新获取背景图片（添加时间戳防止缓存）
	backgroundImage.value = `${getAssetUrl(`/${randomImageNum}.png`)}?t=${new Date().getTime()}`;
	
	// 重新生成目标位置
	generateRandomPosition();
	
	// 重置拖动状态
	isDragging.value = false;
};

// 刷新验证码 - 调用重置函数
const refreshCaptcha = () => {
	console.log('刷新验证码');
	resetCaptcha();
};

// 关闭验证
const handleClose = () => {
	console.log('关闭验证');
	emit('close');
};

// 滑块触摸开始
const sliderTouchStart = (e) => {
	console.log('滑块触摸开始');
	isDragging.value = true;
	if (e.type === 'touchstart') {
		startX.value = e.touches[0].clientX;
	} else if (e.type === 'mousedown') {
		startX.value = e.clientX;
	}
};

// 滑块触摸移动
const sliderTouchMove = (e) => {
	if (!isDragging.value) return;

	let moveX = 0;
	if (e.type === 'touchmove') {
		moveX = e.touches[0].clientX - startX.value;
	} else if (e.type === 'mousemove') {
		moveX = e.clientX - startX.value;
	}

	let newLeft = sliderLeft.value + moveX;

	// 限制滑块在轨道范围内
	newLeft = Math.max(0, Math.min(280 - 40, newLeft));

	sliderLeft.value = newLeft;
	if (e.type === 'touchmove') {
		startX.value = e.touches[0].clientX;
	} else if (e.type === 'mousemove') {
		startX.value = e.clientX;
	}

	// 更新拼图位置
	puzzleLeft.value = sliderLeft.value;
};

// 滑块触摸结束
const sliderTouchEnd = () => {
	console.log('滑块触摸结束');
	if (!isDragging.value) return;
	
	isDragging.value = false;

	// 检查是否匹配
	if (Math.abs(puzzleLeft.value - targetLeft.value) <= tolerance) {
		// 验证成功
		uni.showToast({
			title: '验证成功',
			icon: 'success',
			duration: 1000
		});

		// 通知父组件验证成功
		setTimeout(() => {
			emit('success');
		}, 500);
	} else {
		// 验证失败，重置
		uni.showToast({
			title: '验证失败，请重试',
			icon: 'none',
			duration: 1000
		});
		
		setTimeout(() => {
			// 完全重置验证码
			resetCaptcha();
		}, 500);
	}
};
</script>

<style lang="scss">
.slide-captcha-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
}

.slide-captcha-container {
	width: 320px;
	background-color: #ffffff;
	border-radius: 8px;
	overflow: hidden;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.slide-captcha-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px 16px;
	border-bottom: 1px solid #f0f0f0;
}

.slide-captcha-title {
	font-size: 16px;
	font-weight: 500;
	color: #333;
}

.slide-captcha-close {
	font-size: 24px;
	color: #999;
	line-height: 24px;
}

.slide-captcha-content {
	padding: 0;
}

.captcha-image-container {
	position: relative;
	width: 100%;
	height: 160px;
	overflow: hidden;
}

.background-image {
	width: 100%;
	height: 100%;
	display: block;
}

.puzzle-hole {
	position: absolute;
	width: 40px;
	height: 40px;
	background-color: rgba(0, 0, 0, 0.3);
	border-radius: 4px;
}

.puzzle-piece {
	position: absolute;
	width: 40px;
	height: 40px;
	background-color: rgba(130, 200, 255, 0.8);
	border-radius: 4px;
	box-shadow: 0 0 6px rgba(0, 0, 0, 0.3);
}

.refresh-btn {
	position: absolute;
	top: 10px;
	right: 10px;
	width: 30px;
	height: 30px;
	background-color: rgba(255, 255, 255, 0.5);
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	z-index: 10;
}

.refresh-icon {
	font-size: 20px;
	color: #666;
	font-weight: bold;
}

.slider-container {
	position: relative;
	padding: 20px 10px;
	background-color: #fff;
}

.slider-track {
	position: relative;
	height: 40px;
	background-color: #f5f5f5;
	border-radius: 20px;
}

.slider-mask {
	position: absolute;
	height: 100%;
	background-color: rgba(255, 159, 67, 0.1);
	border-radius: 20px 0 0 20px;
}

.slider-button {
	position: absolute;
	top: 0;
	width: 40px;
	height: 38px;
	background-color: #fff;
	border-radius: 2px;
	border: 1px solid #FF9F43;
	display: flex;
	justify-content: center;
	align-items: center;
}

.slider-bars {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 3px;
}

.slider-bar {
	width: 4px;
	height: 22px;
	background-color: #FF9F43;
	border-radius: 2px;
}

.slider-text {
	text-align: center;
	margin-top: 12px;
	font-size: 14px;
	color: #333;
}
</style>