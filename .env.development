# 开发环境特定配置

# 环境标识
NODE_ENV=development

# 开发服务器端口
VITE_PORT=8080

# 测试环境
# VITE_BASE_API=http://*************:9100/butler
# 测试域名环境(IP就是测试环境)
VITE_BASE_API=https://butler.joyintech.com/butler
# 研发环境
# VITE_BASE_API=http://*************:9100/butler
# 范总环境
# VITE_BASE_API=http://**************:8909/butler


# 行内环境
# VITE_BASE_API=https://winxintest.bocd.com.cn/bcbs/butler


# 是否启用代理
VITE_USE_PROXY=true

# 是否显示调试信息
VITE_APP_DEBUG=true

# 开发环境跨域配置
VITE_CORS=true

# 开发环境Mock数据设置
VITE_USE_MOCK=true

# 资源公共路径
VITE_ASSETS_BASE_URL=https://butler.joyintech.com/miniprogram
# VITE_ASSETS_BASE_URL=https://winxintest.bocd.com.cn/bcbs/static

# 开发环境特有的变量
VITE_API_BASE_URL=/