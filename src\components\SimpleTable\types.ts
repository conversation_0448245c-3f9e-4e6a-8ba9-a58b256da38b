// SimpleTable 组件类型定义

export interface Column {
  name: string
  label: string
  width?: number
  minWidth?: number
  fixed?: boolean
  align?: 'left' | 'center' | 'right'
  slot?: string
  formatter?: (value: any, row: any, column: Column) => string
  left?: number
}

export interface TableProps {
  columns: Column[]
  data: any[]
  height?: string
  loading?: boolean
  hasMore?: boolean
  highlight?: boolean
  border?: boolean
  stripe?: boolean
  rowKey?: string | ((row: any, index: number) => string | number)
  headerStyle?: Record<string, any>
  cellStyle?: Record<string, any> | ((params: {
    row: any
    column: Column
    rowIndex: number
    colIndex: number
  }) => Record<string, any>)
  noDataText?: string
  loadingText?: string
  noMoreText?: string
}

export interface TableEmits {
  rowClick: [row: any, index: number]
  cellClick: [row: any, rowIndex: number, column: Column, colIndex: number]
  loadMore: []
}

export interface TableMethods {
  scrollTo: (options: { left?: number; top?: number; animated?: boolean }) => void
  resetScroll: () => void
}

// 表格行数据类型（示例）
export interface BondRowData {
  id: string | number
  bondName: string
  bondCode: string
  issuerName: string
  issueAmount: number
  issueRate: number
  issueDate: string
  maturityDate: string
  rating: string
  [key: string]: any
} 