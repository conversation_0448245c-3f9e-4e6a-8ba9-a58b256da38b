<template>
    <view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
        <!-- 固定的头部区域 -->
        <view class="fixed-header">
            <CustomHead title="信用利差" />
        </view>

        <!-- 顶部tab切换 -->
        <view class="tab-container">
            <view class="tab-item" :class="{ active: activeTab === 'corporate' }" @click="switchTab('corporate')">中短期票据</view>
            <view class="tab-item" :class="{ active: activeTab === 'financial' }" @click="switchTab('financial')">城投债</view>
        </view>

        <!-- 可滚动的内容区域 -->
        <view class="content-wrapper">
            <scroll-view class="scrollable-content" scroll-y :show-scrollbar="false" enhanced>
                <view class="chart-card">
                    <view class="card-content">
                        <!-- 评级和期限选择 -->
                        <view class="rating-period-selector">
                            <view class="selector-item" @click="showRatingPickerDialog">
                                <text>评级:</text>
                                <view class="dropdown-wrapper">
                                    <text class="selected-value">{{ selectedRating }}</text>
                                    <text class="dropdown-arrow">▼</text>
                                </view>
                            </view>
                            <view class="divider"></view>
                            <view class="selector-item" @click="showPeriodPickerDialog">
                                <text>期限:</text>
                                <view class="dropdown-wrapper">
                                    <text class="selected-value">{{ selectedPeriod }}</text>
                                    <text class="dropdown-arrow">▼</text>
                                </view>
                            </view>
                        </view>

                        <!-- 选择弹出框 -->
                        <SelectionPopup v-model:visible="showRatingPicker" title="选择评级" :options="ratingOptions"
                            :defaultSelected="selectedRating" @confirm="handleRatingConfirm" />
                        <SelectionPopup v-model:visible="showPeriodPicker" title="发行期限" :options="periodOptions"
                            :defaultSelected="selectedPeriodCode" @confirm="handlePeriodConfirm" />

                        <!-- 利率信息展示 -->
                        <RateInfoDisplay 
                            rateLabel="最新利差"
							:rateValue="currentRate" 
							:changeValue="rateDiff" 
							:timeValue="updateTime" 
						/>

                        <!-- 时间筛选器 -->
                        <TimeFilterSelect :isShow="true" @filterChange="handleTimeFilterChange" @openCalendar="toggleModal" />

                        <!-- 图表上方当前数据点信息 -->
                        <view class="chart-data-point">
                            <view class="point-date">
                                <view class="point-indicator"></view>
                                <text class="number-font">{{ latestDataDate }}</text>
                            </view>
                            <view class="point-value">估值: <text class="number-font">{{ latestDataValue }}</text></view>
                            <view class="point-change" :class="latestDataChange > 0 ? 'up' : (latestDataChange < 0 ? 'down' : 'flat')">
                                较昨日: <text class="number-font">{{ latestDataChange > 0 ? '+' : '' }}{{ latestDataChange }}BP</text>
                            </view>
                        </view>

                        <!-- 图表展示区 -->
                        <view class="chart-component">
                            <RateChart :chartData="chartStaticData" @point-touch="handleChartPointTouch" />
                        </view>


                    </view>
                </view>

                <!-- 期限列表卡片 -->
                <PeriodListCard :bondTabType="activeTab" :bondType="bondTypeMap[activeTab]" :periodList="periodList"
                    :rating="selectedRating" :columns="[
                        { title: '期限', field: 'period' },
                        { title: '信用利差', field: 'value' },
                        { title: '涨跌BP', field: 'change' }
                    ]" />

                <!-- 底部留白区域 -->
                <view class="bottom-space"></view>
            </scroll-view>
        </view>

        <!-- 日期选择组件 -->
        <DateRangePicker v-model:visible="showModal" :defaultStartDate="startDate" :defaultEndDate="endDate"
            @dateRangeChange="handleDateRangeChange" @update:visible="handleModalVisibleChange" />
    </view>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import PeriodListCard from '@/components/MarketRate/PeriodListCard.vue';
import CustomHead from '@/components/head/head.vue';
import DateRangePicker from '@/components/calendar/DateRangePicker.vue';
import TimeFilterSelect from '@/components/common/TimeFilterSelect.vue';
import SelectionPopup from '@/components/common/SelectionPopup.vue';
import RateChart from '@/components/MarketRate/RateChart.vue';
import RateInfoDisplay from '@/components/MarketRate/RateInfoDisplay.vue';
import { getCDTerm, getCreditSpread } from '@/api/dataDict';
import { getCreditSpreadTerm, getCreditSpreadCurve } from '@/api/marketRate';
import { getAssetUrl } from '@/config/assets';

// 标签页配置
const activeTab = ref('corporate'); // 默认显示中短期票据

// 配置映射
const spreadConfig = {
    corporate: {
        curveName: '中债中短期票据收益率曲线',
        defaultRating: 'AA+',
        apiFlag: '0'
    },
    financial: {
        curveName: '中债城投债收益率曲线',
        defaultRating: 'AA+',
        apiFlag: '1'
    }
};

// 债券类型映射
const bondTypeMap = {
    corporate: '企业债',
    financial: '金融债'
};

// 评级和期限选择相关
const selectedRating = ref(spreadConfig.corporate.defaultRating);
const selectedPeriod = ref('3Y');
const selectedPeriodCode = ref('');
const showRatingPicker = ref(false);
const showPeriodPicker = ref(false);
const ratingOptions = ref([]);
const corporateRatingOptions = ref([]);
const financialRatingOptions = ref([]);
const periodOptions = ref([]);

// 图表和数据显示相关
const isShowChart = ref(true);
const periodList = ref([]);
const currentRate = ref(0);
const rateDiff = ref(0);
const updateTime = ref('--');

// 图表数据（采用市场利率页面的格式）
const chartStaticData = ref([]);

// 图表上方数据点信息
const latestDataDate = ref('--');
const latestDataValue = ref('--');
const latestDataChange = ref(0);

// 弹窗和日期相关
const showModal = ref(false);
const startDate = ref(new Date('2023-10-09'));
const endDate = ref(new Date());
const activeTimeFilter = ref('1year');
const tradeDtStart = ref("2024-04-15");
const tradeDtEnd = ref("2025-04-15");

// 曲线名称状态
const curvename = ref([]);

// 图表数据映射，用于快速查找
const dateToDataMap = ref({});

// 删除特殊点计算，使用市场利率页面的简单数组格式

// 获取默认曲线名称
const getDefaultCurvename = (tab, rating = 'AAA') => {
    if (tab === 'corporate') {
        return [`中债中短期票据收益率曲线(${rating})`];
    } else {
        return [`中债城投债收益率曲线(${rating})`];
    }
};

// 格式化日期为YYYYMMDD格式
const formatDateToYYYYMMDD = (dateStr) => {
    if (!dateStr) return '';
    return dateStr.replace(/-/g, '');
};

// 格式化交易日期 YYYYMMDD -> YYYY-MM-DD
const formatTradeDate = (dateStr) => {
    if (!dateStr || dateStr.length !== 8) return dateStr;
    
    return `${dateStr.substring(0, 4)}-${dateStr.substring(4, 6)}-${dateStr.substring(6, 8)}`;
};

// 从期限列表中获取当前期限的数据
const updateDisplayData = () => {
    // 只取期限列表中对应选择期限的数据
    const selectedPeriodData = periodList.value.find(item => item.period === selectedPeriod.value);
    
    if (selectedPeriodData) {
        // 直接赋值，不做额外处理
        currentRate.value = selectedPeriodData.value;
        rateDiff.value = selectedPeriodData.change;
        updateTime.value = selectedPeriodData.trade_dt ? formatTradeDate(selectedPeriodData.trade_dt) : '--';
    } else {
        // 找不到匹配的期限数据，显示为空或默认值
        currentRate.value = '--';
        rateDiff.value = '--';
        updateTime.value = '--';
    }
};

// 清空图表数据
const clearChartData = () => {
    chartStaticData.value = [];
    dateToDataMap.value = {};
    // 清空图表上方数据
    latestDataDate.value = '--';
    latestDataValue.value = '--';
    latestDataChange.value = 0;
};

// 更新图表数据（采用市场利率页面的格式）
const formatChartData = (data) => {
    // 检查新的数据格式
    if (!data || !Array.isArray(data) || data.length === 0) {
        clearChartData();
        return;
    }
    
    try {
        // 转换接口数据为图表数据格式（类似市场利率页面）
        chartStaticData.value = data.map(item => {
            // 从X字段获取日期并格式化 YYYYMMDD -> YYYY/MM/DD
            const dateStr = item.X || '';
            const formattedDate = `${dateStr.substring(0, 4)}/${dateStr.substring(4, 6)}/${dateStr.substring(6, 8)}`;
            
            return {
                date: formattedDate,
                value: parseFloat(item.Y) || 0,
                bpChange: item.updown || 0  // 使用bpChange字段，与市场利率页面保持一致
            };
        });
        
        // 构建日期到数据的映射，用于快速查找
        const newDateMap = {};
        data.forEach(item => {
            const dateStr = item.X || '';
            const formattedDate = `${dateStr.substring(0, 4)}/${dateStr.substring(4, 6)}/${dateStr.substring(6, 8)}`;
            if (formattedDate) {
                newDateMap[formattedDate] = item;
            }
        });
        dateToDataMap.value = newDateMap;
        
        // 更新图表上方最新数据点信息
        if (chartStaticData.value.length > 0) {
            const latestData = chartStaticData.value[chartStaticData.value.length - 1];
            latestDataDate.value = latestData.date;
            latestDataValue.value = `${latestData.value.toFixed(4)}%`;
            latestDataChange.value = latestData.bpChange;
        } else {
            latestDataDate.value = '--';
            latestDataValue.value = '--';
            latestDataChange.value = 0;
        }
    } catch (error) {
        console.error('格式化图表数据出错:', error);
        clearChartData();
    }
};

// 获取评级信息和期限
const fetchRatingTerm = async () => {
    const data = ["creditMTNRating", "creditLgfvRating", "creditTerm"];
    const res = await getCDTerm(data);
    
    // 设置期限选项
    periodOptions.value = res.data.data.creditTerm.map(item => ({
        cnname: item.cnname,
        itemcode: item.itemcode
    }));
    
    // 初始化默认期限
    if (periodOptions.value.length > 0) {
        selectedPeriod.value = periodOptions.value[0].cnname;
        selectedPeriodCode.value = periodOptions.value[0].itemcode;
    }
    
    // 缓存两种评级数据
    corporateRatingOptions.value = res.data.data.creditMTNRating.map(item => ({
        cnname: item.cnname,
        itemcode: item.itemcode
    }));
    
    financialRatingOptions.value = res.data.data.creditLgfvRating.map(item => ({
        cnname: item.cnname,
        itemcode: item.itemcode
    }));
    
    // 更新当前评级选项
    updateRatingOptions();
};

// 获取信用利差期限列表
const fetchCreditSpreadTerm = async () => {
    const data = {
        tab: activeTab.value === 'corporate' ? 'mtn' : 'lgfv'
    }
    try {
        const res = await getCreditSpreadTerm(data);
        
        // 根据当前选中的评级筛选数据
        const filteredData = res.data?.data?.filter(item => item.rating === selectedRating.value) || [];
        
        if (filteredData.length > 0) {
            periodList.value = filteredData.map(item => ({
                period: item.term,
                value: item.spread,
                change: item.updown, // 优先使用updown作为涨跌BP值
                trade_dt: item.trade_dt
            }));
            
            // 更新显示数据
            updateDisplayData();
            
            // 更新curvename值
            if (filteredData[0]?.b_anal_curvename) {
                curvename.value = [filteredData[0].b_anal_curvename];
            } else {
                // 设置默认curvename
                curvename.value = getDefaultCurvename(activeTab.value, selectedRating.value);
            }
        } else {
            periodList.value = [];
            
            // 设置默认curvename
            curvename.value = getDefaultCurvename(activeTab.value, selectedRating.value);
            
            // 清空图表数据
            clearChartData();
        }
    } catch (error) {
        periodList.value = [];
        
        // 设置默认curvename
        curvename.value = getDefaultCurvename(activeTab.value, selectedRating.value);
        
        // 清空图表数据
        clearChartData();
    }
    
    // 获取图表数据
    fetchCreditSpreadCurve();
};

// 获取信用利差曲线数据
const fetchCreditSpreadCurve = async () => {
    const data = {
        flag: spreadConfig[activeTab.value].apiFlag,
        dtStart: formatDateToYYYYMMDD(tradeDtStart.value),
        dtEnd: formatDateToYYYYMMDD(tradeDtEnd.value),
        term: [String(selectedPeriodCode.value || '0')],
        curvename: curvename.value.length > 0 ? curvename.value : [''],
        chartType: "MULTILINE",
        chartSeq: "21210e6994554794a22654e5bd34e39a",
        sysid: "PORTS-PC"
    }
    
    try {
        const res = await getCreditSpreadCurve(data);
        
        // 处理返回的数据
        if (res.data?.data && Array.isArray(res.data.data) && res.data.data.length > 0) {
            formatChartData(res.data.data);
        } else if (res.data && Array.isArray(res.data) && res.data.length > 0) {
            formatChartData(res.data);
        } else {
            // 如果没有数据，清空图表
            clearChartData();
        }
    } catch (error) {
        // 错误处理，清空图表
        clearChartData();
    }
};

// 更新评级选项列表
const updateRatingOptions = () => {
    ratingOptions.value = activeTab.value === 'corporate' ? 
        corporateRatingOptions.value : financialRatingOptions.value;
    
    // 重置为第一个评级选项或配置中的默认值
    if (ratingOptions.value.length > 0) {
        selectedRating.value = spreadConfig[activeTab.value].defaultRating || ratingOptions.value[0]?.cnname || '';
        
        // 更新曲线名称，使用当前选择的评级
        curvename.value = getDefaultCurvename(activeTab.value, selectedRating.value);
    }
};

// 显示评级选择弹出框
const showRatingPickerDialog = () => {
    showRatingPicker.value = true;
};

// 处理评级确认
const handleRatingConfirm = (rating) => {
    if (rating === selectedRating.value) return;
    
    selectedRating.value = rating;
    
    // 更新curvename对应的值
    curvename.value = getDefaultCurvename(activeTab.value, rating);
    
    fetchCreditSpreadTerm();
};

// 重置期限选择
const resetPeriodSelection = () => {
    if (periodOptions.value?.length > 0) {
        selectedPeriod.value = periodOptions.value[0].cnname;
        selectedPeriodCode.value = periodOptions.value[0].itemcode;
    } else {
        selectedPeriod.value = '3Y';
        selectedPeriodCode.value = '';
    }
};

// 显示期限选择弹出框
const showPeriodPickerDialog = () => {
    showPeriodPicker.value = true;
};

// 处理期限确认
const handlePeriodConfirm = (periodCode, periodItem) => {
    selectedPeriodCode.value = periodCode;
    selectedPeriod.value = periodItem ? periodItem.cnname : periodCode;
    
    updateDisplayData();
    fetchCreditSpreadCurve();
};

// 标签页切换
const switchTab = (tab) => {
    if (tab === activeTab.value) return;
    
    activeTab.value = tab;
    
    // 重置评级和期限
    updateRatingOptions();
    resetPeriodSelection();
    
    // 重置curvename为当前tab的默认值，使用当前选中的评级
    curvename.value = getDefaultCurvename(tab, selectedRating.value);
    
    // 更新数据
    fetchCreditSpreadTerm();
};

// 处理图表触摸事件（采用市场利率页面的方式）
const handleChartPointTouch = (pointData) => {
    // 更新图表上方的数据点信息
    latestDataDate.value = pointData.date;
    latestDataValue.value = `${pointData.value}%`;
    latestDataChange.value = pointData.bpChange === 'N/A' ? 0 : pointData.bpChange;
};

// 切换日期选择弹窗
const toggleModal = () => {
    showModal.value = !showModal.value;
    isShowChart.value = !showModal.value;
};

// 处理日期区间变更
const handleDateRangeChange = (dateRange) => {
    tradeDtStart.value = dateRange[0];
    tradeDtEnd.value = dateRange[1];
    
    // 重新获取图表数据
    fetchCreditSpreadCurve();
};

// 处理弹窗可见性变化
const handleModalVisibleChange = (visible) => {
    isShowChart.value = !visible;
};

// 处理时间筛选变更
const handleTimeFilterChange = (data) => {
    activeTimeFilter.value = data.filter;
    tradeDtStart.value = data.dateRange[0];
    tradeDtEnd.value = data.dateRange[1];
    
    // 重新获取图表数据
    fetchCreditSpreadCurve();
};

// 监听showModal的变化
watch(() => showModal.value, (newValue) => {
    isShowChart.value = !newValue;
});

// 监听期限和期限列表的变化，更新显示数据
watch([() => selectedPeriod.value, () => periodList.value], () => {
    updateDisplayData();
});

// 组件挂载时初始化数据
onMounted(() => {
    fetchRatingTerm();
    fetchCreditSpreadTerm();
});
</script>

<style lang="scss" scoped>
/* 页面布局相关样式 */
.container {
    padding: 0 20rpx;
    height: 100vh;
    box-sizing: border-box;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

.content-wrapper {
    flex: 1;
    overflow: hidden;
}

.scrollable-content {
    height: 100%;
    padding: 10rpx 0 0;

    ::-webkit-scrollbar {
        display: none;
        width: 0;
        height: 0;
        color: transparent;
    }
}

.bottom-space {
    height: 100rpx;
    width: 100%;
}

/* Tab切换样式 */
.tab-container {
    display: flex;
    margin-bottom: 20rpx;
    border-radius: 10rpx;
    margin-top: 180rpx;

    .tab-item {
        flex: 1;
        padding: 30rpx 0;
        text-align: center;
        position: relative;
        font-size: 32rpx;
        color: #999;
        transition: all 0.3s ease;
        font-weight: bold;

        &.active {
            color: #FF9900;
            font-weight: bold;

            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 40%;
                height: 6rpx;
                background-color: #FF9900;
                border-radius: 6rpx 6rpx 0 0;
            }
        }
    }
}

/* 图表卡片样式 */
.chart-card {
    background-color: white;
    border-radius: 20rpx;
    overflow: hidden;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-content {
    padding: 30rpx;
}

.chart-component {
    background-color: white;
    border-radius: 16rpx;
    overflow: hidden;
    margin-top: 20rpx;
}

/* 图表数据点信息样式 */
.chart-data-point {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    background-color: #F5F7FF;
    padding: 15rpx 20rpx;
    border: 4rpx solid #fafafa;
    box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(172, 172, 172, 0.2);
    border-radius: 10rpx;
    font-size: 22rpx;
}

.point-date {
    display: flex;
    align-items: center;
}

.point-indicator {
    width: 14rpx;
    height: 14rpx;
    border-radius: 50%;
    background-color: #6F7CD1;
    margin-right: 10rpx;
}

.point-value {
    color: #333;
}

.point-change {
    &.up {
        color: #E76056;
    }

    &.down {
        color: #52C41A;
    }
    
    &.flat {
        color: #FEB249;
    }
}

/* 评级期限选择器样式 */
.rating-period-selector {
    display: flex;
    justify-content: center;
    margin-bottom: 20rpx;
    align-items: center;
}

.selector-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;

    text {
        color: #666;
        font-size: 28rpx;
        margin-right: 10rpx;
    }
}

.divider {
    width: 2rpx;
    height: 36rpx;
    background-color: #DDDDDD;
    margin: 0 30rpx;
}

.dropdown-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    text:nth-child(2) {
        font-size: 20rpx !important;
    }
}

.selected-value {
    color: #333;
    font-weight: bold;
}

.dropdown-arrow {
    font-size: 20rpx;
    color: #999;
    margin-left: 10rpx;
}


</style>