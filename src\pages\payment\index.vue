<template>
	<view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
		<!-- 固定的头部区域 -->
		<view class="fixed-header">
			<CustomHead title="付息兑税" :showBack="false" />
		</view>



		<view class="content-wrapper">
			<scroll-view class="scrollable-content" scroll-y :show-scrollbar="false" enhanced>
				<!-- 搜索框 -->
				<view class="search-container">
					<view class="search-box">
						<view class="search-input">
							<uni-icons type="search" size="18" color="#999"></uni-icons>
							<input type="text" placeholder="搜索" confirm-type="search" v-model="searchKeyword" placeholder-class="placeholder" @tap="handleSearch" />
						</view>
						<text class="cancel-btn" @tap="clearSearch">取消</text>
					</view>
				</view>

				<!-- 未来偿债现金流卡片 -->
				<view class="card future-payment">
					<view class="card-header">
						<view class="card-title">
							<view class="title-icon"></view>
							<text class="title-text">未来偿债现金流</text>
						</view>
					</view>
					<view class="card-content">
						<!-- 行权计/到期计切换 -->
						<view class="toggle-container">
							<view class="toggle-options">
								<view class="toggle-item" :class="{ 'active-toggle': durationType === 'exercise', 'inactive-toggle': durationType !== 'exercise' }" @tap="switchDurationType('exercise')">
									以行权计
								</view>
								<view class="toggle-item" :class="{ 'active-toggle': durationType === 'maturity', 'inactive-toggle': durationType !== 'maturity' }" @tap="switchDurationType('maturity')">
									以到期计
								</view>
							</view>
						</view>
						
						<!-- 未来偿债现金流内容 -->
						<view class="cash-flow-content">
							<bar-chart 
								:dataSet="cashFlowData" 
								height="480rpx"
								barColor="#FFC069"
								unit="亿"
							/>
						</view>
					</view>
				</view>

				<!-- 现金流明细卡片 -->
				<view class="card cash-flow-details">
					<view class="card-header">
						<view class="card-title">
							<view class="title-icon"></view>
							<text class="title-text">现金流明细</text>
						</view>
						<text class="header-date">{{ currentDate }}</text>
					</view>
					<view class="card-content">
						<!-- 使用zb-table组件显示现金流明细数据 -->
						<view class="table-container">
							<zb-table 
								ref="tableRef"
								:columns="tableColumns" 
								:stripe="false" 
								:data="cashFlowDetailList" 
								:border="false"
								:highlight="true"
								:cell-header-style="headerCellStyle"
								:isShowLoadMore="true"
								@pullUpLoading="pullUpLoading">
							</zb-table>
						</view>
					</view>
				</view>

				<!-- 底部留白区域，确保内容可以完全滚动显示 -->
				<view class="bottom-space"></view>
			</scroll-view>
		</view>
		<!-- 自定义tabBar -->
		<AppTabBar :selectNumber="3" :permissionData="getTabBarPermissions" />
	</view>
</template>

<script setup lang="ts">
import CustomHead from '@/components/head/head.vue';
import { ref, onMounted, computed } from 'vue';
import BarChart from '@/components/charts/BarChart.vue';
import zbTable from '@/uni_modules/zb-table/components/zb-table/zb-table.vue';
import AppTabBar from '@/components/AppTabBar/index.vue';
import { onShow } from '@dcloudio/uni-app';
import { usePermissionStore } from '@/stores/permission';
import { storeToRefs } from 'pinia';
import { getAssetUrl } from '@/config/assets';

// 使用权限 store
const permissionStore = usePermissionStore();
const { getTabBarPermissions } = storeToRefs(permissionStore);

// 搜索关键字
const searchKeyword = ref('');

// 久期类型：行权计/到期计
const durationType = ref('exercise'); // 默认为行权计

// 未来偿债现金流数据 - 行权计
const exerciseCashFlowData = ref([
	{ year: '2025', value: 173.5933 },
	{ year: '2026', value: 100.5933 },
	{ year: '2027', value: 111.2829 },
	{ year: '2028', value: 28.3389 },
	{ year: '2029', value: 96.7065 }
]);

// 未来偿债现金流数据 - 到期计
const maturityCashFlowData = ref([
	{ year: '2025', value: 150.4321 },
	{ year: '2026', value: 88.7654 },
	{ year: '2027', value: 120.9876 },
	{ year: '2028', value: 36.5432 },
	{ year: '2029', value: 105.8765 }
]);

// 计算当前应该显示的现金流数据
const cashFlowData = computed(() => {
	return durationType.value === 'exercise' ? exerciseCashFlowData.value : maturityCashFlowData.value;
});

// 静态日期区间
const currentDate = '2025/03-2029/12';



// 切换久期类型
const switchDurationType = (type) => {
	durationType.value = type;
};

// 处理搜索
const handleSearch = () => {
	console.log('搜索关键字:', searchKeyword.value);
    uni.navigateTo({
        url: '/subPageB/search-page/index'
    });
	// 实现搜索逻辑
};

// 清除搜索
const clearSearch = () => {
	searchKeyword.value = '';
	// 重置搜索结果
};

// 表格样式
const headerCellStyle = () => {
	return {
		fontSize: '28rpx',
		color: '#666',
		backgroundColor: '#f5f5f5',
		fontWeight: 'bold',
		padding: '24rpx 10rpx'
	};
};

// 表格列定义
const tableColumns = ref([
	{
		name: 'bondName',
		label: '债券简称',
		width: 120,
		fixed: true,
		align: 'left',
		emptyString: '--'
	},
	{
		name: 'paymentDate',
		label: '付息日期',
		width: 120,
		align: 'center',
		emptyString: '--'
	},
	{
		name: 'paymentAmount',
		label: '付息金额(元)',
		width: 120,
		align: 'right',
		emptyString: '--'
	},
	{
		name: 'status',
		label: '状态',
		width: 80,
		align: 'center',
		emptyString: '--'
	}
]);

// 现金流明细数据
const cashFlowDetailList = ref([
	{
		bondName: '20某某债01',
		paymentDate: '2025-06-15',
		paymentAmount: '1,850,000.00',
		status: '未付息'
	},
	{
		bondName: '21某企MTN002',
		paymentDate: '2025-07-20',
		paymentAmount: '2,700,000.00',
		status: '未付息'
	},
	{
		bondName: '22某科技SCP001',
		paymentDate: '2025-09-05',
		paymentAmount: '930,000.00',
		status: '未付息'
	},
	{
		bondName: '21某地产MTN001',
		paymentDate: '2025-10-15',
		paymentAmount: '3,600,000.00',
		status: '未付息'
	},
	{
		bondName: '22某集团PPN001',
		paymentDate: '2025-12-20',
		paymentAmount: '1,250,000.00',
		status: '未付息'
	}
]);

// 上拉加载更多
const pullUpLoading = (done) => {
	// 模拟加载更多数据
	setTimeout(() => {
		// 假设没有更多数据了
		done('ok');
	}, 1000);
};

// 页面显示时获取权限数据
onShow(() => {
    console.log('TabBar权限列表:', getTabBarPermissions.value);
});

onMounted(() => {
	// 页面加载完成后的逻辑
});
</script>

<style lang="scss" scoped>
/* 1.1 页面容器 */
.container {
	padding: 0 20rpx;
	height: 100vh;
	box-sizing: border-box;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;
	/* 防止整体页面滚动 */
}

/* 1.2 头部区域 */
.fixed-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 100;
}



/* 1.4 内容区域 */
.content-wrapper {
	flex: 1;
	margin-top: 180rpx;
	/* 给固定头部留出空间 */
	overflow: auto;
	/* 主滚动容器 */
	position: relative;
}

/* 1.5 滚动区域 */
.scrollable-content {
	height: calc(100% - 130rpx);

	::-webkit-scrollbar {
		display: none;
		width: 0;
		height: 0;
		color: transparent;
	}
}

/* 1.6 搜索框 */
.search-container {
	padding: 20rpx 0;
	width: 100%;
}

.search-box {
	display: flex;
	align-items: center;
	padding: 0;
	flex-shrink: 0;
}

.search-input {
	flex: 1;
	height: 72rpx;
	background: rgba(255, 255, 255, 0.5);
	border-radius: 36rpx;
	display: flex;
	align-items: center;
	padding: 0 24rpx;
	backdrop-filter: blur(10rpx);
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);

	uni-icons {
		margin-right: 10rpx;
	}

	input {
		flex: 1;
		height: 100%;
		font-size: 28rpx;
	}

	.placeholder {
		color: #999999;
	}
}

.cancel-btn {
	padding: 0 20rpx;
	font-size: 28rpx;
	color: #ff9500;
}

/* 1.7 卡片通用样式 */
.card {
	background-color: #fff;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
}

.card-title {
	display: flex;
	align-items: center;

	.title-icon {
		width: 48rpx;
		height: 52rpx;
		position: relative;
		top: -10rpx;

		&::before {
			content: '';
			position: absolute;
			inset: 0;
			background: linear-gradient(135deg, rgba(133, 111, 254, 0.6) 0%, rgba(255, 237, 221, 0) 100%);
			clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
		}
	}

	.title-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		transform: translateX(-25rpx);
	}
}

.card-content {
	padding: 0 30rpx 30rpx;
}

/* 1.8 底部留白 */
.bottom-space {
	height: 40rpx;
}

/* 未来偿债现金流卡片特有样式 */
.toggle-container {
	display: flex;
	justify-content: center;
}

.toggle-options {
	display: flex;
	justify-content: center;
	gap: 20rpx;
}

.toggle-item {
	padding: 15rpx 40rpx;
	font-size: 28rpx;
	border-radius: 40rpx;
	transition: all 0.3s ease;
	font-weight: 500;
}

.active-toggle {
	background-color: #FFA940;
	color: #fff;
	border: none;
}

.inactive-toggle {
	background-color: #f5f5f5;
	color: #666;
	border: none;
}

/* 图表容器样式 */
.cash-flow-content {
	padding: 20rpx 0;
	margin-top: 10rpx;
	height: 460rpx;
}

/* 表格样式 */
.table-container {
	width: 100%;
	overflow-x: hidden;
}

/* 新增日期样式 */
.header-date {
	font-size: 28rpx;
	color: #000000;
}
</style>