<template>
	<view class="login-container" :style="{ backgroundImage: `url('${getAssetUrl('/launch/sgin_bg.png')}')` }">
		<!-- 主体内容 -->
		<view class="content">
			<!-- 银行标志 -->
			<view class="bank-logo">
				<image class="bank-logo-img" :src="getAssetUrl('/launch/sgin_logo.svg')" mode=""></image>
				<!-- <text class="bank-name">成都银行 | 投行·智管家</text> -->
			</view>

			<!-- 表单区域 -->
			<view class="form-area">
				<!-- 手机号输入 -->
				<view class="form-item phone-input">
					<input 
						type="number" 
						placeholder="请输入您的手机号" 
						placeholder-class="placeholder-style" 
						v-model="phone" 
						maxlength="11"
						@blur="validatePhone"
					/>
				</view>
				<text v-if="phoneError" class="error-tip">{{ phoneError }}</text>

				<!-- 验证码输入 -->
				<view class="form-item code-input">
					<input 
						type="number" 
						placeholder="请输入短信验证码" 
						placeholder-class="placeholder-style" 
						v-model="code" 
						maxlength="6"
					/>
					<view 
						class="get-code-btn" 
						:class="{ disabled: countdown > 0 || !isValidPhone }" 
						@tap="showCaptcha"
					>
						{{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
					</view>
				</view>

				<!-- 登录按钮 -->
				<button class="login-btn" @tap="handleLogin">立即登录</button>
			</view>
		</view>
		
		<!-- 滑块验证组件 -->
		<SlideCaptcha 
			:visible="captchaVisible" 
			@close="captchaVisible = false"
			@success="onCaptchaSuccess"
		/>
	</view>
</template>

<script setup>
import { ref, computed } from 'vue';
import SlideCaptcha from '@/components/SlideCaptcha/SlideCaptcha.vue';
import { getAssetUrl } from '@/config/assets';

// 响应式数据
const phone = ref('18923873678');
const code = ref('123123');
const phoneError = ref('');
const countdown = ref(0);
const captchaVisible = ref(false);
let timer = null;

// 计算属性：判断手机号是否有效
const isValidPhone = computed(() => {
	return /^1[3-9]\d{9}$/.test(phone.value);
});

// 验证手机号
const validatePhone = () => {
	if (!phone.value) {
		phoneError.value = '请输入手机号';
		return false;
	}
	
	if (!isValidPhone.value) {
		phoneError.value = '请输入正确的手机号码';
		return false;
	}
	
	phoneError.value = '';
	return true;
};

// 显示滑块验证
const showCaptcha = () => {
	// 如果正在倒计时或手机号无效，则不处理
	if (countdown.value > 0 || !isValidPhone.value) {
		console.log('电话号码无效或倒计时中');
		return;
	}
	
	if (!validatePhone()) {
		console.log('电话号码验证失败');
		return;
	}
	
	console.log('显示验证码弹窗');
	// 设置状态
	captchaVisible.value = true;
	console.log('captchaVisible:', captchaVisible.value);
};

// 滑块验证成功回调
const onCaptchaSuccess = () => {
	// 关闭验证弹窗
	captchaVisible.value = false;
	
	// 发送验证码
	sendVerificationCode();
};

// 开始倒计时
const startCountdown = () => {
	countdown.value = 60;
	timer = setInterval(() => {
		countdown.value--;
		if (countdown.value <= 0) {
			clearInterval(timer);
		}
	}, 1000);
};

// 发送验证码
const sendVerificationCode = () => {
	// 这里添加获取验证码的逻辑
	uni.showToast({
		title: '验证码已发送',
		icon: 'none'
	});
	
	// 开始倒计时
	startCountdown();
};

// 登录处理
const handleLogin = () => {
	if (!validatePhone()) {
		return;
	}
	
	if (!code.value) {
		uni.showToast({
			title: '请输入验证码',
			icon: 'none'
		});
		return;
	}
	
	if (code.value.length !== 6) {
		uni.showToast({
			title: '验证码应为6位数字',
			icon: 'none'
		});
		return;
	}
	
	// 这里添加登录逻辑
	uni.showToast({
		title: '登录成功',
		icon: 'success'
	});
	uni.reLaunch({ url: '/pages/home/<USER>' });
};
</script>

<style lang="scss">
.login-container {
	position: relative;
	height: 100vh;
	display: flex;
	flex-direction: column;
	justify-content: center;
	background-size: cover;
	background-repeat: no-repeat;
	// 主体内容区域
	.content {
		padding: 40px 30px;
		
		// 银行标志
		.bank-logo {
			width: 100%;
			.bank-logo-img {
				display: block;
				width: 90%;
				height: 75rpx;
				margin: 0 auto;
			}
		}
		
		// 表单区域
		.form-area {
			margin-top: 40px;
			
			// 表单项通用样式
			.form-item {
				background-color: #FFFFFF;
				border-radius: 5px;
				padding: 12px;
				margin-bottom: 20px;
				box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
				
				input {
					width: 100%;
					height: 24px;
					font-size: 16px;
				}
			}
			
			// 错误提示
			.error-tip {
				font-size: 12px;
				color: #ff4d4f;
				margin: -15px 0 15px 5px;
				display: block;
			}
			
			// 验证码输入框
			.code-input {
				display: flex;
				justify-content: space-between;
				align-items: center;
				
				input {
					flex: 1;
				}
				
				.get-code-btn {
					color: rgba(255, 142, 43, 1);
					font-size: 14px;
					font-weight: 500;
					padding-left: 15px;
					white-space: nowrap;
					
					&.disabled {
						color: rgba(255, 142, 43, 1);
						pointer-events: none;
					}
				}
			}
			
			// 登录按钮
			.login-btn {
				width: 100%;
				height: 50px;
				background: linear-gradient(135deg, #FFD280 0%, #FF9F43 100%);
				color: #FFFFFF;
				text-align: center;
				line-height: 50px;
				border-radius: 5px;
				margin-top: 30px;
				font-size: 16px;
				font-weight: 500;
				border: none;
				box-shadow: 0 4px 10px rgba(255, 159, 67, 0.2);
				
				&:active {
					opacity: 0.9;
				}
			}
		}
	}
}

// 占位符样式
.placeholder-style {
	color: #BBBBBB;
	font-size: 16px;
}
</style>
