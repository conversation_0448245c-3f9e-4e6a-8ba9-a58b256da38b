# Pinia权限管理系统使用说明

## 概述

本项目使用Pinia状态管理来替代localStorage存储权限信息，提供了更好的响应式体验和类型安全。

## Store结构

### 1. Permission Store (`permission.ts`)
负责管理用户权限相关的数据：
- `token`: 用户登录令牌
- `mpUserInfo`: 小程序用户信息
- `tabBarPermissions`: TabBar权限列表
- `homePagePermissions`: 首页权限列表
- `sysVersion`: 系统版本

### 2. User Store (`user.ts`)
负责管理用户登录状态：
- `isLoggedIn`: 登录状态
- `login()`: 登录方法
- `logout()`: 登出方法

## 使用方法

### 1. 在登录页面使用

```vue
<script setup>
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 登录
const handleLogin = async () => {
  const success = await userStore.login(token)
  if (success) {
    // 登录成功，跳转到首页
    uni.switchTab({ url: '/pages/home/<USER>' })
  }
}
</script>
```

### 2. 在其他页面获取权限信息

```vue
<script setup>
import { computed } from 'vue'
import { usePermissionStore } from '@/stores/permission'

const permissionStore = usePermissionStore()

// 响应式获取权限数据
const userInfo = computed(() => permissionStore.getUserInfo)
const tabBarPermissions = computed(() => permissionStore.getTabBarPermissions)
const homePagePermissions = computed(() => permissionStore.getHomePagePermissions)
const sysVersion = computed(() => permissionStore.getSysVersion)
</script>
```

### 3. 权限检查

```javascript
// 检查是否有特定权限
const hasPermission = (permissionName) => {
  const allPermissions = [...tabBarPermissions.value, ...homePagePermissions.value]
  return allPermissions.some(permission => permission.name === permissionName)
}

// 使用示例
if (hasPermission('首页')) {
  // 有首页权限，显示相关内容
}
```

### 4. 退出登录

```vue
<script setup>
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const logout = () => {
  userStore.logout()
  // 清除所有状态，跳转到登录页
  uni.reLaunch({ url: '/pages/sign_in/sign_in' })
}
</script>
```

## 数据持久化

所有store都启用了数据持久化（`persist: true`），数据会自动保存到本地存储中，应用重启后会自动恢复。

## 迁移说明

### 原有的localStorage调用替换：

```javascript
// 原来的方式
uni.setStorageSync('token', token)
uni.getStorageSync('token')

// 新的方式
const permissionStore = usePermissionStore()
permissionStore.setToken(token)
permissionStore.getToken
```

```javascript
// 原来的方式
uni.setStorageSync('mpUserInfo', userInfo)
uni.getStorageSync('mpUserInfo')

// 新的方式
const permissionStore = usePermissionStore()
permissionStore.setUserInfo(userInfo)
permissionStore.getUserInfo
```

## 优势

1. **响应式**: 数据变化会自动更新UI
2. **类型安全**: TypeScript支持，减少运行时错误
3. **集中管理**: 所有权限相关逻辑集中在store中
4. **易于维护**: 清晰的数据结构和方法
5. **自动持久化**: 无需手动管理localStorage

## 注意事项

1. 在使用store之前，确保已经在`main.ts`中注册了Pinia
2. 所有异步操作都应该有错误处理
3. 在组件中使用computed来获取响应式数据
4. 退出登录时记得调用`clearState()`清除所有状态 