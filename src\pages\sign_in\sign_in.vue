<!-- pages/launch/launch.vue -->
<template>
    <view class="launch-container"
        :style="{ backgroundImage: `url('${getAssetUrl('/launch/sgin_bg.png')}')` }">
        <view class="login-content">
            <image class="logo" :src="getAssetUrl('/launch/sgin_logo.svg')" mode="widthFix">
            </image>
            <!-- <button class="btn one-click-btn" open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber">一键登录</button> -->
            <button class="btn one-click-btn" @click="handleOneClickLogin">一键登录</button>
            <button class="btn mobile-login-btn" @click="handleMobileLogin">手机号快捷登录</button>

            <!-- 手机号免密登录区域 -->
            <!-- <view class="phone-login-container">
                <input class="phone-input" type="text" v-model="phoneNumber" placeholder="请输入账号" />
                <button class="phone-login-btn" @click="handlePhoneLogin">登录-测试使用</button>
            </view> -->
            <!-- 测试专用手机号密码登录区域 -->
            <view class="password-login-container">
                <view class="phone-input-wrapper">
                    <input class="password-input" type="number" maxlength="11" v-model="testPhoneNumber" placeholder="请输入手机号" />
                    <view v-if="testPhoneNumber" class="clear-btn" @click="clearPhoneNumber">
                        <text class="clear-icon">✕</text>
                    </view>
                </view>
                <input class="password-input" type="number" v-model="testPassword" maxlength="6" disabled placeholder="请输入密码" />
                <button class="password-login-btn" @click="handlePasswordLogin">仅内网测试专用 - 登录</button>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref } from "vue";
import { onLoad, onUnload } from "@dcloudio/uni-app";
import { getFreeLogin, getPcLogin } from '@/api/common';
import { useUserStore } from '@/stores/user';
import { getAssetUrl } from '@/config/assets';

// ==================== 数据定义 ====================
// 手机号免密登录账号数据
const phoneNumber = ref('13811111111');

// 手机号密码登录数据
const testPhoneNumber = ref('13811111111');
const testPassword = ref('111111');

// 用户store
const userStore = useUserStore();

// ==================== 一键登录功能 ====================
const handleOneClickLogin = async () => {
    uni.showLoading({
        title: '登录中'
    });

    const freeLoginParams = {
        userid: "13811111111",
        password: "lk1f5RJ//1HGNBa3GwPiIg==",//域名环境
        // password: "554XyoJ6IIjNL4HUmMJ1QQ==",//研发环境
    }

    const res = await getPcLogin(freeLoginParams);

    if (res.data.code == '000') {
        // 使用用户store进行登录
        const success = await userStore.login(res.data.data.token);
        if (!success) {
            uni.hideLoading();
            uni.showToast({
                title: '获取用户信息失败',
                icon: 'none',
                duration: 2000
            });
            return;
        }

        uni.hideLoading();

        uni.switchTab({
            url: '/pages/home/<USER>'
        });
        // uni.navigateTo({
        //     url: '/subPageB/valuation-market/valuation-market'
        // });
        // uni.navigateTo({
        //     url: '/subPageA/credit-spread/credit-spread'
        // });
        // uni.navigateTo({
        //      url: '/subPageA/market-rate/market-rate'
        // });
        // uni.navigateTo({
        //      url: '/subPageA/policy-rate/policy-rate'
        // });
        // uni.navigateTo({
        //      url: '/subPageA/bond-yield/bond-yield'
        // });
        // uni.navigateTo({
        //     url: '/subPageA/benchmark-analysis/benchmark-analysis'
        // });
    }
}

// ==================== 手机号快捷登录功能 ====================
const handleMobileLogin = () => {
    uni.showToast({
        title: '手机号快捷登录暂时不可用',
        icon: 'none',
        duration: 3000
    });
    // 手机号登录逻辑
    // uni.navigateTo({
    //     url: '/pages/mobile_login/mobile_login'
    // });
    // console.log('手机号登录点击');
}

// 微信授权获取手机号（暂未启用）
const onGetPhoneNumber = (e) => {
    // console.log('一键登录点击', e);
}

// ==================== 手机号免密登录功能 ====================
const handlePhoneLogin = async () => {
    // 验证输入
    if (!phoneNumber.value) {
        uni.showToast({
            title: '请输入账号',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    uni.showLoading({
        title: '登录中'
    });

    try {
        // 免密登录逻辑
        const phoneLoginParams = {
            userid: phoneNumber.value,
        }

        const res = await getFreeLogin(phoneLoginParams);

        if (res.data.code == '000') {
            // 使用用户store进行登录
            const success = await userStore.login(res.data.data.token);
            if (!success) {
                uni.hideLoading();
                uni.showToast({
                    title: '获取用户信息失败',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }

            uni.hideLoading();
            // uni.switchTab({
            //     url: '/pages/home/<USER>'
            // });
            uni.navigateTo({
                url: '/subPageA/benchmark-analysis/benchmark-analysis'
            });
            // uni.switchTab({
            //     url: '/pages/issuance/index'
            // });
        } else {
            uni.hideLoading();
            uni.showToast({
                title: res.data.message || '登录失败',
                icon: 'none',
                duration: 2000
            });
        }
    } catch (error) {
        uni.hideLoading();
        uni.showToast({
            title: '登录失败，请重试',
            icon: 'none',
            duration: 2000
        });
        console.error('手机号免密登录错误：', error);
    }
};

// ==================== 手机号密码登录功能 ====================
const handlePasswordLogin = async () => {
    // 验证输入
    if (!testPhoneNumber.value) {
        uni.showToast({
            title: '请输入手机号',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    if (!testPassword.value) {
        uni.showToast({
            title: '请输入密码',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    uni.showLoading({
        title: '登录中'
    });

    try {
        // 手机号密码登录逻辑
        const passwordLoginParams = {
            userid: testPhoneNumber.value,
            // password: testPassword.value,
            password: "554XyoJ6IIjNL4HUmMJ1QQ=="
        }

        const res = await getPcLogin(passwordLoginParams);

        if (res.data.code == '000') {
            // 使用用户store进行登录
            const success = await userStore.login(res.data.data.token);
            if (!success) {
                uni.hideLoading();
                uni.showToast({
                    title: '获取用户信息失败',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }

            uni.hideLoading();
            uni.switchTab({
                url: '/pages/home/<USER>'
            });
        } else {
            uni.hideLoading();
            uni.showToast({
                title: res.data.message || '登录失败',
                icon: 'none',
                duration: 2000
            });
        }
    } catch (error) {
        uni.hideLoading();
        uni.showToast({
            title: '登录失败，请重试',
            icon: 'none',
            duration: 2000
        });
        console.error('手机号密码登录错误：', error);
    }
};

// ==================== 页面生命周期 ====================
// 页面离开时清除loading状态
onUnload(() => {
    uni.hideLoading();
});

// ==================== 清除手机号功能 ====================
const clearPhoneNumber = () => {
    testPhoneNumber.value = '';
};
</script>

<style lang="scss" scoped>
.launch-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-size: cover;
    background-repeat: no-repeat;
}

.login-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 85%;
    // max-width: 600rpx;
}

.logo {
    width: 90%;
    margin-bottom: 200rpx;
}

.btn {
    width: 100%;
    height: 90rpx;
    border-radius: 10rpx;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30rpx;
}

.one-click-btn {
    background: linear-gradient(to right, #F9C171, #F6944C);
    color: #FFFFFF;
    border: none;
}

.mobile-login-btn {
    background-color: #FFFFFF;
    color: #333333;
    border: 1px solid #EEEEEE;
}

.phone-login-container {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 30rpx;
    gap: 20rpx;
}

.phone-input {
    flex: 1;
    height: 90rpx;
    border: 1px solid #EEEEEE;
    border-radius: 10rpx;
    padding: 0 20rpx;
    font-size: 32rpx;
    background-color: #FFFFFF;
    color: #878585;
}

.phone-login-btn {
    width: 260rpx;
    height: 90rpx;
    border-radius: 10rpx;
    line-height: 90rpx;
    font-size: 28rpx;
    background: linear-gradient(to right, #F9C171, #F6944C);
    color: #FFFFFF;
    border: none;
}

.password-login-container {
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 30rpx;
    background: rgba(203, 201, 201, 30%);
    border-radius: 20rpx;
    width: 100%;
    margin-bottom: 30rpx;
    gap: 20rpx;
}

.phone-input-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    position: relative;
    margin-bottom: 0;
}

.password-input {
    flex: 1;
    width: -webkit-fill-available;
    height: 90rpx;
    border: 1px solid #EEEEEE;
    border-radius: 10rpx;
    padding: 0 80rpx 0 20rpx;
    font-size: 32rpx;
    background-color: #FFFFFF;
    color: #878585;
}

.clear-btn {
    position: absolute;
    right: 20rpx;
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 30%);
    border-radius: 50%;
    cursor: pointer;
}

.clear-icon {
    font-size: 24rpx;
    color: #FFFFFF;
    line-height: 1;
}

.password-login-btn {
    width: 100%;
    height: 90rpx;
    border-radius: 10rpx;
    line-height: 90rpx;
    font-size: 28rpx;
    background: linear-gradient(to right, #F9C171, #F6944C);
    color: #FFFFFF;
    border: none;
}
</style>
