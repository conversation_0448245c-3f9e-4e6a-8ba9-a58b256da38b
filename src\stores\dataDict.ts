import { defineStore } from 'pinia'

import { getDataDict } from '@/api/dataDict'

interface Result<T = any> {
  code: number | string
  result: T
  message: string
  status: string | number
}

export const useDataDictStore = defineStore({
  id: 'dataDict',
  state: () => ({
    commonList: []
  }),
  actions: {
    async getCommonList(obj: any) {
      if (!obj && this.commonList.length) return
      try {
        const res = await getDataDict({
          page_size: 1000,
          page: 1,
          ...obj
        })
        if (res.code == 200) {
          this.commonList = res.data
          // this.commonList = res.data.list.map((item) => {
          //   return {
          //     ...item,
          //     label: item.name,
          //     value: item.id,
          //   }
          // })
        }
      } catch (error: any) {
        console.log('error getCommonList', error.message)
      }
    }
  },
  persist: true // 数据持久化
})
