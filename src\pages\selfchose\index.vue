<template>
	<view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
		<!-- 固定的头部区域 -->
		<view class="fixed-header">
			<CustomHead title="自选债券" :showBack="false" />
		</view>
		<view class="content-wrapper">
			<scroll-view class="scrollable-content" scroll-y :show-scrollbar="false" enhanced @scrolltolower="loadMore">
				<!-- 搜索框 -->
				<view class="search-box">
					<view class="search-input">
						<uni-icons type="search" size="18" color="#999"></uni-icons>
						<input type="text" placeholder="债券简称/债券代码/发行人" placeholder-class="placeholder" />
					</view>
					<text class="cancel-btn" @tap="clearSearch">取消</text>
				</view>

				<!-- 卡片内容区 -->
				<view class="card-container">
					<!-- 卡片头部 -->
					<view class="card-header">
						<view class="card-title">
							<view class="title-icon"></view>
							<text class="title-text">债券列表</text>
						</view>
						<view class="more-link" @tap="goToSelfchoseManagement">
                            <uni-icons type="plus" size="20" color="#ff9500"></uni-icons>
							<text class="more-text">添加自选</text>
						</view>
					</view>
					
					<!-- 债券列表 - 使用zb-table组件 -->
					<view class="bonds-list">
						<zb-table :columns="tableColumns" :stripe="false" :data="bondsList" :border="false"
							:highlight="true" @rowClick="showBondDetail" :cell-style="cellStyle"
							:cell-header-style="headerCellStyle"></zb-table>
					</view>

					<!-- 底部加载更多提示 -->
					<view class="loading-more" v-if="isLoading">
						<uni-icons type="spinner-cycle" size="20" color="#999"></uni-icons>
						<text class="loading-text">加载中...</text>
					</view>

					<!-- 没有更多数据提示 -->
					<view class="no-more" v-if="!hasMore && !isLoading">
						<text class="no-more-text">没有更多数据了</text>
					</view>
				</view>
			</scroll-view>
		</view>
		<!-- 自定义tabBar -->
		<AppTabBar :selectNumber="2" :permissionData="getTabBarPermissions" />
	</view>
</template>

<script setup lang="ts">
import CustomHead from '@/components/head/head.vue';
import { ref, onMounted } from 'vue';
// 导入zb-table组件
import zbTable from '@/uni_modules/zb-table/components/zb-table/zb-table.vue';
import AppTabBar from '@/components/AppTabBar/index.vue';
import { onShow } from '@dcloudio/uni-app';
import { usePermissionStore } from '@/stores/permission';
import { storeToRefs } from 'pinia';
import { getAssetUrl } from '@/config/assets';
// 搜索关键词
const searchKeyword = ref('');

// 表格列定义
const tableColumns = ref([
	{
		name: 'name',
		label: '债券简称',
		fixed: true,
		width: 100,
		emptyString: '--',
		// 使用cell-style实现文本截断
		cellStyle: {
			overflow: 'hidden',
			textOverflow: 'ellipsis',
			whiteSpace: 'nowrap',
			maxWidth: '100px'
		}
	},
	{ name: 'code', label: '债券类型', width: 120 },
	{ name: 'rating', label: '中债隐含评级', width: 120 },
	{ name: 'issueDate', label: '发行日期', width: 120 },
	{ name: 'maturityDate', label: '到期日期', width: 120 },
	{ name: 'issueAmount', label: '发行金额', width: 120 }
]);

// 债券列表数据
const bondsList = ref([
	{
		name: '成都市城市资产运营有限公司债券',
		code: 'CDS2587',
		rating: 'AAA',
		issueDate: '2023-10-15',
		maturityDate: '2026-10-15',
		issueAmount: '5亿元'
	},
	{
		name: '成都市城市资产运营有限公司债券',
		code: 'CDS2587',
		rating: 'AAA',
		issueDate: '2023-10-15',
		maturityDate: '2026-10-15',
		issueAmount: '5亿元'
	},
	{
		name: '成都市城市资产运营有限公司债券',
		code: 'CDS2587',
		rating: 'AAA',
		issueDate: '2023-10-15',
		maturityDate: '2026-10-15',
		issueAmount: '5亿元'
	},
	{
		name: '成都市城市资产运营有限公司债券',
		code: 'CDS2587',
		rating: 'AAA',
		issueDate: '2023-10-15',
		maturityDate: '2026-10-15',
		issueAmount: '5亿元'
	},
	{
		name: '成都市城市资产运营有限公司债券',
		code: 'CDS2587',
		rating: 'AAA',
		issueDate: '2023-10-15',
		maturityDate: '2026-10-15',
		issueAmount: '5亿元'
	},
	{
		name: '成都市城市资产运营有限公司债券',
		code: 'CDS2587',
		rating: 'AAA',
		issueDate: '2023-10-15',
		maturityDate: '2026-10-15',
		issueAmount: '5亿元'
	},
	{
		name: '成都市城市资产运营有限公司债券',
		code: 'CDS2587',
		rating: 'AAA',
		issueDate: '2023-10-15',
		maturityDate: '2026-10-15',
		issueAmount: '5亿元'
	},
	{
		name: '成都市城市资产运营有限公司债券',
		code: 'CDS2587',
		rating: 'AAA',
		issueDate: '2023-10-15',
		maturityDate: '2026-10-15',
		issueAmount: '5亿元'
	},
	{
		name: '成都高新技术产业开发区管委会债券',
		code: 'CDS3456',
		rating: 'AA+',
		issueDate: '2023-11-20',
		maturityDate: '2027-11-20',
		issueAmount: '10亿元'
	},
	{
		name: '成都市锦江区财政局债券',
		code: 'CDS1234',
		rating: 'AA',
		issueDate: '2023-09-05',
		maturityDate: '2026-09-05',
		issueAmount: '3亿元'
	},
	{
		name: '成都市武侯区国有资产投资有限公司债券',
		code: 'CDS5678',
		rating: 'AAA',
		issueDate: '2023-08-15',
		maturityDate: '2028-08-15',
		issueAmount: '8亿元'
	},
	{
		name: '成都天府新区管委会债券',
		code: 'CDS7890',
		rating: 'AA+',
		issueDate: '2023-07-10',
		maturityDate: '2027-07-10',
		issueAmount: '6亿元'
	},
	{
		name: '成都市青羊区财政局债券',
		code: 'CDS4321',
		rating: 'AA',
		issueDate: '2023-06-25',
		maturityDate: '2026-06-25',
		issueAmount: '2.5亿元'
	},
	{
		name: '成都市金牛区城市发展投资有限公司债券',
		code: 'CDS8765',
		rating: 'AA+',
		issueDate: '2023-05-18',
		maturityDate: '2028-05-18',
		issueAmount: '4亿元'
	},
	{
		name: '成都市温江区财政局债券',
		code: 'CDS9876',
		rating: 'AA',
		issueDate: '2023-04-12',
		maturityDate: '2026-04-12',
		issueAmount: '2亿元'
	},
	{
		name: '成都市双流区城市建设投资有限公司债券',
		code: 'CDS6543',
		rating: 'AA-',
		issueDate: '2023-03-22',
		maturityDate: '2027-03-22',
		issueAmount: '3.5亿元'
	},
	{
		name: '成都市郫都区国有资产经营有限公司债券',
		code: 'CDS2468',
		rating: 'AA-',
		issueDate: '2023-02-15',
		maturityDate: '2026-02-15',
		issueAmount: '1.8亿元'
	},
	{
		name: '成都经济开发区管委会债券',
		code: 'CDS1357',
		rating: 'AA+',
		issueDate: '2023-01-10',
		maturityDate: '2028-01-10',
		issueAmount: '7亿元'
	},
	{
		name: '成都市青城山旅游度假区管委会债券',
		code: 'CDS8642',
		rating: 'AA',
		issueDate: '2022-12-20',
		maturityDate: '2027-12-20',
		issueAmount: '3.2亿元'
	},
	{
		name: '成都市国有资产运营有限公司债券',
		code: 'CDS9753',
		rating: 'AAA',
		issueDate: '2022-11-15',
		maturityDate: '2029-11-15',
		issueAmount: '12亿元'
	}
]);

// 清除搜索
const clearSearch = () => {
	searchKeyword.value = '';
};

// 跳转到自选管理页面
const goToSelfchoseManagement = () => {
    uni.navigateTo({
        url: '/subPageB/selfchose-management/index',
        success: () => {
            console.log('跳转到自选管理页面成功');
        },
        fail: (err) => {
            console.error('跳转到自选管理页面失败', err);
            uni.showToast({
                title: '页面开发中，敬请期待',
                icon: 'none',
                duration: 2000
            });
        }
    });
};

// 显示债券详情
const showBondDetail = (row: any, index: number) => {
	console.log('查看债券详情:', row, index);
	// 实现跳转到债券详情页的逻辑
	uni.navigateTo({
		url: `/subPageA/bond-detail/index?id=${row.code}`,
		success: () => {
			// 可以在这里进行一些跳转成功后的操作
			console.log('跳转成功');
			// 在全局状态或缓存中保存当前选中的债券数据，以便详情页使用
			uni.setStorageSync('currentBondDetail', JSON.stringify(row));
		},
		fail: (err) => {
			console.error('跳转失败', err);
			// 如果页面不存在，提示用户
			uni.showToast({
				title: '详情页开发中，敬请期待',
				icon: 'none',
				duration: 2000
			});
		}
	});
};

// 使用权限 store
const permissionStore = usePermissionStore();
const { getTabBarPermissions } = storeToRefs(permissionStore);

// 加载状态
const isLoading = ref(false);
const hasMore = ref(true);

// 页面显示时获取权限数据
onShow(() => {
    console.log('TabBar权限列表:', getTabBarPermissions.value);
});

// 加载更多
const loadMore = () => {
	console.log('加载更多数据');
	// 实现加载更多数据的逻辑
	if (!hasMore.value || isLoading.value) return;
	
	isLoading.value = true;
	// 模拟加载数据
	setTimeout(() => {
		// 这里添加实际的加载逻辑
		isLoading.value = false;
		// 如果没有更多数据了，设置hasMore为false
		// hasMore.value = false;
	}, 1000);
};

// 定义cellStyle函数
const cellStyle = ({ row, column, rowIndex, columnIndex }) => {
	if (column.name === 'name') {
		return {
			overflow: 'hidden',
			textOverflow: 'ellipsis',
			whiteSpace: 'nowrap',
			maxWidth: '100px'
		};
	}
	return {};
};

// 定义表头样式
const headerCellStyle = ({ column, columnIndex }) => {
	return {
		fontWeight: 'bold',
		fontSize: '30rpx',
		backgroundColor: '#f6f8fa',
		color: '#333'
	};
};

// 页面加载时获取数据
onMounted(() => {
	// 获取债券列表数据
   
});
</script>

<style lang="scss" scoped>
/* 1.1 页面容器 */
.container {
	padding: 0 20rpx;
	height: 100vh;
	box-sizing: border-box;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;
	/* 防止整体页面滚动 */
}

/* 1.2 头部区域 */
.fixed-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 100;
}

/* 1.3 内容区域 */
.content-wrapper {
	flex: 1;
	margin-top: 180rpx;
	overflow: auto;
	/* 主滚动容器 */
	position: relative;
}

/* 1.4 滚动区域 */
.scrollable-content {
	height: 100%;

	::-webkit-scrollbar {
		display: none;
		width: 0;
		height: 0;
		color: transparent;
	}
}

/* 搜索框 */
.search-box {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
}

.search-input {
	flex: 1;
	height: 72rpx;
	background: rgba(255, 255, 255, 0.5);
	border-radius: 36rpx;
	display: flex;
	align-items: center;
	padding: 0 24rpx;
	backdrop-filter: blur(10rpx);
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);

	uni-icons {
		margin-right: 10rpx;
	}

	input {
		flex: 1;
		height: 100%;
		font-size: 28rpx;
	}

	.placeholder {
		color: #999999;
	}
}

.cancel-btn {
	padding: 0 20rpx;
	font-size: 28rpx;
	color: #ff9500;
}

/* 卡片容器 */
.card-container {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 卡片头部 */
.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
}

.card-title {
	display: flex;
	align-items: center;

	.title-icon {
		width: 48rpx;
		height: 52rpx;
		position: relative;
		top: -10rpx;

		&::before {
			content: '';
			position: absolute;
			inset: 0;
			background: linear-gradient(135deg, rgba(133, 111, 254, 0.6) 0%, rgba(255, 237, 221, 0) 100%);
			clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
		}
	}

	.title-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		transform: translateX(-25rpx);
	}
}

.more-link {
	display: flex;
	align-items: center;
	color: #ff9500;

	.more-text {
		font-size: 28rpx;
		margin-left: 10rpx;
	}
}

/* 债券列表 */
.bonds-list {
	padding: 10rpx 0;
	position: relative;
	height: 850rpx;
	/* 设置列表固定高度 */
}

/* 底部加载更多提示 */
.loading-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx 0;
	font-size: 26rpx;
	color: #999999;
}

.loading-text {
	margin-left: 10rpx;
}

/* 没有更多数据提示 */
.no-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx 0;
	font-size: 26rpx;
	color: #999999;
}

.no-more-text {
	margin-left: 10rpx;
}
</style>