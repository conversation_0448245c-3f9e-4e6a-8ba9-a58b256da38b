<template>
	<view class="mine-container" :style="{ backgroundImage: `url('${getAssetUrl('/mine-bg.png')}')` }">
		<!-- 用户信息 -->
		<CustomHead title="我的" :showBack="false" />
		<view class="user-info-section">
			<image class="avatar" :src="getAssetUrl('/avatar.png')" mode="aspectFill"></image>
			<!-- 暂时用 logo 替代头像 -->
			<view class="user-details">
				<text class="user-name">{{ userInfo.username }}</text>
				<view class="user-phone">
					<image class="phone-icon" :src="getAssetUrl('/phone.svg')"
						mode="aspectFill"></image>
					<text>{{ userInfo.mobile }}</text>
				</view>
			</view>
			<!-- 切换按钮始终显示，只有一个权限时禁用 -->
			<view class="group-switch">
				<text class="switch-label">{{ switchTargetLabel }}</text>
				<switch :checked="isGroupVersion" @change="handleVersionSwitch" 
					:disabled="!canSwitchVersion"
					width="200" 
					color="#FF8E2B"
					style="transform:scale(0.8)" />
			</view>
		</view>

		<!-- 公司信息 -->
		<view class="company-info-section">
			<text class="company-name">{{ userInfo.companyName }}</text>
			<text class="company-tag">{{ userInfo.companyLevel || '--' }}</text>
		</view>

		<!-- 设置 -->
		<view class="settings-section" @click="goToSettings">
			<image class="settings-icon" :src="getAssetUrl('/setting.svg')" mode="aspectFill">
			</image>
			<text class="settings-text">设置</text>
			<uni-icons class="arrow-icon" type="right" size="18" color="#000000"></uni-icons>
		</view>

		<!-- 退出登录 -->
		<view class="logout-section">
			<button class="logout-button" @click="handleLogout">退出登录</button>
		</view>
        <!-- 自定义tabBar -->
        <AppTabBar :selectNumber="4" :permissionData="getTabBarPermissions"/>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import CustomHead from '@/components/head/head.vue';
import AppTabBar from '@/components/AppTabBar/index.vue';
import { getPermissionList } from '@/api/common';
import { usePermissionStore } from '@/stores/permission';
import { useUserStore } from '@/stores/user';
import { storeToRefs } from 'pinia';
import { getAssetUrl } from '@/config/assets';

// ==================== 状态管理 ====================
const permissionStore = usePermissionStore();
const userStore = useUserStore();

const { 
    getUserInfo, 
    getTabBarPermissions, 
    getHomePagePermissions, 
    getSysVersion,
    getAvailableVersions
} = storeToRefs(permissionStore);

// ==================== 响应式数据 ====================
const isGroupVersion = ref(false);

// ==================== 计算属性 ====================
const userInfo = computed(() => getUserInfo.value || {});
const versionList = computed(() => getAvailableVersions.value || []);
const canSwitchVersion = computed(() => versionList.value.length > 1);

// 当前版本标签 - 显示当前版本名称
const switchTargetLabel = computed(() => {
    if (!canSwitchVersion.value) return '企业版';
    
    if (isGroupVersion.value) {
        // 当前是集团版（开关打开），显示集团版
        const groupVersion = versionList.value.find((v: any) => 
            v.id === 'group_mp' || v.name?.includes('集团')
        );
        return groupVersion?.name || '集团版';
    } else {
        // 当前是企业版（开关关闭），显示企业版
        const companyVersion = versionList.value.find((v: any) => 
            v.id === 'company_mp' || v.name?.includes('企业')
        );
        return companyVersion?.name || '企业版';
    }
});

// ==================== 版本管理方法 ====================
// 初始化版本数据
const initVersionData = () => {
    const versions = versionList.value;
    if (versions.length === 0) return;
    
    const currentVersion = getSysVersion.value;
    const currentVersionInfo = versions.find(v => v.id === currentVersion) || versions[0];
    
    if (currentVersionInfo) {
        // 判断是否为集团版
        isGroupVersion.value = currentVersionInfo.id === 'group_mp' || 
                              currentVersionInfo.name?.includes('集团') || false;
    }
};

// 版本切换处理
const handleVersionSwitch = async (e: any) => {
    if (!canSwitchVersion.value) return;
    
    const switchToGroup = e.detail.value;
    uni.showLoading({ title: '切换中...' });
    
    try {
        // 找到目标版本
        let targetVersion: any = null;
        if (switchToGroup) {
            // 切换到集团版
            targetVersion = versionList.value.find((v: any) => 
                v.id === 'group_mp' || v.name?.includes('集团')
            ) || versionList.value[1];
        } else {
            // 切换到企业版
            targetVersion = versionList.value.find((v: any) => 
                v.id === 'company_mp' || v.name?.includes('企业')
            ) || versionList.value[0];
        }
        
        if (!targetVersion) {
            throw new Error('未找到目标版本');
        }
        
        // 切换版本
        const result = await permissionStore.switchVersion(targetVersion.id);
        
        if (result.success) {
            // 更新开关状态
            isGroupVersion.value = switchToGroup;
            
            // 通知其他页面
            uni.$emit('versionChanged', {
                version: targetVersion.id,
                versionName: targetVersion.name
            });
            
            uni.showToast({
                title: '切换成功',
                icon: 'success'
            });
        } else {
            throw new Error('切换失败');
        }
        
    } catch (error) {
        console.error('版本切换失败:', error);
        isGroupVersion.value = !switchToGroup; // 恢复开关状态
        uni.showToast({
            title: '切换失败，请重试',
            icon: 'error'
        });
    } finally {
        uni.hideLoading();
    }
};

// ==================== 页面操作方法 ====================
const goToSettings = () => {
    console.log('跳转到设置页面');
    uni.navigateTo({ url: '/pages/setting/setting' });
};

const handleLogout = () => {
    uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        confirmColor: '#FF8E2B',
        success: (res) => {
            if (res.confirm) {
                // 使用userStore的logout方法
                userStore.logout();
                
                // 跳转到登录页
                uni.reLaunch({ url: '/pages/sign_in/sign_in' });
                
                uni.showToast({
                    title: '已退出登录',
                    icon: 'success'
                });
            }
        }
    });
};

// ==================== 生命周期 ====================
onMounted(() => {
    console.log('我的页面已挂载');
    initVersionData();
});

onShow(() => {
    console.log('我的页面显示');
    console.log('当前TabBar权限:', getTabBarPermissions.value);
    
    // 重新初始化版本数据，以防用户信息更新
    initVersionData();
});
</script>

<style lang="scss" scoped>
.mine-container {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100vh;
	// background-color: #f8f8f8; /* 页面背景色 */
	padding: 20rpx;
	padding-top: 0;
	box-sizing: border-box;
	background-size: cover;
	// background: linear-gradient(to bottom, #fffaf0 10%, #f8f8f8 40%); /* 顶部渐变背景 */

	.user-info-section {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
		margin-top: 40rpx;
		/* background-color: #fff; */
		/* 可选：给用户信息区加白色背景 */
		/* border-radius: 16rpx; */
		/* box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05); */

		.avatar {
			width: 120rpx;
			height: 120rpx;
			border-radius: 50%;
			margin-right: 10rpx;
			background-color: #eee;
			/* 占位符背景色 */
		}

		.user-details {
			flex: 1;
			display: flex;
			flex-direction: column;

			.user-name {
				font-size: 34rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 10rpx;
			}

			.user-phone {
				font-size: 28rpx;
				color: #666;
				display: flex;
				align-items: center;

				.phone-icon {
					width: 40rpx;
					height: 40rpx;
					margin-right: 10rpx;
				}
			}
		}

		.group-switch {
			display: flex;
			align-items: center;

			.switch-label {
				font-size: 28rpx;
				color: #666;
				margin-right: 10rpx;
			}
			
			// 禁用状态的开关样式
			switch[disabled] {
				opacity: 0.6;
			}
		}
	}

	.company-info-section {
		background: linear-gradient(90deg, #FFD484 0%, #FF8E2B 100%);
		padding: 40rpx;
		border-radius: 16rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(255, 204, 51, 0.2);

		.company-name {
			display: block;
			font-size: 28rpx;
			color: #fff;
			/* 深一点的文字颜色 */
			margin-bottom: 10rpx;
			font-weight: 500;
		}

		.company-tag {
			font-size: 24rpx;
			color: rgba(255, 255, 255, 0.7);
		}
	}

	.settings-section {
		display: flex;
		align-items: center;
		background-color: #fff;
		padding: 30rpx 40rpx;
		border-radius: 16rpx;
		margin-bottom: 35rpx;

		/* 与退出按钮间距拉大 */
		.settings-icon {
			width: 40rpx;
			height: 40rpx;
		}

		.settings-text {
			flex: 1;
			margin-left: 20rpx;
			font-size: 30rpx;
			color: #333;
		}

	}

	.logout-section {

		.logout-button {
			background-color: #fff;
			color: #000000;
			/* 红色文字 */
			border: 1rpx solid #eee;
			/* 浅边框 */
			font-size: 32rpx;
			border-radius: 10rpx;
			height: 100rpx;
			line-height: 100rpx;
			text-align: center;
			border: 1rpx solid #CCCCCC;
			width: 100%;

			&::after {
				border: none;
				/* 移除 uniapp 按钮默认边框 */
			}

			&:active {
				background-color: #f9f9f9;
				/* 点击效果 */
			}
		}
	}
}
</style>
