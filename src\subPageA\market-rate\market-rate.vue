<template>
    <view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
        <!-- 固定的头部区域 -->
        <view class="fixed-header">
            <CustomHead title="市场利率" />
        </view>

        <!-- 顶部tab切换 -->
        <view class="tab-container">
            <view class="tab-item" :class="{ active: activeTab === 'corporate' }" @click="switchTab('corporate')">国债
            </view>
            <view class="tab-item" :class="{ active: activeTab === 'financial' }" @click="switchTab('financial')">国开债
            </view>
        </view>

        <!-- 可滚动的内容区域 -->
        <scroll-view class="scrollable-content" scroll-y :show-scrollbar="false" enhanced>
            <!-- 主要内容卡片 -->
            <view class="main-content-card">
                <!-- ==================== 期限选择 ==================== -->
                <PeriodSelector 
                    :periodOptions="periodOptions" 
                    @periodChange="handlePeriodChange"
                />

                <!-- ==================== 利率信息展示 ==================== -->
                <RateInfoDisplay 
                    rateLabel="最新估值"
                    :rateValue="getSelectedPeriodData('value')"
                    changeLabel="涨跌BP"
                    :changeValue="getSelectedPeriodData('change')"
                    timeLabel="更新时间"
                    :timeValue="getSelectedPeriodData('time')"
                />

                <!-- ==================== 时间筛选器 ==================== -->
                <TimeFilterSelect :isShow="true" @filterChange="handleTimeFilterChange"
                    @openCalendar="toggleModal" />

                <!-- ==================== 图表上方当前数据点信息 ==================== -->
                <view class="chart-data-point">
                    <view class="point-date">
                        <view class="point-indicator"></view>
                        <text class="number-font">{{ latestDataDate }}</text>
                    </view>
                    <view class="point-value">估值: <text class="number-font">{{ latestDataValue }}</text></view>
                    <view class="point-change" :class="latestDataChange > 0 ? 'up' : (latestDataChange < 0 ? 'down' : 'flat')">
                        较昨日: <text class="number-font">{{ latestDataChange > 0 ? '+' : '' }}{{ latestDataChange }}BP</text>
                    </view>
                </view>

                <!-- ==================== 图表展示区 ==================== -->
                <view class="chart-component">
                    <RateChart :chartData="chartStaticData" @point-touch="handleChartPointTouch" />
                </view>
            </view>

            <!-- 期限列表卡片 -->
            <PeriodListCard :bondTabType="activeTab" :bondType="selectedBondType" :periodList="periodList"
                :rating="currentRateData.mainRating" :columns="[
                    { title: '期限', field: 'period' },
                    { title: '最新估值(%)', field: 'value' },
                    { title: '涨跌BP', field: 'change' }
                ]" />

            <!-- 底部留白区域，确保内容可以完全滚动显示 -->
            <view class="bottom-space"></view>
        </scroll-view>

        <!-- 日期选择组件（浮动在内容上方） -->
        <DateRangePicker v-model:visible="showModal" :defaultStartDate="startDate" :defaultEndDate="endDate"
            @dateRangeChange="handleDateRangeChange" @update:visible="handleModalVisibleChange" />
    </view>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import PeriodListCard from '@/components/MarketRate/PeriodListCard.vue';
import PeriodSelector from '@/components/common/PeriodSelector.vue';
import CustomHead from '@/components/head/head.vue';
import DateRangePicker from '@/components/calendar/DateRangePicker.vue';
import TimeFilterSelect from '@/components/common/TimeFilterSelect.vue';
// 引入图表组件
import RateChart from '@/components/MarketRate/RateChart.vue';
import RateInfoDisplay from '@/components/MarketRate/RateInfoDisplay.vue';
import { getMarketRate, getMarketRateCard, getMarketRateTerm } from '@/api/marketRate';
import { getSHIBORTerm } from '@/api/dataDict';
import { getAssetUrl } from '@/config/assets';


// Tab和基础配置相关
const activeTab = ref('corporate'); // 默认显示企业债
const bAnalCurveName = ref('中债国债收益率曲线');
const selectedBondType = ref('国债'); // 当前选中的债券类型

// 期限相关
const allPeriodOptions = ref([]);
const periodOptions = ref([]);
const activePeriod = ref('');
const activePeriodValue = ref(0);
const periodList = ref([]);

// 市场利率参数
const ccid = ref("98745c20df3747a1885c354f1ed85713");
const ownedModuleid = ref("708631605142536192");

// 计算近一年的起始日期和结束日期
const today = new Date();
const oneYearAgo = new Date();
oneYearAgo.setFullYear(today.getFullYear() - 1);

// 格式化日期为YYYY-MM-DD格式
const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};

const tradeDtStart = ref(formatDate(oneYearAgo));
const tradeDtEnd = ref(formatDate(today));
const activeTimeFilter = ref('1year');

// 当前数据显示相关
const currentRateData = ref({}); // 利率信息展示数据
const updateTime = ref('');

// 图表上方数据点信息
const latestDataDate = ref('--');
const latestDataValue = ref('--');
const latestDataChange = ref(0);

// 注释：updateChartPointData 函数已删除，现在使用实际接口数据



// 弹窗和日期相关
const showModal = ref(false); 
const startDate = ref(oneYearAgo);
const endDate = ref(new Date());



// 图表数据（从接口获取）
const chartStaticData = ref([]);




// 日期格式化函数
const formatDateString = (dateStr) => {
    if (!dateStr) return '--';
    
    // 如果是YYYY/MM/DD格式，保持原样
    if (dateStr.includes('/')) {
        return dateStr;
    }
    
    // 如果是YYYYMMDD格式，转换为YYYY/MM/DD
    if (dateStr.length === 8) {
        return `${dateStr.substring(0, 4)}/${dateStr.substring(4, 6)}/${dateStr.substring(6, 8)}`;
    }
    
    // 其他情况直接返回原始字符串
    return dateStr;
};

// 图表日期格式化函数
const formatDateToChart = (dateStr) => {
    if (!dateStr) return '';
    
    // 如果已经是YYYY/MM/DD格式，直接返回
    if (dateStr.includes('/') && dateStr.split('/').length === 3) {
        return dateStr;
    }
    
    // 如果是YYYYMMDD格式，转换为YYYY/MM/DD
    if (dateStr.length === 8 && /^\d{8}$/.test(dateStr)) {
        return `${dateStr.substring(0, 4)}/${dateStr.substring(4, 6)}/${dateStr.substring(6, 8)}`;
    }
    
    // 其他情况尝试解析日期
    try {
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}/${month}/${day}`;
        }
    } catch (e) {
        console.error('日期格式化失败:', dateStr, e);
    }
    
    return dateStr; // 如果无法处理，返回原始字符串
};

// 利率变化值处理
const formatYieldChange = (yieldAddValue) => {
    if (yieldAddValue === null || yieldAddValue === undefined) {
        return 0;
    }
    
    // 如果是数字类型，直接返回
    if (typeof yieldAddValue === 'number') {
        return yieldAddValue;
    }
    
    // 如果是字符串类型，需要处理
    if (typeof yieldAddValue === 'string') {
        // 去除可能的加号，然后转换为数字
        const cleanValue = yieldAddValue.replace(/^\+/, '');
        const numValue = parseFloat(cleanValue);
        return isNaN(numValue) ? 0 : numValue;
    }
    
    // 其他情况返回0
    return 0;
};

// 获取期限字典
const fetchPeriodList = async () => {
	const data = 'BOND_TERM_MARKET_CURVE_RATIO';
	const res = await getSHIBORTerm(data);
	allPeriodOptions.value = res.data.data;
	filterPeriods(activeTab.value);
	
	// 设置默认期限并获取图表数据
	if (periodOptions.value.length > 0 && !activePeriod.value) {
		const defaultPeriod = periodOptions.value[0];
		activePeriod.value = defaultPeriod.cnname;
		activePeriodValue.value = defaultPeriod.itemcode;
		// 获取图表数据
		fetchMarketRateData();
	}
};

// 获取市场利率
const fetchMarketRateData = async () => {
    const data = {
        ccid: ccid.value,
        ownedModuleid: ownedModuleid.value, 
        bAnalCurveterm: [activePeriodValue.value],
        date: [tradeDtStart.value, tradeDtEnd.value],
        radioDate: 12,
        bAnalCurveName: bAnalCurveName.value,
        tradeDtStart: tradeDtStart.value,
        tradeDtEnd: tradeDtEnd.value
    }
    try {
        const res = await getMarketRate(data);
        const periodKey = activePeriod.value;
        if (res.data?.data?.[periodKey]) {
            // 转换接口数据为图表数据格式
            const rawData = res.data.data[periodKey];
            chartStaticData.value = rawData.map(item => ({
                date: formatDateToChart(item.tradeDt),
                value: parseFloat(item.bAnalYield) || 0,
                bpChange: formatYieldChange(item.bAnalYieldAdd)
            }));
            
            // 更新图表上方最新数据点信息
            if (chartStaticData.value.length > 0) {
                const latestData = chartStaticData.value[chartStaticData.value.length - 1];
                latestDataDate.value = latestData.date;
                latestDataValue.value = `${latestData.value.toFixed(4)}%`;
                latestDataChange.value = latestData.bpChange;
            }
        }
    } catch (error) {
        console.error('获取市场利率数据失败:', error);
        // 如果接口失败，保持使用假数据
    }
}


// 获取市场利率期限列表
const fetchMarketRateTerm = async () => {
	const data = {
		ccid: ccid.value,
		ownedModuleid: ownedModuleid.value,
		bAnalCurveName: bAnalCurveName.value
	}
    const res = await getMarketRateTerm(data);
	periodList.value = res.data.data.map(item => ({
		period: item.bAnalCurveterm,
		value: parseFloat(item.bAnalYield) || 0,
		change: formatYieldChange(item.bAnalYieldAdd),
		displayValue: item.bAnalYield ? parseFloat(item.bAnalYield).toFixed(4) : '0.0000',
        time: item.tradeDt
	}));
    
    // 获取期限列表后，更新当前利率数据
    updateCurrentRateFromPeriodList();
};


// 根据tab筛选期限
const filterPeriods = (tab) => {
    if (tab == 'corporate') {
        const targetCnnames = ['5Y', '10Y', '15Y', '20Y', '30Y', '40Y', '50Y'];
        periodOptions.value = allPeriodOptions.value.filter(item => targetCnnames.includes(item.cnname));
        bAnalCurveName.value = '中债国债收益率曲线';
    } else {
        const targetCnnames = ['10Y', '15Y', '20Y', '30Y', '40Y', '50Y'];
        periodOptions.value = allPeriodOptions.value.filter(item => targetCnnames.includes(item.cnname));
        bAnalCurveName.value = '中债国开债收益率曲线';
    }
};

// 切换标签页
const switchTab = (tab) => {
    activeTab.value = tab;
    
    if (!allPeriodOptions.value?.length) {
        fetchPeriodList();
    } else {
        filterPeriods(tab);
        // 重置期限选择，设置新的默认期限
        if (periodOptions.value.length > 0) {
            const defaultPeriod = periodOptions.value[0];
            activePeriod.value = defaultPeriod.cnname;
            activePeriodValue.value = defaultPeriod.itemcode;
            // 获取新期限的图表数据
            fetchMarketRateData();
        }
    }
    
    fetchMarketRateTerm();
};



// 处理期限变更事件
const handlePeriodChange = (data) => {
    activePeriod.value = data.period;
    activePeriodValue.value = data.itemcode;
    fetchMarketRateData();
    updateCurrentRateFromPeriodList();
};

// 更新当前利率数据
const updateCurrentRateFromPeriodList = () => {
    if (!periodList.value.length || !activePeriod.value) return;
    
    // 查找当前选中期限在期限列表中的数据
    const matchingPeriod = periodList.value.find(item => item.period === activePeriod.value);
    
    if (matchingPeriod) {
        currentRateData.value = {
            ...currentRateData.value,
            bAnalYield: matchingPeriod.value,
            bAnalYieldAdd: matchingPeriod.change
        };
        
        // 更新时间为当前时间
        const now = new Date();
        updateTime.value = formatDate(now);
    }
};

// 监听期限列表变化，更新当前利率数据
watch(() => periodList.value, () => {
    updateCurrentRateFromPeriodList();
}, { deep: true });

// 监听选中的期限变化
watch(() => activePeriod.value, () => {
    updateCurrentRateFromPeriodList();
});



// 切换弹窗显示状态
const toggleModal = () => {
    showModal.value = !showModal.value;
};

// 处理日期区间变更
const handleDateRangeChange = (dateRange) => {
    tradeDtStart.value = dateRange[0];
    tradeDtEnd.value = dateRange[1];
    fetchMarketRateData();
};

// 处理弹窗可见性变化
const handleModalVisibleChange = () => {};

// 处理时间筛选变更
const handleTimeFilterChange = (data) => {
    activeTimeFilter.value = data.filter;
    tradeDtStart.value = data.dateRange[0];
    tradeDtEnd.value = data.dateRange[1];
    fetchMarketRateData();
};

// 处理图表触摸事件
const handleChartPointTouch = (pointData) => {
    // console.log('📊 触摸点数据:', pointData);
    // 示例：更新图表上方的数据点信息
    latestDataDate.value = pointData.date;
    latestDataValue.value = `${pointData.value}%`;
    latestDataChange.value = pointData.bpChange === 'N/A' ? 0 : pointData.bpChange;
};



// 获取选中期限数据
const getSelectedPeriodData = (field) => {
    if (!periodList.value.length || !activePeriod.value) return '--';
    
    // 查找当前选中期限在期限列表中的数据
    const matchingPeriod = periodList.value.find(item => item.period === activePeriod.value);
    
    if (!matchingPeriod) return '--';
    
    if (field === 'value') {
        return matchingPeriod.value !== null && matchingPeriod.value !== undefined ? `${parseFloat(matchingPeriod.value).toFixed(4)}%` : '--';
    } else if (field === 'change') {
        return matchingPeriod.change !== null && matchingPeriod.change !== undefined ? matchingPeriod.change : '--';
    } else if (field === 'time') {
        if (!matchingPeriod.time) return '--';
        // 格式化日期 YYYYMMDD -> YYYY-MM-DD
        const dateStr = matchingPeriod.time;
        return dateStr ? formatDateString(dateStr) : '--';
    }
    
    return '--';
};



onMounted(() => {
    fetchPeriodList();
    fetchMarketRateTerm();
});
</script>

<style lang="scss" scoped>
/* 页面容器 */
.container {
    padding: 0 20rpx;
    height: 100vh;
    box-sizing: border-box;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    /* 防止整体页面滚动 */
}

/* 头部区域 */
.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

/* 滚动区域 */
.scrollable-content {
    flex: 1;
    /* 给固定头部留出空间 */
    overflow: hidden;
    height: 100%;
    padding: 10rpx 0 0;

    ::-webkit-scrollbar {
        display: none;
        width: 0;
        height: 0;
        color: transparent;
    }
}

/* 底部留白 */
.bottom-space {
    height: 100rpx;
    width: 100%;
}

/* Tab切换样式 */
.tab-container {
    display: flex;
    margin-bottom: 20rpx;
    border-radius: 10rpx;
    margin-top: 180rpx;
    /* 给固定头部留出空间 */

    .tab-item {
        flex: 1;
        padding: 30rpx 0;
        text-align: center;
        position: relative;
        font-size: 32rpx;
        color: #999;
        transition: all 0.3s ease;
        font-weight: bold;

        &.active {
            color: #FF9900;
            font-weight: bold;

            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 40%;
                height: 6rpx;
                background-color: #FF9900;
                border-radius: 6rpx 6rpx 0 0;
            }
        }
    }
}

/* 主要内容样式 */
.main-content-card {
    background-color: white;
    border-radius: 20rpx;
    overflow: hidden;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
    padding: 30rpx;
}

/* 图表组件 */
.chart-component {
    background-color: white;
    border-radius: 16rpx;
    overflow: hidden;
    margin-top: 20rpx;
    padding: 20rpx;
    width: 100%;
}

/* 辅助样式 */
.loading-container {
    height: 400rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.8);
    margin: 20rpx 0;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);

    text {
        font-size: 28rpx;
        color: #999999;
    }
}


/* 图表数据点信息样式 */
.chart-data-point {
    display: flex;
    justify-content: space-between;
    align-items: center;

    background-color: #F5F7FF;
    padding: 15rpx 20rpx;
    border: 4rpx solid #fafafa;
    box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(172, 172, 172, 0.2);
    border-radius: 10rpx;
    font-size: 22rpx;
}

.point-date {
    display: flex;
    align-items: center;
}

.point-indicator {
    width: 14rpx;
    height: 14rpx;
    border-radius: 50%;
    background-color: #6F7CD1;
    margin-right: 10rpx;
}

.point-value {
    color: #333;
}

.point-change {
    &.up {
        color: #E76056;
    }

    &.down {
        color: #52C41A;
    }
    
    &.flat {
        color: #FEB249;
    }
}


</style>