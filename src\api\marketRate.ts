import request from '@/utils/request'

// 获取市场利率期限列表
export const getMarketRateTerm = (data: object) => {
	return request({
		method: 'POST',
		url: '/marketdata/market/analBondYieldList',
		data
	})
}
// 获取发行期限
export const getIssueTerm = (data: object) => {
	return request({
		method: 'GET',
		url: '/dict/view',
		data
	})
}
// 获取市场利率
export const getMarketRate = (data: object) => {
	return request({
		method: 'POST',
		url: '/marketdata/market/bondYieldCurve',
		data
	})
}
// 获取小卡片中的市场利率
export const getMarketRateCard = (data: object) => {
	return request({
		method: 'POST',
		url: '/tmCustomColumns/sql',
		data
	})
}
// 获取债券收益率曲线
export const getBondYieldCurve = (data: object) => {
	return request({
		method: 'POST',
		url: '/marketdata/market/bondYieldCurve',
		data
	})
}
// 获取估值行情详情顶部小卡片
export const getValuationMarketCard = (data: object) => {
	return request({
		method: 'POST',
		url: '/tmCustomColumns/sql',
		data
	})
}
// 获取估值图表数据
export const getValuationChart = (data: object) => {
	return request({
		method: 'POST',
		url: '/desktop/module/data',
		data
	})
}
// 承销明细
export const getUnderwritingDetail = (data: object) => {
	return request({
		method: 'POST',
		url: '/myBonds/underwritingDetails/queryBondRank',
		data
	})
}
//获取信用利差期限列表
export const getCreditSpreadTerm = (data: object) => {
	return request({
		method: 'POST',
		url: '/bondMarketOverview/spreadAnalysis/queryCreditSpread',
		data
	})
}
// 获取信用利差曲线数据(小程序独用)
export const getCreditSpreadCurve = (data: object) => {
	return request({
		method: 'POST',
		url: '/bondMarketOverview/spreadAnalysis/queryCreditSpreadCurve',
		data
	})
}
// 获取LPR利率
export const getLPRRate = (data: object) => {
	return request({
		method: 'POST',
		url: '/web/Shibor/policy',
		data
	})
}
// 获取SHIBOR利率
export const getSHIBORRate = (data: object) => {
	return request({
		method: 'POST',
		url: '/web/Shibor/rateChart',
		data
	})
}
// 获取估值行情曲线数据
export const getCompanyValuationLine = (data: object) => {
	return request({
		method: 'POST',
		url: '/myBonds/companyValuation/queryCompanyValuationLine',
		data
	})
}