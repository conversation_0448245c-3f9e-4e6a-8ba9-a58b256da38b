<template>
	<view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
		<!-- 固定的头部区域 -->
		<view class="fixed-header">
			<CustomHead title="测试页面" />
		</view>
		<view class="content-wrapper">
			<scroll-view class="scrollable-content" scroll-y :show-scrollbar="false" enhanced>
				
			</scroll-view>
		</view>
	</view>
</template>

<script setup lang="ts">
import CustomHead from '@/components/head/head.vue';
import { ref, onMounted } from 'vue';
import { getAssetUrl } from '@/config/assets';

</script>

<style lang="scss" scoped>
/* 1.1 页面容器 */
.container {
	padding: 0 20rpx;
	height: 100vh;
	box-sizing: border-box;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;
	/* 防止整体页面滚动 */
}

/* 1.2 头部区域 */
.fixed-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 100;
}

/* 1.3 内容区域 */
.content-wrapper {
	flex: 1;
	margin-top: 180rpx;
	padding: 20rpx 0;
	overflow: auto;
	/* 主滚动容器 */
	position: relative;
}

/* 1.4 滚动区域 */
.scrollable-content {
	height: 100%;

	::-webkit-scrollbar {
		display: none;
		width: 0;
		height: 0;
		color: transparent;
	}
}
</style>