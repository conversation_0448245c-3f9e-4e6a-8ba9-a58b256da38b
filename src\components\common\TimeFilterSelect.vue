<template>
    <view class="time-filter-component" v-if="isShow">
        <view class="time-filter">
            <view class="filter-options">
                <view class="filter-btn" v-for="(filter, index) in timeFilters" :key="index"
                    :class="{ active: activeTimeFilter === filter.value }" @click="selectTimeFilter(filter.value)">
                    <text>{{ filter.label }}</text>
                </view>
            </view>
            <view class="divider"></view>
            <view class="calendar-btn" @click="toggleCalendar">
                <image class="calendar-icon" :src="getAssetUrl('/canlendar.png')" mode="">
                </image>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getAssetUrl } from '@/config/assets';

// 定义组件属性
const props = defineProps({
    // 是否显示组件
    isShow: {
        type: Boolean,
        default: true
    }
});

// 定义事件
const emit = defineEmits(['filterChange', 'openCalendar']);

// 时间筛选选项 - 写死在组件内
const timeFilters = [
    { label: '近一年', value: '1year' },
    { label: '近半年', value: '6month' },
    { label: '近三月', value: '3month' },
    { label: '近一月', value: '1month' },
];

// 当前选中的时间筛选器
const activeTimeFilter = ref('1year');

// 选择时间筛选
const selectTimeFilter = (filter) => {
    // 如果已经选中，则不重复触发
    if (activeTimeFilter.value === filter) {
        return;
    }

    activeTimeFilter.value = filter;

    // 获取当前日期
    const now = new Date();
    let startDate = new Date();

    // 根据不同时间筛选计算起始日期
    switch (filter) {
        case '1year':
            startDate.setFullYear(now.getFullYear() - 1);
            break;
        case '6month':
            startDate.setMonth(now.getMonth() - 6);
            break;
        case '3month':
            startDate.setMonth(now.getMonth() - 3);
            break;
        case '1month':
            startDate.setMonth(now.getMonth() - 1);
            break;
    }

    // 格式化日期为 YYYY-MM-DD
    const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    // 生成日期数组
    const dateRange = [formatDate(startDate), formatDate(now)];

    // 将选择结果传递给父组件
    emit('filterChange', {
        filter: filter,
        dateRange: dateRange
    });
};

// 切换日历显示
const toggleCalendar = () => {
    emit('openCalendar');
};

// 初始化
onMounted(() => {
    // 初始化时触发一次，以便父组件获取初始值
    selectTimeFilter(activeTimeFilter.value);
});
</script>

<style lang="scss" scoped>
.time-filter-component {
    margin-bottom: 30rpx;

    .time-filter {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        overflow: hidden;
        width: 100%;

        .filter-options {
            display: flex;
            flex: 1;
            justify-content: space-between;
        }

        .filter-btn {
            height: 50rpx;
            line-height: 50rpx;
            padding: 0 30rpx;
            background-color: #f0f0f0;
            border-radius: 60rpx;
            font-size: 24rpx;
            transition: all 0.2s ease;
            color: #333;
            text-align: center;
            white-space: nowrap;

            &.active {
                background-color: #FF9900;
                color: white;
            }
        }

        .divider {
            width: 1rpx;
            height: 50rpx;
            background-color: #e0e0e0;
            margin: 0 20rpx;
            flex-shrink: 0;
        }

        .calendar-btn {
            width: 50rpx;
            height: 50rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;

            .calendar-icon {
                width: 50rpx;
                height: 50rpx;
            }
        }
    }
}
</style>