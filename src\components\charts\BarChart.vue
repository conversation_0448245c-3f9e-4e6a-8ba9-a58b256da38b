<template>
    <view class="chart-container" :style="{ width: width, height: height }">
        <canvas canvas-id="barChart" id="barChart" class="bar-chart-canvas"></canvas>
    </view>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, getCurrentInstance } from 'vue';

const props = defineProps({
    // 数据集合
    dataSet: {
        type: Array,
        default: () => []
        // 数据格式: [{year: '2025', value: 173.5933}, ...]
    },
    // 图表宽度
    width: {
        type: String,
        default: '100%'
    },
    // 图表高度
    height: {
        type: String,
        default: '430rpx'
    },
    // 柱子颜色
    barColor: {
        type: String,
        default: '#FFC069'
    },
    // 是否自动刷新
    autoRefresh: {
        type: Boolean,
        default: true
    },
    // 单位
    unit: {
        type: String,
        default: '亿'
    }
});

// 获取组件实例
const instance = getCurrentInstance();
const canvasContext = ref(null);
const canvasWidth = ref(300);
const canvasHeight = ref(300);

// 图表配置
const config = ref({
    padding: { top: 30, right: 10, bottom: 40, left: 10 },
    barWidth: 40,
    barSpacing: 20,
    gridLineColor: '#CCCCCC',
    gridLineDash: [4, 4],
    textColor: '#999999',
    valueTextColor: '#999999',
    valueTextSize: 12,
    axisTextSize: 12,
    unitTextSize: 12,
});

// 初始化图表
const initChart = () => {
    // 获取父容器尺寸来设置Canvas尺寸
    nextTick(() => {
        if (!instance) {
            return;
        }

        // 使用uni.createSelectorQuery获取父容器尺寸
        const query = uni.createSelectorQuery().in(instance.proxy);
        query.select('.chart-container').boundingClientRect((data) => {
            if (data && typeof data.width === 'number' && typeof data.height === 'number') {
                const needsResize = canvasWidth.value !== data.width || canvasHeight.value !== data.height;

                // 只有当尺寸变化时才更新
                if (needsResize) {
                    canvasWidth.value = data.width;
                    canvasHeight.value = data.height;
                }

                // 创建canvas上下文
                const ctx = uni.createCanvasContext('barChart', instance.proxy);
                if (ctx) {
                    canvasContext.value = ctx;
                    // 确保在下一个渲染周期执行绘制
                    nextTick(() => {
                        renderChart();
                    });
                }
            }
        }).exec();
    });
};

// 渲染柱状图
const renderChart = () => {
    if (!canvasContext.value) return;

    const ctx = canvasContext.value;
    const data = props.dataSet;
    if (!data || data.length === 0) return;

    // 获取画布尺寸
    const width = canvasWidth.value;
    const height = canvasHeight.value;

    // 清除画布
    ctx.clearRect(0, 0, width, height);
    ctx.setFillStyle('#ffffff');
    ctx.fillRect(0, 0, width, height);

    // 计算图表绘制区域
    const padding = config.value.padding;
    const chartWidth = width - padding.left - padding.right;
    const chartHeight = height - padding.top - padding.bottom;

    // 计算最大值，用于Y轴缩放
    const maxValue = Math.max(...data.map(item => item.value)) * 1.1; // 增加10%的空间

    // 调整柱子宽度和间距
    const totalBars = data.length;
    const availableWidth = chartWidth;
    config.value.barWidth = Math.min(40, (availableWidth / totalBars) * 0.3);
    config.value.barSpacing = (availableWidth - (config.value.barWidth * totalBars)) / (totalBars + 1);

    // 绘制网格线
    ctx.beginPath();
    ctx.setStrokeStyle(config.value.gridLineColor);
    ctx.setLineDash(config.value.gridLineDash);
    
    // 绘制4条横向网格线（均匀分布），但不包括最底部的线（与X轴重合的位置）
    for (let i = 0; i < 4; i++) {
        const y = padding.top + (chartHeight / 4) * i;
        ctx.moveTo(padding.left, y);
        ctx.lineTo(width - padding.right, y);
    }
    ctx.stroke();
    ctx.setLineDash([]);

    // 绘制X轴实线
    ctx.beginPath();
    ctx.setStrokeStyle('#999999');
    ctx.setLineWidth(0.3);
    const xAxisY = padding.top + chartHeight;
    ctx.moveTo(padding.left, xAxisY);
    ctx.lineTo(width - padding.right, xAxisY);
    ctx.stroke();

    // 绘制单位文本
    ctx.setFontSize(config.value.unitTextSize);
    ctx.setFillStyle(config.value.textColor);
    ctx.setTextAlign('left');
    ctx.fillText(`单位: ${props.unit}`, padding.left, 20);

    // 绘制柱状图和数据
    data.forEach((item, index) => {
        // 计算柱子位置
        const x = padding.left + config.value.barSpacing + index * (config.value.barWidth + config.value.barSpacing);
        const barHeight = (item.value / maxValue) * chartHeight;
        const y = padding.top + chartHeight - barHeight;
        
        // 创建渐变色
        const gradient = ctx.createLinearGradient(x, y, x, y + barHeight);
        gradient.addColorStop(0, '#FFD285');
        gradient.addColorStop(1, '#FFAF69');
        
        // 绘制柱子
        ctx.setFillStyle(gradient);
        ctx.fillRect(x, y, config.value.barWidth, barHeight);
        
        // 绘制数值
        ctx.setFontSize(config.value.valueTextSize);
        ctx.setFillStyle(config.value.valueTextColor);
        ctx.setTextAlign('center');
        ctx.fillText(item.value.toFixed(4), x + config.value.barWidth / 2, y - 5);
    });
    
    // X轴标签统一绘制，确保在X轴之下
    ctx.setFontSize(config.value.axisTextSize);
    ctx.setFillStyle(config.value.textColor);
    ctx.setTextAlign('center');
    
    data.forEach((item, index) => {
        const x = padding.left + config.value.barSpacing + index * (config.value.barWidth + config.value.barSpacing);
        // 确保X轴标签在X轴线下方，保持10px的距离
        const labelY = xAxisY + 20; // X轴线 + 10px间距 + 文字基线调整
        ctx.fillText(item.year, x + config.value.barWidth / 2, labelY);
    });

    // 执行绘制
    ctx.draw();
};

// 监听数据变化
watch(() => props.dataSet, (newVal) => {
    if (props.autoRefresh && newVal.length > 0) {
        setTimeout(() => {
            renderChart();
        }, 50);
    }
}, { deep: true });

// 组件挂载后初始化图表
onMounted(() => {
    nextTick(() => {
        initChart();
    });
});

// 暴露方法让父组件可以手动触发重绘
defineExpose({
    renderChart
});
</script>

<style lang="scss" scoped>
.chart-container {
    position: relative;
    box-sizing: border-box;
    background-color: #ffffff;
    border-radius: 8px;
    height: 460rpx;
}

.bar-chart-canvas {
    width: 100% !important;
    height: 100% !important;
    display: block;
}
</style>