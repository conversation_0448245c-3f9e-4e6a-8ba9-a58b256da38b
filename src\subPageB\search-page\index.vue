<template>
    <view class="container">
        <!-- 固定的头部区域 -->
        <view class="fixed-header">
            <CustomHead title="搜索" :showBack="true" />
        </view>

        <!-- 搜索框 -->
        <view class="search-box">
            <view class="search-input">
                <uni-icons type="search" size="18" color="#999"></uni-icons>
                <input type="text" placeholder="债券简称/代码/发行人" v-model="searchKeyword" placeholder-class="placeholder" />
            </view>
            <text class="cancel-btn" @tap="clearSearch">取消</text>
        </view>

        <!-- Tab切换 -->
        <view class="tab-container">
            <view class="tab-item" :class="{ active: activeTab === 'bond' }" @click="switchTab('bond')">债券</view>
            <view class="tab-item" :class="{ active: activeTab === 'issuer' }" @click="switchTab('issuer')">发行人</view>
            <view class="tab-item" :class="{ active: activeTab === 'news' }" @click="switchTab('news')">政策资讯</view>
            <view class="tab-item" :class="{ active: activeTab === 'announcement' }" @click="switchTab('announcement')">公司公告</view>
        </view>

        <!-- 债券和发行人子标签 -->
        <view class="subtab-container" v-if="activeTab === 'bond' || activeTab === 'issuer'" style="display: none;">
            <scroll-view class="subtab-scroll" scroll-x :show-scrollbar="false" enhanced>
                <view class="subtab-item" :class="{ active: subActiveTab === 'all' }" @click="switchSubTab('all')">全部</view>
                <view class="subtab-item" :class="{ active: subActiveTab === 'recent' }" @click="switchSubTab('recent')">最近查看</view>
                <view class="subtab-item" :class="{ active: subActiveTab === 'myCollection' }" @click="switchSubTab('myCollection')">我的收藏</view>
            </scroll-view>
        </view>

        <!-- 政策资讯子标签 -->
        <view class="subtab-container news" v-if="activeTab === 'news'">
            <scroll-view class="subtab-scroll" scroll-x :show-scrollbar="false" enhanced>
                <view class="subtab-item" :class="{ active: newsSubTab === 'all' }" @click="switchNewsTab('all')">全部</view>
                <view class="subtab-item" :class="{ active: newsSubTab === 'policy' }" @click="switchNewsTab('policy')">监管政策</view>
                <view class="subtab-item" :class="{ active: newsSubTab === 'analysis' }" @click="switchNewsTab('analysis')">新政解读</view>
                <view class="subtab-item" :class="{ active: newsSubTab === 'market' }" @click="switchNewsTab('market')">市场战况</view>
                <view class="subtab-item" :class="{ active: newsSubTab === 'typical' }" @click="switchNewsTab('typical')">典型案例</view>
            </scroll-view>
        </view>

        <!-- 公司公告子标签 -->
        <view class="subtab-container news" v-if="activeTab === 'announcement'">
            <scroll-view class="subtab-scroll" scroll-x :show-scrollbar="false" enhanced>
                <view class="subtab-item" :class="{ active: announcementSubTab === 'all' }" @click="switchAnnouncementTab('all')">全部</view>
                <view class="subtab-item" :class="{ active: announcementSubTab === 'performance' }" @click="switchAnnouncementTab('performance')">业绩公告</view>
                <view class="subtab-item" :class="{ active: announcementSubTab === 'dividend' }" @click="switchAnnouncementTab('dividend')">分红派息</view>
                <view class="subtab-item" :class="{ active: announcementSubTab === 'bond' }" @click="switchAnnouncementTab('bond')">债券信息</view>
                <view class="subtab-item" :class="{ active: announcementSubTab === 'meeting' }" @click="switchAnnouncementTab('meeting')">股东大会</view>
            </scroll-view>
        </view>

        <view class="content-wrapper">
            <scroll-view class="scrollable-content" scroll-y @scrolltolower="loadMore" :show-scrollbar="false" enhanced :scroll-top="scrollTop" :style="{ height: scrollViewHeight }">
                <!-- 债券列表 -->
                <block v-if="activeTab === 'bond'">
                    <view class="bond-item" v-for="(item, index) in bondsList" :key="index">
                        <view class="bond-info">
                            <view class="bond-name-row">
                                <text class="bond-name">{{ item.name }}</text>
                                <text class="tag-text">{{ item.tag }}</text>
                            </view>
                            <view class="bond-code">{{ item.code }}</view>
                        </view>
                        <view class="add-btn" @tap="addBond(item)">
                            <uni-icons type="plus" size="16" color="#FF9900"></uni-icons>
                            <text class="add-text">添加</text>
                        </view>
                    </view>
                </block>

                <!-- 发行人列表 -->
                <block v-if="activeTab === 'issuer'">
                    <view class="issuer-item" v-for="(item, index) in issuerList" :key="index">
                        <view class="issuer-top-row">
                             <view class="bond-name-row">
                                 <text class="bond-name">{{ item.name }}</text>
                                 <text class="tag-text issuer-tag">{{ item.tag || '城投' }}</text>
                             </view>
                             <view class="add-btn" @tap="addIssuer(item)">
                                 <uni-icons type="plus" size="16" color="#FF9900"></uni-icons>
                                 <text class="add-text">添加</text>
                             </view>
                        </view>
                        <view class="issuer-details-row">
                             <view class="detail-column">
                                 <text class="detail-value">{{ item.rating || 'AAA' }}</text>
                                 <text class="detail-label">主体评级</text>
                             </view>
                             <view class="detail-column">
                                 <text class="detail-value">{{ item.location || '北京' }}</text>
                                 <text class="detail-label">地区</text>
                             </view>
                             <view class="detail-column">
                                 <text class="detail-value">{{ item.nature || '市级国企' }}</text>
                                 <text class="detail-label">主体性质</text>
                             </view>
                        </view>
                    </view>
                </block>
                
                <!-- 政策资讯列表 -->
                <block v-if="activeTab === 'news'">
                    <view class="news-item" v-for="(item, index) in filteredNewsList" :key="index" @click="viewNewsDetail(item)">
                        <text class="news-title">{{ item.title }}</text>
                        <view class="news-bottom">
                            <text class="news-department">{{ item.department }}</text>
                            <text class="news-date">{{ item.date }}</text>
                        </view>
                    </view>
                </block>

                <!-- 公司公告列表 -->
                <block v-if="activeTab === 'announcement'">
                    <view class="news-item" v-for="(item, index) in filteredAnnouncementList" :key="index" @click="viewAnnouncementDetail(item)">
                        <text class="news-title">{{ item.title }}</text>
                        <view class="news-bottom announcement-bottom">
                            <text class="news-date">{{ item.date }}</text>
                        </view>
                    </view>
                </block>

                <!-- 加载状态 -->
                <view class="loading-more" v-if="isLoading">
                    <uni-icons type="spinner-cycle" size="20" color="#999"></uni-icons>
                    <text class="loading-text">加载中...</text>
                </view>
                
                <!-- 没有更多数据提示 -->
                <view class="no-more" v-if="!hasMore && !isLoading">
                    <text class="no-more-text">没有更多数据了</text>
                </view>
            </scroll-view>
        </view>
    </view>
</template>

<script setup lang="ts">
import CustomHead from '@/components/head/head.vue';
import { ref, onMounted, computed, onUnmounted } from 'vue';

// 当前激活的标签页
const activeTab = ref('news'); // 默认显示政策资讯
const subActiveTab = ref('all'); // 默认子标签
const newsSubTab = ref('all'); // 默认政策资讯子标签
const announcementSubTab = ref('all'); // 默认公司公告子标签
const scrollTop = ref(0); // 滚动位置
const scrollViewHeight = ref('700rpx'); // 滚动区域高度，默认值

// 搜索关键字
const searchKeyword = ref('');

// 债券列表数据
const bondsList = ref([
    {
        name: '李宁有限公司',
        code: '264502.SH',
        tag: '企业资产支持证券'
    },
    {
        name: '李宁有限公司',
        code: '264502.SH',
        tag: '企业资产支持证券'
    },
    {
        name: '李宁有限公司',
        code: '264502.SH',
        tag: '企业资产支持证券'
    },
    {
        name: '李宁有限公司',
        code: '264502.SH',
        tag: '企业资产支持证券'
    },
    {
        name: '李宁有限公司',
        code: '264502.SH',
        tag: '企业资产支持证券'
    },
    {
        name: '李宁有限公司',
        code: '264502.SH',
        tag: '企业资产支持证券'
    },
    {
        name: '李宁有限公司',
        code: '264502.SH',
        tag: '企业资产支持证券'
    }
]);

// 发行人列表数据
const issuerList = ref([
    {
        name: '李宁有限公司',
        code: '264502.SH',
        tag: '城投',
        rating: 'AAA',
        location: '北京',
        nature: '市级国企'
    },
    {
        name: '李宁有限公司',
        code: '264502.SH',
        tag: '城投',
        rating: 'AAA',
        location: '北京',
        nature: '市级国企'
    },
    {
        name: '李宁有限公司',
        code: '264502.SH',
        tag: '城投',
        rating: 'AAA',
        location: '北京',
        nature: '市级国企'
    }
]);

// 政策资讯列表数据
const newsList = ref([
    {
        title: '【专题研究-2025年第三期】一文读懂募集资金金新规要求',
        department: '宁波银行投资银行部',
        date: '2025-03-28',
        type: 'policy'
    },
    {
        title: '【专题研究-2025年第二期】债券发行注册制变革解读',
        department: '宁波银行投资银行部',
        date: '2025-03-25',
        type: 'analysis'
    },
    {
        title: '【市场战况-2025年第一季度】债券发行量分析报告',
        department: '宁波银行投资银行部',
        date: '2025-03-20',
        type: 'market'
    },
    {
        title: '【典型案例】某城投公司债务重组成功案例分析',
        department: '宁波银行投资银行部',
        date: '2025-03-18',
        type: 'typical'
    },
    {
        title: '【监管政策】央行关于金融机构债券投资新规发布',
        department: '宁波银行投资银行部',
        date: '2025-03-15',
        type: 'policy'
    },
    {
        title: '【新政解读】绿色金融债券发行管理办法要点解析',
        department: '宁波银行投资银行部',
        date: '2025-03-10',
        type: 'analysis'
    }
]);

// 公司公告列表数据
const announcementList = ref([
    {
        title: '关于2024年度分红派息实施公告',
        date: '2025-03-28',
        type: 'dividend'
    },
    {
        title: '关于公司债券2025年付息公告',
        date: '2025-03-28',
        type: 'bond'
    },
    {
        title: '第三季度业绩报告公告',
        date: '2025-03-27',
        type: 'performance'
    },
    {
        title: '关于召开2025年第一次临时股东大会的通知',
        date: '2025-03-27',
        type: 'meeting'
    },
    {
        title: '关于"23年第一期中期票据"发行结果的公告',
        date: '2025-03-26',
        type: 'bond'
    },
    {
        title: '关于公司高管人员变更的公告',
        date: '2025-03-26',
        type: 'other'
    }
]);

// 根据选择的子标签过滤公司公告列表
const filteredAnnouncementList = computed(() => {
    if (announcementSubTab.value === 'all') {
        return announcementList.value;
    } else {
        return announcementList.value.filter(item => item.type === announcementSubTab.value);
    }
});

// 根据选择的子标签过滤政策资讯列表
const filteredNewsList = computed(() => {
    if (newsSubTab.value === 'all') {
        return newsList.value;
    } else {
        return newsList.value.filter(item => item.type === newsSubTab.value);
    }
});

// 加载状态
const isLoading = ref(false);
const hasMore = ref(true);
const currentPage = ref(1);
const pageSize = ref(10);

// 加载更多数据
const loadMore = () => {
    if (!hasMore.value || isLoading.value) return;
    
    isLoading.value = true;
    console.log('加载更多数据，当前标签:', activeTab.value);
    
    // 模拟请求延迟
    setTimeout(() => {
        // 根据当前标签页加载不同类型的数据
        try {
            if (activeTab.value === 'bond') {
                // 加载更多债券数据
                const newBonds = generateMoreBonds();
                bondsList.value = [...bondsList.value, ...newBonds];
                
                // 模拟数据加载完毕的情况
                if (currentPage.value >= 3) {
                    hasMore.value = false;
                }
            } else if (activeTab.value === 'issuer') {
                // 加载更多发行人数据
                const newIssuers = generateMoreIssuers();
                issuerList.value = [...issuerList.value, ...newIssuers];
                
                // 模拟数据加载完毕的情况
                if (currentPage.value >= 3) {
                    hasMore.value = false;
                }
            } else if (activeTab.value === 'news') {
                // 加载更多政策资讯数据
                const newNews = generateMoreNews();
                newsList.value = [...newsList.value, ...newNews];
                
                // 模拟数据加载完毕的情况
                if (currentPage.value >= 3) {
                    hasMore.value = false;
                }
            } else if (activeTab.value === 'announcement') {
                // 加载更多公司公告数据
                const newAnnouncements = generateMoreAnnouncements();
                announcementList.value = [...announcementList.value, ...newAnnouncements];
                
                // 模拟数据加载完毕的情况
                if (currentPage.value >= 3) {
                    hasMore.value = false;
                }
            }
            
            currentPage.value += 1;
        } catch (error) {
            console.error('加载更多数据出错:', error);
        } finally {
            isLoading.value = false;
        }
    }, 1000);
};

// 切换标签页
const switchTab = (tab) => {
    activeTab.value = tab;
    console.log('切换到标签页:', tab);
    
    // 重置分页参数
    currentPage.value = 1;
    hasMore.value = true;
    
    // 重置加载状态
    isLoading.value = false;
    
    // 重置滚动位置
    resetScrollTop();
    
    // 重新计算滚动区域高度
    setTimeout(() => {
        calculateScrollViewHeight();
    }, 50);
};

// 切换子标签页
const switchSubTab = (tab) => {
    subActiveTab.value = tab;
    console.log('切换到子标签页:', tab);
    
    // 重置分页参数
    currentPage.value = 1;
    hasMore.value = true;
    isLoading.value = false;
    
    // 重置滚动位置
    resetScrollTop();
};

// 切换政策资讯子标签页
const switchNewsTab = (tab) => {
    newsSubTab.value = tab;
    console.log('切换到政策资讯子标签页:', tab);
    
    // 重置分页参数
    currentPage.value = 1;
    hasMore.value = true;
    isLoading.value = false;
    
    // 重置滚动位置
    resetScrollTop();
};

// 切换公司公告子标签页
const switchAnnouncementTab = (tab) => {
    announcementSubTab.value = tab;
    console.log('切换到公司公告子标签页:', tab);
    
    // 重置分页参数
    currentPage.value = 1;
    hasMore.value = true;
    isLoading.value = false;
    
    // 重置滚动位置
    resetScrollTop();
};

// 重置滚动位置
const resetScrollTop = () => {
    scrollTop.value = 0;
    // 强制在下一个事件循环更新
    setTimeout(() => {
        scrollTop.value = 1;
        setTimeout(() => {
            scrollTop.value = 0;
        }, 10);
    }, 10);
};

// 清除搜索
const clearSearch = () => {
    searchKeyword.value = '';
    // 实现清除搜索逻辑
};

// 添加债券
const addBond = (item) => {
    console.log('添加债券:', item);
    // 实现添加债券逻辑
};

// 添加发行人
const addIssuer = (item) => {
    console.log('添加发行人:', item);
    // 实现添加发行人逻辑
};

// 查看资讯详情
const viewNewsDetail = (item) => {
    console.log('查看资讯详情:', item);
    // 实现查看资讯详情逻辑
};

// 查看公告详情
const viewAnnouncementDetail = (item) => {
    console.log('查看公告详情:', item);
    // 实现查看公告详情逻辑
};

// 生成更多债券数据（模拟）
const generateMoreBonds = () => {
    const data: Array<{name: string; code: string; tag: string}> = [];
    for (let i = 0; i < 5; i++) {
        data.push({
            name: `债券公司 ${currentPage.value}-${i+1}`,
            code: `26${currentPage.value}${i}02.SH`,
            tag: '企业债券'
        });
    }
    return data;
};

// 生成更多发行人数据（模拟）
const generateMoreIssuers = () => {
    const data: Array<{name: string; code: string; tag: string; rating: string; location: string; nature: string}> = [];
    for (let i = 0; i < 5; i++) {
        data.push({
            name: `发行人 ${currentPage.value}-${i+1}`,
            code: `IS${currentPage.value}${i}`,
            tag: '科技',
            rating: 'AAA',
            location: '深圳',
            nature: '民营企业'
        });
    }
    return data;
};

// 生成更多政策资讯数据（模拟）
const generateMoreNews = () => {
    const data: Array<{title: string; department: string; date: string; type: string}> = [];
    for (let i = 0; i < 5; i++) {
        data.push({
            title: `【政策资讯-${currentPage.value}-${i+1}】债券市场最新动态分析报告`,
            department: '宁波银行投资银行部',
            date: `2025-0${currentPage.value}-${i+1}`,
            type: i % 4 === 0 ? 'policy' : i % 4 === 1 ? 'analysis' : i % 4 === 2 ? 'market' : 'typical'
        });
    }
    return data;
};

// 生成更多公司公告数据（模拟）
const generateMoreAnnouncements = () => {
    const data: Array<{title: string; date: string; type: string}> = [];
    for (let i = 0; i < 5; i++) {
        data.push({
            title: `公司公告-${currentPage.value}-${i+1}：关于重大事项的通知`,
            date: `2025-0${currentPage.value}-${i+1}`,
            type: i % 4 === 0 ? 'performance' : i % 4 === 1 ? 'dividend' : i % 4 === 2 ? 'bond' : 'meeting'
        });
    }
    return data;
};

onMounted(() => {
    // 页面加载时的逻辑
    // 预先加载一些数据
    bondsList.value = generateMoreBonds();
    issuerList.value = generateMoreIssuers();
    
    // 确保政策资讯有不同类型的数据
    newsList.value = [
        {
            title: '【专题研究-2025年第三期】一文读懂募集资金金新规要求',
            department: '宁波银行投资银行部',
            date: '2025-03-28',
            type: 'policy'
        },
        {
            title: '【专题研究-2025年第二期】债券发行注册制变革解读',
            department: '宁波银行投资银行部',
            date: '2025-03-25',
            type: 'analysis'
        },
        {
            title: '【市场战况-2025年第一季度】债券发行量分析报告',
            department: '宁波银行投资银行部',
            date: '2025-03-20',
            type: 'market'
        },
        {
            title: '【典型案例】某城投公司债务重组成功案例分析',
            department: '宁波银行投资银行部',
            date: '2025-03-18',
            type: 'typical'
        },
        {
            title: '【监管政策】央行关于金融机构债券投资新规发布',
            department: '宁波银行投资银行部',
            date: '2025-03-15',
            type: 'policy'
        },
        {
            title: '【新政解读】绿色金融债券发行管理办法要点解析',
            department: '宁波银行投资银行部',
            date: '2025-03-10',
            type: 'analysis'
        }
    ];
    
    // 合并一些测试数据
    newsList.value = [...newsList.value, ...generateMoreNews()];
    
    // 确保DOM渲染完成后再计算高度
    setTimeout(() => {
        calculateScrollViewHeight();
    }, 100);
    
    // 监听窗口尺寸变化，重新计算高度（小程序环境）
    uni.onWindowResize(() => {
        calculateScrollViewHeight();
    });
});

// 组件销毁时移除事件监听
onUnmounted(() => {
    // 在小程序环境中移除窗口尺寸变化监听
    uni.offWindowResize(() => {
        console.log('移除窗口尺寸监听');
    });
});

// 计算滚动视图的高度
const calculateScrollViewHeight = () => {
    // 获取系统信息
    uni.getSystemInfo({
        success: (res) => {
            console.log('系统信息:', res);
            const windowHeight = res.windowHeight;
            const statusBarHeight = res.statusBarHeight || 0;
            
            // 获取元素信息
            const query = uni.createSelectorQuery();
            // 获取固定头部高度
            query.select('.fixed-header').boundingClientRect();
            // 获取搜索框高度
            query.select('.search-box').boundingClientRect();
            // 获取主标签栏高度
            query.select('.tab-container').boundingClientRect();
            
            // 根据当前激活的标签获取子标签栏高度
            if (activeTab.value === 'news' || activeTab.value === 'announcement') {
                query.select('.subtab-container').boundingClientRect();
            }
            
            query.exec((rects) => {
                console.log('元素尺寸信息:', rects);
                if (!rects || !rects.length) return;
                
                let totalNonScrollHeight = 0;
                
                // 计算所有固定元素的高度总和
                rects.forEach((rect, index) => {
                    if (rect) {
                        console.log(`元素${index}高度:`, rect.height);
                        totalNonScrollHeight += rect.height;
                    }
                });
                
                // 考虑页面的内边距
                const containerPadding = 40; // 容器上下padding总和（0+40）
                
                // 计算滚动区域的可用高度
                const scrollHeight = windowHeight - totalNonScrollHeight - containerPadding;
                
                console.log('窗口高度:', windowHeight);
                console.log('固定元素总高度:', totalNonScrollHeight);
                console.log('容器内边距:', containerPadding);
                console.log('计算得到的滚动区域高度:', scrollHeight);
                
                // 设置滚动区域高度，确保不会为负值
                scrollViewHeight.value = Math.max(100, scrollHeight) + 'px';
            });
        },
        fail: (err) => {
            console.error('获取系统信息失败:', err);
            // 设置一个默认值
            scrollViewHeight.value = '500px';
        }
    });
};
</script>

<style lang="scss" scoped>
/* 1.1 页面容器 */
.container {
    padding: 0 20rpx 40rpx 20rpx; /* 增加了底部内边距 */
    height: 100vh;
    box-sizing: border-box;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden; /* 防止整体页面滚动 */
}

/* 1.2 头部区域 */
.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

/* 1.3 搜索框 */
.search-box {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    margin-top: 180rpx;
}

.search-input {
    flex: 1;
    height: 72rpx;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 36rpx;
    display: flex;
    align-items: center;
    padding: 0 24rpx;
    backdrop-filter: blur(10rpx);
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.search-input uni-icons {
    margin-right: 10rpx;
}

.search-input input {
    flex: 1;
    height: 100%;
    font-size: 28rpx;
}

.search-input .placeholder {
    color: #999999;
}

.cancel-btn {
    padding: 0 20rpx;
    font-size: 28rpx;
    color: #ff9500;
}

/* 1.4 Tab切换 */
.tab-container {
    display: flex;
    margin-bottom: 0; /* 移除底部间距 */
    border-radius: 10rpx;
}

.tab-item {
    flex: 1;
    padding: 30rpx 0;
    text-align: center;
    position: relative;
    font-size: 32rpx;
    color: #999;
    transition: all 0.3s ease;
    font-weight: bold;
}

.tab-item.active {
    color: #FF9900;
    font-weight: bold;
}

.tab-item.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40%;
    height: 6rpx;
    background-color: #FF9900;
    border-radius: 6rpx 6rpx 0 0;
}

/* 子标签栏 */
.subtab-container {
    margin-bottom: 0; /* 移除底部间距 */
    border-radius: 12rpx;
    padding: 10rpx 0;
}

.subtab-scroll {
    width: 100%;
    white-space: nowrap;
}

.subtab-item {
    display: inline-block;
    padding: 16rpx 30rpx;
    font-size: 28rpx;
    color: #666;
    position: relative;
    transition: all 0.3s;
}

.subtab-item.active {
    color: #FF9900;
    font-weight: bold;
}

/* 政策资讯子标签样式 */
.news .subtab-item {
    background-color: #f5f5f5;
    border-radius: 30rpx;
    margin: 0 10rpx;
    padding: 10rpx 25rpx;
}

.news .subtab-item.active {
    background-color: #FF9900;
    color: #fff;
}

/* 1.5 内容区域 */
.content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    padding-bottom: 0; /* 移除内边距 */
}

/* 1.6 滚动区域 */
.scrollable-content {
    width: 100%;
}

/* 债券列表项样式 */
.bond-item {
    display: flex;
    align-items: center;
    background-color: #fff;
    padding: 30rpx 20rpx;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0px 7px 14px 0px rgba(219,219,219,0.48);
}

/* 最后一个列表项去掉底部间距 */
.bond-item:last-child,
.issuer-item:last-child,
.news-item:last-child {
    margin-bottom: 0;
}

.bond-info {
    flex: 1;
}

.bond-name-row {
    display: flex;
    align-items: center;
    margin-bottom: 10rpx;
}

.bond-name {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-right: 20rpx;
}

.bond-code {
    font-size: 28rpx;
    color: #999;
}

.tag-text {
    font-size: 24rpx;
    padding: 2rpx 16rpx;
    background: linear-gradient(to right, #C8BEFF, #856FFE);
    color: #FFFFFF;
    border-bottom-left-radius: 22rpx;
    border-top-right-radius: 22rpx;
    white-space: nowrap;
}

.add-btn {
    width: auto;
    height: 60rpx;
    border-radius: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FF9900;
    padding: 0 20rpx;
}

.add-text {
    font-size: 26rpx;
    color: #FF9900;
    margin-left: 4rpx;
}

/* 发行人列表项样式 */
.issuer-item {
    background-color: #fff;
    padding: 30rpx 20rpx;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0px 7px 14px 0px rgba(219,219,219,0.48);
}

.issuer-top-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx; /* 增加与下方信息的间距 */
}

.issuer-details-row {
    display: flex;
    justify-content: space-between;
}

.detail-column {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.detail-value {
    font-size: 28rpx;
    color: #333;
    font-weight: bold;
    margin-bottom: 6rpx;
}

.detail-label {
    font-size: 24rpx;
    color: #999;
}

/* 政策资讯列表项样式 */
.news-item {
    background-color: #fff;
    padding: 30rpx 20rpx;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.news-title {
    font-size: 28rpx;
    color: #333;
    display: block;
    margin-bottom: 20rpx;
    line-height: 1.5;
    font-weight: normal;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.news-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.announcement-bottom {
    justify-content: flex-start;
}

.news-department {
    font-size: 24rpx;
    color: #999;
}

.news-date {
    font-size: 24rpx;
    color: #999;
}

/* 加载更多样式 */
.loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 0;
}

.loading-text {
    font-size: 24rpx;
    color: #999;
    margin-left: 10rpx;
}

.no-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 0;
}

.no-more-text {
    font-size: 24rpx;
    color: #999;
}

/* 城投标签特殊样式 */
.issuer-tag {
    background: linear-gradient(to right, #FFD28A, #FF9500);
}
</style> 