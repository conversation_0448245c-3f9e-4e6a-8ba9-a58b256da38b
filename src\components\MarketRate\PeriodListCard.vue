<template>
	<view class="period-table-card">
		<view class="card-header">
			<view class="title-wrapper">
				<view class="title-icon"></view>
				<text class="title-text">期限列表</text>
			</view>
			<view class="title-wrapper" v-show="isShowRating">{{ rating }}</view>
		</view>

		<view class="bond-list">
			<!-- 表头 -->
			<view class="list-header">
				<text class="header-item" v-for="(column, index) in columns" :key="index">{{ column.title }}</text>
			</view>

			<!-- 列表内容 -->
			<view class="list-content" :class="{ expanded: isPeriodListExpanded }">
				<view class="list-item" v-for="(item, index) in displayPeriodList" :key="index">
					<view class="period-name">{{ item.period }}</view>
					<view class="period-rate-container">
						<text class="period-rate number-font">{{ item.displayValue || (item.value ? item.value.toFixed(4) : '0.0000') }}%</text>
					</view>
					<view class="period-rate-container">
						<text class="period-change number-font" 
							:class="{ 
								'rate-up': isUpValue(item.change), 
								'rate-down': isDownValue(item.change), 
								'rate-zero': isZeroValue(item.change) 
							}">
							{{ formatValue(item.change) }}
						</text>
					</view>
				</view>
			</view>

			<!-- 查看更多 - 仅在列表条目超过5条时显示 -->
			<view class="view-more" @click="togglePeriodList" v-if="periodList.length > 5">
				<image class="icon-arrow" :class="{ 'rotate-180': isPeriodListExpanded }"
					:src="getAssetUrl('/home/<USER>')" mode=""></image>
				<text class="more-text">{{ isPeriodListExpanded ? '收起' : '查看更多' }}</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { getAssetUrl } from '@/config/assets';

// 组件属性
const props = defineProps({
	// 是否显示评级
	isShowRating: {
		type: Boolean,
		default: true
	},
	// 债券标签页类型：国债、国开债等
	bondTabType: {
		type: String,
		default: 'treasury'
	},
	// 债券类型
	bondType: {
		type: String,
		default: '中债'
	},
	// 评级
	rating: {
		type: String,
		default: ''
	},
	// 期限列表数据
	periodList: {
		type: Array,
		default: () => []
	},
	// 表格列配置
	columns: {
		type: Array,
		default: () => [
			{ title: '期限', field: 'period' },
			{ title: '最新估值(%)', field: 'value' },
			{ title: '涨跌BP', field: 'change' }
		]
	}
});

// 期限列表展开状态
const isPeriodListExpanded = ref(false);

// 计算属性：根据是否展开返回显示的数据
const displayPeriodList = computed(() => {
	if (isPeriodListExpanded.value) {
		// 展开状态显示全部数据
		return props.periodList;
	} else {
		// 收起状态最多显示5条数据
		return props.periodList.slice(0, 5);
	}
});

// 展开/收起期限列表
const togglePeriodList = () => {
	isPeriodListExpanded.value = !isPeriodListExpanded.value;
};

// 当债券标签页类型或债券类型变化时，重置展开状态
watch([() => props.bondTabType, () => props.bondType], () => {
	isPeriodListExpanded.value = false;
});

// 格式化显示值
const formatValue = (value) => {
	if (typeof value === 'number' && value > 0) {
		return '+' + value;
	}
	return value;
};

// 判断是否为上涨值
const isUpValue = (value) => {
	return typeof value === 'number' && value > 0;
};

// 判断是否为下跌值
const isDownValue = (value) => {
	return typeof value === 'number' && value < 0;
};

// 判断是否为0值
const isZeroValue = (value) => {
	return typeof value === 'number' && value === 0;
};

onMounted(() => {
	// 组件挂载时的初始化操作（如有需要）
});
</script>

<style lang="scss" scoped>

// 期限列表卡片样式
.period-table-card {
	background-color: white;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;
	margin-bottom: 30rpx;

	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 40rpx;

		.title-wrapper {
			display: flex;
			align-items: center;

			.title-icon {
				width: 48rpx;
				height: 52rpx;
				position: relative;
				top: -10rpx;

				&::before {
					content: '';
					position: absolute;
					inset: 0;
					background: linear-gradient(135deg, rgba(133, 111, 254, 0.6) 0%, rgba(255, 237, 221, 0) 100%);
					clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
				}
			}

			.title-text {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				transform: translateX(-25rpx);
			}
		}
	}

	// 与BondListCard保持一致的列表样式
	.bond-list {
		padding: 0 30rpx;
        padding-bottom: 30rpx;
		.list-header {
			display: flex;
			justify-content: space-between;
			padding: 30rpx;
			background: linear-gradient(180deg, #FAFAFA 0%, #F4F4F4 100%);
			box-shadow: inset 0rpx -2rpx 0rpx 0rpx #EAE9E9;

			.header-item {
				font-size: 28rpx;
				color: #000;
				font-weight: 500;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				
				&:nth-child(1) {
					width: 30%;
					text-align: left;
					padding-left: 10rpx;
				}
				
				&:nth-child(2),
				&:nth-child(3) {
					width: 30%;
					text-align: right;
					padding-right: 10rpx;
				}
			}
		}

		.list-content {
			max-height: 500rpx;
			overflow: hidden;
			transition: max-height 0.3s ease;

			&.expanded {
				max-height: 1200rpx;
				overflow-y: auto;
				scrollbar-width: none; /* Firefox */
				-ms-overflow-style: none; /* IE and Edge */
			}

			/* 隐藏滚动条 */
			&.expanded::-webkit-scrollbar {
				display: none; /* Chrome, Safari, Opera */
			}

			.list-item {
				display: flex;
				justify-content: space-between;
				padding: 30rpx;
				border-bottom: 2rpx solid #EAE9E9;

				.period-name {
					font-size: 28rpx;
					color: #333;
					width: 30%;
					text-align: left;
					padding-left: 10rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				.period-rate-container {
					display: flex;
					width: 30%;
					justify-content: flex-end;
					align-items: center;
					padding-right: 10rpx;

					.period-rate,
					.period-change {
						font-size: 28rpx;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						text-align: right;
						max-width: 90%;
					}
					
					.period-rate {
						color: #333;
					}

					.period-change {
						&.rate-up {
							color: #f56c6c;
						}

						&.rate-down {
							color: #67c23a;
						}
						
						&.rate-zero {
							color: #FEB249;
						}
					}
				}
			}
		}

		// 查看更多
		.view-more {
			display: flex;
			justify-content: center;
			flex-direction: column;
			align-items: center;
			padding: 30rpx;
            padding-bottom: 0;
			cursor: pointer;

			.more-text {
				font-size: 28rpx;
				color: #666;
			}

			.icon-arrow {
				width: 24rpx;
				height: 26rpx;
				margin-bottom: 10rpx;
			}

			.rotate-180 {
				transform: rotate(180deg);
			}
		}
	}
}
</style>