<template>
    <view class="container">
        <!-- 固定的头部区域 -->
        <view class="fixed-header">
            <CustomHead title="债券详情" />
        </view>
        <view class="content-wrapper">
            <scroll-view class="scrollable-content" scroll-y :show-scrollbar="false" enhanced>
                <!-- 债券信息卡片 -->
                <view class="bond-card"
                    :style="{ backgroundImage: `url('${getAssetUrl('/card-bg.png')}')` }">
                    <view class="bond-title">
                        <text class="bond-name">{{ bondInfo.binfoFullname }}</text>
                        <text class="bond-code number-font">{{ bondInfo.sinfoWindcode }}</text>
                    </view>

                    <view class="interest-rate-container">
                        <text class="interest-rate number-font">{{ bondInfo.latestCouponrate + '%' || '--' }}</text>
                        <text class="interest-rate-label">票面利率</text>
                    </view>

                    <view class="bond-info-row">
                        <view class="info-item">
                            <text class="info-value number-font">{{ bondInfo.remainTerm || '--' }}</text>
                            <text class="info-label">剩余期限</text>
                        </view>
                        <view class="info-item">
                            <text class="info-value number-font">{{ bondInfo.bondRating || '--' }}</text>
                            <text class="info-label">主/债</text>
                        </view>
                        <view class="info-item">
                            <text class="info-value number-font">{{ bondInfo.latestYtm || '--' }}</text>
                            <text class="info-label">最新YTM(%)</text>
                        </view>
                    </view>

                    <view class="bond-info-row">
                        <view class="info-item">
                            <text class="info-value number-font">{{ bondInfo.exerciseValuation || '--' }}</text>
                            <text class="info-label">行权估值(%)</text>
                        </view>
                        <view class="info-item">
                            <text class="info-value number-font">{{ bondInfo.maturityValuation || '--' }}</text>
                            <text class="info-label">到期估值(%)</text>
                        </view>
                        <view class="info-item">
                            <text class="info-value number-font">{{ bondInfo.cnbdCreditrating || '--' }}</text>
                            <text class="info-label">中债隐含评级</text>
                        </view>
                    </view>
                </view>

                <!-- 详情卡片（包含标签页、详细信息列表和自选按钮） -->
                <view class="details-card">
                    <!-- 标签页 -->
                    <view class="tabs-container">
                        <scroll-view class="tabs-scroll" scroll-x :show-scrollbar="false" enhanced>
                            <view class="tabs">
                                <view v-for="(tab, index) in tabs" :key="index" class="tab-item"
                                    :class="{ active: currentTab === index }" @click="switchTab(index)">
                                    {{ tab }}
                                </view>
                            </view>
                        </scroll-view>
                    </view>

                    <!-- 详细信息列表区域（可滚动） -->
                    <view class="details-scroll-area">
                        <!-- 详细信息列表：基本条款 -->
                        <scroll-view class="details-scroll" scroll-y v-if="currentTab === 0">
                            <view class="details-list">
                                <view class="detail-item" v-for="(item, index) in detailsList" :key="index">
                                    <text class="detail-label">{{ item.label }}</text>
                                    <text class="detail-value number-font">{{ item.value }}</text>
                                </view>
                            </view>
                        </scroll-view>

                        <!-- 详细信息列表：募集发行 -->
                        <scroll-view class="details-scroll" scroll-y v-if="currentTab === 1">
                            <view class="details-list">
                                <view class="detail-item" v-for="(item, index) in issuanceList" :key="index">
                                    <text class="detail-label">{{ item.label }}</text>
                                    <text class="detail-value number-font">{{ item.value }}</text>
                                </view>
                            </view>
                        </scroll-view>

                        <!-- 详细信息列表：中债隐含评级 -->
                        <scroll-view class="details-scroll" scroll-y v-if="currentTab === 2">
                            <view class="details-list">
                                <view class="detail-item" v-for="(item, index) in impliedRatingList" :key="index">
                                    <text class="detail-label">{{ item.label }}</text>
                                    <text class="detail-value number-font">{{ item.value }}</text>
                                </view>
                            </view>
                        </scroll-view>

                        <!-- 详细信息列表：主体评级 -->
                        <scroll-view class="details-scroll" scroll-y v-if="currentTab === 3">
                            <view class="details-list">
                                <view class="detail-item" v-for="(item, index) in issuerRatingList" :key="index">
                                    <text class="detail-label">{{ item.label }}</text>
                                    <text class="detail-value number-font">{{ item.value }}</text>
                                </view>
                            </view>
                        </scroll-view>

                        <!-- 详细信息列表：发行人 -->
                        <scroll-view class="details-scroll" scroll-y v-if="currentTab === 4">
                            <view class="details-list">
                                <view class="detail-item" v-for="(item, index) in issuerInfoList" :key="index">
                                    <text class="detail-label">{{ item.label }}</text>
                                    <text class="detail-value number-font">{{ item.value }}</text>
                                </view>
                            </view>
                        </scroll-view>
                    </view>

                    <!-- 自选按钮（固定在卡片底部） -->
                    <view class="add-favorite" @click="goToFavorite">
                        <uni-icons type="plus" class="add-icon" color="#FFA83C" size="22"></uni-icons>
                        <text>添加自选</text>
                    </view>
                </view>
            </scroll-view>
        </view>
    </view>
</template>

<script setup lang="ts">
import CustomHead from '@/components/head/head.vue';
import { ref, onMounted, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { getIssuanceDetail } from '@/api/dataDict';
import { getAssetUrl } from '@/config/assets';

// 标签页
const tabs = ref(['基本条款', '募集发行', '中债隐含评级', '主体评级', '发行人']);
const currentTab = ref(0);

// 页面参数
const pageParams = ref<Record<string, any>>({});

// 债券信息
const bondInfo = ref({
    accounttreatmentType: null,
    allLu: "江苏润城城市投资控股集团有限公司",
    allLuDetail: null,
    annDate: "********",
    binfoAct: "3650.0000",
    binfoActualbenchmark: "A/3650",
    binfoAgencyname: null,
    binfoAmountbyplacing: null,
    binfoBgndbyplacing: null,
    binfoCarrydate: "2018-09-11",
    binfoCodebyplacing: null,
    binfoCoupon: "*********",
    binfoCoupondatetxt: "每年9月11日付息,节假日顺延",
    binfoCouponname: null,
    binfoCouponrate: "4.550000",
    binfoCoupontxt: "4.55%",
    binfoCreditrating: null,
    binfoCreditratingagency: null,
    binfoCurpar: "100.0000",
    binfoDelistdate: "********",
    binfoEnddate: "********",
    binfoEnddbyplacing: null,
    binfoForm: "1",
    binfoFormercode: null,
    binfoFullname: "华福证券有限责任公司2018年公开发行公司债券(第一期)(品种一)",
    binfoGuaranteeId: null,
    binfoGuaranteesettlement: null,
    binfoGuarantor: null,
    binfoGuarintroduction: null,
    binfoGuartype: null,
    binfoInterestfrequency: "Y1",
    binfoInteresttype: "501002000",
    binfoInteresttypename: null,
    binfoIssueprice: "100.00",
    binfoIssuer: "华福证券有限责任公司",
    binfoIssuercode: "617bitmI4f",
    binfoIssuertype: "证券公司",
    binfoIssuetype: "*********",
    binfoIssuetypename: null,
    binfoListdate: "2018-09-28",
    binfoMaturitydate: "2021-09-11",
    binfoOutstandingbalance: "10.0000",
    binfoPar: 100,
    binfoPayment: null,
    binfoPaymentdate: "2021-09-13",
    binfoPaymenttype: "*********",
    binfoPaymenttypename: "单利",
    binfoPinyin: null,
    binfoProvisiontype: null,
    binfoSpecialbondtype: null,
    binfoSpread: null,
    binfoSubordinateornot: "0",
    binfoTaxrate: "20.0000",
    binfoTermDay: "1096.0000",
    binfoTermYear: "3.0000",
    binfoUnderwritingcode: "*********",
    binfoYearsnumber: 1,
    bisRight: null,
    bissueAmountMax: null,
    bissueAmountact: "15.0000",
    bissueAmountplan: "7.5000",
    bissueAnnouncement: "2018-09-04",
    bissueFee: null,
    bissueFirstissue: "2025-05-06",
    bissueLastissue: "2018-09-11",
    bmCompSname: null,
    bondRating: "AA+",
    bondTypeCode: null,
    bondTypeCode2: null,
    bondTypeName: null,
    bondTypeName2: null,
    bondratingConversion: null,
    bookAgency: null,
    bredemptionFeeration: null,
    btendrstReferyield: null,
    city: null,
    citycode: null,
    clientadddate: 1691596800000,
    clientopdate: 1691596800000,
    cnbdCreditrating: null,
    compProperty: null,
    creditratingConversion: null,
    crncyCode: "CNY",
    curvemeberCnbdDto: null,
    district: null,
    districtcode: null,
    exerciseValuation: null,
    firstYieldCnbd: null,
    funduse: null,
    guarantorlist: null,
    hostAgency: null,
    interestMargin: "3.45",
    isActDays: "0",
    isCallable: "0",
    isChooseright: "0",
    isCorporateBond: "0",
    isCrossmarket: null,
    isFailure: "0",
    isGuarantor: null,
    isIncbonds: "0",
    isInright: "0",
    isNetprice: "1",
    isPayadvanced: "0",
    isTaxfree: "0",
    isWindInvest: "否",
    issueObject: "合格机构投资者",
    issueStatus: "已到期",
    issuerName: null,
    issuerRatingDto: null,
    jointLu: null,
    latestCouponrate: "0.0345",
    latestValuationDate: null,
    latestYtm: null,
    leaderLu: null,
    leaderRate: null,
    listAnnDate: "********",
    listDateType: "近期已发行",
    managementAgency: null,
    managementFlag: "0",
    maturityValuation: null,
    newYieldCnbd: null,
    nextRecorddate: null,
    nextRightDate: null,
    objectId: "{CEA9EF8C-B02C-11E8-81CD-ECF4BBCF9C17}",
    opdate: 1656259200000,
    opmode: "0",
    productCode: null,
    productName: null,
    province: null,
    provincecode: null,
    registerFileNumber: null,
    registerFileTypeCode: null,
    reimbursement: "到期偿付",
    releaseYear: null,
    remainTerm: null,
    remainTermMonth: "1.0000",
    remainTermStr: null,
    rightTerm: null,
    sdivRecorddate: null,
    secId: null,
    sinfoCompindCode1: null,
    sinfoCompindCode2: null,
    sinfoCompindCode3: null,
    sinfoCompindCode4: null,
    sinfoCompindName1: null,
    sinfoCompindName2: null,
    sinfoCompindName3: null,
    sinfoCompindName4: null,
    sinfoExchmarket: "SSE",
    sinfoExchmarketname: null,
    sinfoFormerwindcode: null,
    sinfoName: "18华福G1",
    sinfoWindcode: "143795.SH",
    taxPayer: null,
    term: null,
    termMonth: null,
    termStr: null,
    tradeTypeCode: "*********"
});

// 基本条款详细信息列表
const detailsList = ref([
    { label: '发行期限', value: computed(() => bondInfo.value.term ? `${bondInfo.value.term}Y` : '-') },
    { label: '剩余期限', value: computed(() => bondInfo.value.remainTerm || '-') },
    { label: '发行时主体评级', value: computed(() => bondInfo.value.binfoCreditrating || '-') },
    { label: '票面与首次估值利差(BP)', value: computed(() => bondInfo.value.interestMargin || '-') },
    { label: '利率类型', value: computed(() => bondInfo.value.binfoInteresttypename || '-') },
    { label: '发行规模(亿)', value: computed(() => bondInfo.value.bissueAmountact || '-') },
    { label: '余额(亿)', value: computed(() => bondInfo.value.binfoOutstandingbalance ? (parseFloat(bondInfo.value.binfoOutstandingbalance) / 10000).toFixed(4) : '-') },
    { label: '内含特殊条款', value: computed(() => bondInfo.value.binfoProvisiontype || '-') },
    { label: '增信情况', value: computed(() => bondInfo.value.binfoGuarintroduction || '-') },
    { label: '下一行权日', value: computed(() => bondInfo.value.nextRightDate || '-') },
    { label: '下一债权登记日', value: computed(() => bondInfo.value.nextRecorddate || '-') }
]);

// 募集发行详细信息列表
const issuanceList = ref([
    { label: '主承销商', value: computed(() => bondInfo.value.allLu || '-') },
    { label: '牵头主承销商', value: computed(() => bondInfo.value.leaderLu || '-') },
    { label: '联席主承销商', value: computed(() => bondInfo.value.jointLu || '-') },
    { label: '发行方式', value: computed(() => bondInfo.value.binfoIssuetype || '-') },
    { label: '发行起始日', value: computed(() => bondInfo.value.bissueFirstissue || '-') },
    { label: '发行截止日', value: computed(() => bondInfo.value.bissueLastissue || '-') }
]);

// 中债隐含评级详细信息列表
const impliedRatingList = ref([
    { label: '评级日期', value: computed(() => bondInfo.value.latestValuationDate || '-') },
    { label: '中债隐含评级', value: computed(() => bondInfo.value.cnbdCreditrating || '-') },
    { label: '评级变动', value: computed(() => bondInfo.value.bondratingConversion || '-') }
]);

// 主体评级详细信息列表
const issuerRatingList = ref([
    { label: '评级日期', value: computed(() => bondInfo.value.latestValuationDate || '-') },
    { label: '主体评级', value: computed(() => bondInfo.value.binfoCreditrating || '-') },
    { label: '评级机构', value: computed(() => bondInfo.value.binfoCreditratingagency || '-') },
    { label: '评级变动', value: computed(() => bondInfo.value.creditratingConversion || '-') },
    { label: '评级展望', value: computed(() => bondInfo.value.creditratingConversion || '-') }
]);

// 发行人详细信息列表
const issuerInfoList = ref([
    { label: '发行人', value: computed(() => bondInfo.value.binfoIssuer || '-') },
    { label: '是否城投', value: computed(() => bondInfo.value.isWindInvest) },
    { label: '主体性质', value: computed(() => bondInfo.value.compProperty || '-') }
]);

// 切换标签页
const switchTab = (index: number) => {
    currentTab.value = index;
};

// 获取债券详情数据
const getBondDetail = async (bondId: string) => {
    try {
        const result = await getIssuanceDetail(
            {
                objectId: bondId,
                isProgram: 'Y'
            }
        );
        if (result && result.data) {
            // 更新债券信息
            Object.assign(bondInfo.value, result.data.data);
            console.log('债券详情数据:', result.data);
        }
    } catch (error) {
        console.error('获取债券详情失败:', error);
        uni.showToast({
            title: '获取数据失败',
            icon: 'none'
        });
    }
};

// 页面加载时获取参数
onLoad((options: Record<string, any> | undefined) => {
    console.log('页面参数:', options);

    if (!options) {
        console.warn('未获取到页面参数');
        return;
    }

    pageParams.value = options;

    // 如果有债券ID参数，则获取详情数据
    if (options.objectId) {
        console.log('债券ID:', options.objectId);
        getBondDetail(options.objectId);
    }
});

// 跳转到自选页面
const goToFavorite = () => {
    uni.navigateTo({
        url: '/subPageB/selfchose-management/index'
    });
};

onMounted(() => {
    // 初始化或获取数据
});
</script>

<style lang="scss" scoped>

/* 1.1 页面容器 */
.container {
    padding: 0 20rpx;
    height: 100vh;
    box-sizing: border-box;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    /* 防止整体页面滚动 */
}

/* 1.2 头部区域 */
.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

/* 1.3 内容区域 */
.content-wrapper {
    flex: 1;
    margin-top: 180rpx;
    padding-bottom: 20rpx;
    overflow: auto;
    /* 主滚动容器 */
    position: relative;
}

/* 1.4 滚动区域 */
.scrollable-content {
    height: 100%;

    ::-webkit-scrollbar {
        display: none;
        width: 0;
        height: 0;
        color: transparent;
    }
}

/* 2. 债券卡片样式 */
.bond-card {
    background-color: #FFF8EE;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
}

.bond-title {
    margin-bottom: 30rpx;
}

.bond-name {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    display: block;
}

.bond-code {
    font-size: 28rpx;
    color: #999;
    margin-top: 8rpx;
    display: block;
}

.interest-rate-container {
    text-align: center;
    margin: 40rpx 0;
}

.interest-rate {
    font-size: 60rpx;
    color: #333;
    display: block;
}

.interest-rate-label {
    font-size: 28rpx;
    color: #999;
    margin-top: 8rpx;
}

.bond-info-row {
    display: flex;
    justify-content: space-between;
    margin-top: 30rpx;
}

.info-item {
    flex: 1;
    text-align: center;
}

.info-value {
    font-size: 32rpx;
    color: #333;
    display: block;
}

.info-label {
    font-size: 24rpx;
    color: #999;
    margin-top: 8rpx;
}

/* 3. 详情卡片（包含标签页、详细信息列表和自选按钮） */
.details-card {
    background-color: #fff;
    border-radius: 20rpx;
    position: relative;
    padding-bottom: 90rpx;
    /* 为底部固定的自选按钮留出空间 */
    margin-bottom: 30rpx;
}

/* 3.1 标签页样式 */
.tabs-container {
    padding: 10rpx;
    background-color: #fff;
    border-bottom: 1px solid #f5f5f5;
}

.tabs-scroll {
    width: 100%;
    white-space: nowrap;
}

.tabs {
    display: inline-flex;
    padding: 10rpx;
    width: auto;
}

.tab-item {
    padding: 15rpx 30rpx;
    font-size: 28rpx;
    color: #666;
    position: relative;
    margin: 0 10rpx;
    border-radius: 30rpx;
    display: inline-block;
}

.tab-item.active {
    font-weight: bold;
    color: #fff;
    background-color: #FFA83C;
}

/* 3.2 详细信息列表区域（可滚动） */
.details-scroll-area {
    position: relative;
    overflow: hidden;
}

.details-scroll {
    max-height: 600rpx;
    /* 设置最大高度，超过此高度可滚动 */

    ::-webkit-scrollbar {
        display: none;
        width: 0;
        height: 0;
        color: transparent;
    }
}

.details-list {
    padding: 20rpx;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    padding: 20rpx;
}

.detail-label {
    color: #666;
    font-size: 28rpx;
}

.detail-value {
    color: #333;
    font-size: 28rpx;
}

/* 3.3 添加自选按钮（固定在卡片底部） */
.add-favorite {
    display: flex;
    // align-items: center;
    justify-content: center;
    padding: 20rpx 0;
    color: #FFA83C;
    font-size: 30rpx;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    border-radius: 0 0 20rpx 20rpx;
}

.add-icon {
    margin-right: 10rpx;
}
</style>