# uni-vue3-pinna

在Vite中使用Vue 3进行开发。

## 推荐的IDE设置

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (禁用Vetur).

## 自定义配置

See [Vite Configuration Reference](https://vitejs.dev/config/).

## 项目设置

```sh
pnpm install
```

### 编译和开发

```sh
pnpm dev
```

### 类型检查、生产编译

```sh
pnpm build
```

### Lint with [ESLint](https://eslint.org/)

```sh
pnpm lint
```
