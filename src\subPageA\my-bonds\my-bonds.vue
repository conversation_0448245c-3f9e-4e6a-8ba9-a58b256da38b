<template>
    <view class="container">
        <view class="fixed-header">
            <CustomHead :title="bondListCardTitle" />
        </view>
        <!-- 可滚动的内容区域 -->
        <view class="content-wrapper">
            <scroll-view class="scrollable-content" scroll-y :show-scrollbar="false" enhanced>
                <!-- 新券行情 -->
                <view class="market-section">
                    <view class="section-header">
                        <view class="title-wrapper">
                            <view class="title-icon"></view>
                            <text class="title-text">新券行情</text>
                        </view>
                    </view>
                    <scroll-view scroll-x class="market-scroll">
                        <view class="market-cards">
                            <view class="market-group" style="margin-right: 15rpx;"
                                v-for="(group, groupIndex) in bondGroups" :key="'group-' + groupIndex">
                                <view class="market-grid">
                                    <view class="market-card"
                                        :style="{ backgroundImage: `url('${getAssetUrl('/scard-bg.png')}')` }"
                                        v-for="(item, index) in group.slice(0, 2)"
                                        :key="'card-' + groupIndex + '-' + index" @click="goToValuationPage(item)">
                                        <view class="card-title">{{ item.sInfoName }}</view>
                                        <view class="card-date">{{ item.bIssueFirstissue }}</view>
                                        <view class="card-bottom">
                                            <text class="card-rate">{{ item.latestYtm }}%</text>
                                            <view class="card-change" :class="{ 'down': item.riseFallBp < 0 }">
                                                <template v-if="item.riseFallBp === null || item.riseFallBp === undefined || item.riseFallBp === '' || isNaN(item.riseFallBp)">
                                                    <text>--</text>
                                                </template>
                                                <template v-else>
                                                    <text>{{ item.riseFallBp > 0 ? '+' : '' }}{{ item.riseFallBp }}BP</text>
                                                    <image class="change-arrow"
                                                        :src="item.riseFallBp > 0 ? getAssetUrl('/add-red.png') : getAssetUrl('/down-green.png')"
                                                        mode="aspectFit"></image>
                                                </template>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                                <view class="market-grid">
                                    <view class="market-card"
                                        :style="{ backgroundImage: `url('${getAssetUrl('/scard-bg.png')}')` }"
                                        v-for="(item, index) in group.slice(2, 4)"
                                        :key="'card-' + groupIndex + '-' + (index + 2)"
                                        @click="goToValuationPage(item)">
                                        <view class="card-title">{{ item.sInfoName }}</view>
                                        <view class="card-date">{{ item.bIssueFirstissue }}</view>
                                        <view class="card-bottom">
                                            <text class="card-rate">{{ item.latestYtm }}%</text>
                                            <view class="card-change" :class="{ 'down': item.riseFallBp < 0 }">
                                                <template v-if="item.riseFallBp === null || item.riseFallBp === undefined || item.riseFallBp === '' || isNaN(item.riseFallBp)">
                                                    <text>--</text>
                                                </template>
                                                <template v-else>
                                                    <text>{{ item.riseFallBp > 0 ? '+' : '' }}{{ item.riseFallBp }}BP</text>
                                                    <image class="change-arrow"
                                                        :src="item.riseFallBp > 0 ? getAssetUrl('/add-red.png') : getAssetUrl('/down-green.png')"
                                                        mode="aspectFit"></image>
                                                </template>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </scroll-view>
                </view>

                <!-- 所有债券 -->
                <view class="all-bonds-section">
                    <view class="section-header">
                        <view class="title-wrapper">
                            <view class="title-icon"></view>
                            <text class="title-text">所有债券</text>
                        </view>
                        <view class="more-link" @click="goToUnderwritingDetails">
                            <text class="more-text">主承排名</text>
                            <uni-icons type="right" size="14" color="#FF8E2B"></uni-icons>
                        </view>
                    </view>

                    <!-- 搜索框 -->
                    <view class="search-box">
                        <uni-icons type="search" size="18" color="#999"></uni-icons>
                        <input type="text" v-model="searchValue" placeholder="债券简称/债券代码" @confirm="handleSearch" />
                    </view>

                    <!-- 筛选器 -->
                    <view class="filter-options">
                        <view class="filter-option" @click="showBondTypePicker">
                            <text>债券类型</text>
                            <text class="arrow">▼</text>
                        </view>
                        <view class="filter-option" @click="showStatusPicker">
                            <text>状态</text>
                            <text class="arrow">▼</text>
                        </view>
                        <view class="filter-option" @click="showInnovationPicker">
                            <text>创新专项产品</text>
                            <text class="arrow">▼</text>
                        </view>
                    </view>

                    <!-- 债券列表 - 使用SimpleTable组件 -->
                    <view class="bonds-list">
                        <SimpleTable :columns="tableColumnsConfig" :stripe="false" :data="bondTableData" :border="false"
                            :highlight="true" :loading="bondListLoading" :hasMore="hasMore"
                            :height="bondTableData.length > 0 ? '800rpx' : 'auto'" @cellClick="showBondDetailFromCell"
                            @loadMore="handleTableLoadMore" :cell-style="cellStyle" />

                        <!-- 无数据提示 -->
                        <view v-if="!bondTableData.length && !bondListLoading" class="no-data-tip">
                            <text class="no-data-text">暂无相关数据</text>
                        </view>

                        <!-- 没有更多数据提示 -->
                        <view v-if="!hasMore && bondTableData.length > 0 && !bondListLoading" class="no-more-tip">
                            <text class="no-more-text">已显示全部数据</text>
                        </view>
                    </view>
                </view>

                <!-- 底部留白区域，确保内容可以完全滚动显示 -->
                <!-- <view class="bottom-space"></view> -->
            </scroll-view>
        </view>
    </view>

    <!-- 债券类型选择弹窗 -->
    <uni-popup ref="bondTypePopup" type="bottom" :safe-area="false">
        <view class="popup-content">
            <view class="popup-header">
                <text class="popup-title">债券类型选择</text>
                <view class="close-icon" @click="closeBondTypePicker">
                    <uni-icons type="close" size="20" color="#333"></uni-icons>
                </view>
            </view>

            <view class="popup-options">
                <view class="options-header">
                    <text>默认勾选全部</text>
                    <view class="options-actions">
                        <text @click="selectAllBondTypes">全选</text>
                        <text @click="clearBondTypes">清空</text>
                        <text @click="invertBondTypes">反选</text>
                    </view>
                </view>

                <view class="options-list">
                    <view v-for="(category, categoryIndex) in bondTypeOptions" :key="categoryIndex">
                        <view class="category-title">{{ category.bondTypeName }}</view>
                        <view class="category-options">
                            <view v-for="(option, index) in category.list" :key="index" class="option-item"
                                @click="toggleBondType(option.bondTypeCode)">
                                <view class="checkbox"
                                    :class="{ checked: selectedBondTypes.includes(option.bondTypeCode) }">
                                    <uni-icons v-if="selectedBondTypes.includes(option.bondTypeCode)"
                                        type="checkmarkempty" size="14" color="#fff"></uni-icons>
                                </view>
                                <text class="option-label">{{ option.bondTypeName }}</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <view class="popup-footer">
                <button class="confirm-btn" @click="confirmBondTypeSelection">确认</button>
            </view>
        </view>
    </uni-popup>

    <!-- 状态选择弹窗 -->
    <uni-popup ref="statusPopup" type="bottom" :safe-area="false">
        <view class="popup-content">
            <view class="popup-header">
                <text class="popup-title">状态选择</text>
                <view class="close-icon" @click="closeStatusPicker">
                    <uni-icons type="close" size="20" color="#333"></uni-icons>
                </view>
            </view>

            <view class="popup-options">
                <view class="options-header">
                    <text>默认勾选全部</text>
                    <view class="options-actions">
                        <text @click="selectAllStatus">全选</text>
                        <text @click="clearStatus">清空</text>
                        <text @click="invertStatus">反选</text>
                    </view>
                </view>

                <view class="options-list">
                    <view class="category-options">
                        <view v-for="(option, index) in statusOptions" :key="index" class="option-item"
                            @click="toggleStatus(option.value)">
                            <view class="checkbox" :class="{ checked: selectedStatus.includes(option.value) }">
                                <uni-icons v-if="selectedStatus.includes(option.value)" type="checkmarkempty" size="14"
                                    color="#fff"></uni-icons>
                            </view>
                            <text class="option-label">{{ option.label }}</text>
                        </view>
                    </view>
                </view>
            </view>

            <view class="popup-footer">
                <button class="confirm-btn" @click="confirmStatusSelection">确认</button>
            </view>
        </view>
    </uni-popup>

    <!-- 创新专项产品选择弹窗 -->
    <uni-popup ref="innovationPopup" type="bottom" :safe-area="false">
        <view class="popup-content">
            <view class="popup-header">
                <text class="popup-title">创新专项产品选择</text>
                <view class="close-icon" @click="closeInnovationPicker">
                    <uni-icons type="close" size="20" color="#333"></uni-icons>
                </view>
            </view>

            <view class="popup-options">
                <view class="options-header">
                    <text>默认勾选全部</text>
                    <view class="options-actions">
                        <text @click="selectAllInnovation">全选</text>
                        <text @click="clearInnovation">清空</text>
                        <text @click="invertInnovation">反选</text>
                    </view>
                </view>

                <view class="options-list">
                    <view v-for="(category, categoryIndex) in innovationOptions" :key="categoryIndex">
                        <view class="category-title">{{ category.label }}</view>
                        <view class="category-options">
                            <view v-for="(option, index) in category.children" :key="index" class="option-item"
                                @click="toggleInnovation(option.label)">
                                <view class="checkbox" :class="{ checked: selectedInnovation.includes(option.label) }">
                                    <uni-icons v-if="selectedInnovation.includes(option.label)" type="checkmarkempty"
                                        size="14" color="#fff"></uni-icons>
                                </view>
                                <text class="option-label">{{ option.label }}</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <view class="popup-footer">
                <button class="confirm-btn" @click="confirmInnovationSelection">确认</button>
            </view>
        </view>
    </uni-popup>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import CustomHead from '@/components/head/head.vue';
import SimpleTable from '@/components/SimpleTable/index.vue';
import { getSql, getBondType } from '@/api/common';
import { getCustomListHead } from '@/api/dataDict';
import { usePermissionStore } from '@/stores/permission';
import { storeToRefs } from 'pinia';
import { getAssetUrl } from '@/config/assets';

// 使用权限store
const permissionStore = usePermissionStore();
const { bondListCardTitle } = storeToRefs(permissionStore);

// 数据部分
const bondListPage = ref(1);
const bondListLoading = ref(false);
const hasMore = ref(true);
const newBonds = ref([]);
const bondTableData = ref([]);
const bondList = ref([]);

// 修改常量定义
const pageSize = ref(20);

const tableColumns = ref([])
const tableColumnsConfig = computed(() => {
    return tableColumns.value.map((col, index) => {
        const columnConfig = {
            name: col.columnCode,  // 字段名
            label: col.columnTitle, // 显示名称
            emptyString: '--'
        };

        // 根据数据类型设置对齐方式
        if (col.columnAlign) {
            columnConfig.align = col.columnAlign;
        }

        // 第一列（债券简称）特殊处理
        if (index === 0) {
            columnConfig.fixed = true;  // 设置为固定列
            columnConfig.align = 'left';  // 债券简称左对齐
            columnConfig.width = 240;  // 设置合适的宽度
        }

        return columnConfig;
    });
});

const getCustomListHeadData = async () => {
    const params = {
        id: "2642e060df324cddbcc58b88f22b472c",
        moduleId: "1361732959698214912"
    }
    const res = await getCustomListHead(params);

    if (res.data && res.data.data && res.data.data.column) {
        const columnList = res.data.data.column;

        // 过滤出需要显示的列（defaultShow为'Y'的列）
        const visibleColumns = columnList
            .filter(col => col.defaultShow === 'Y')
            .sort((a, b) => a.order - b.order); // 按order字段排序

        // 确保债券简称字段始终作为第一列
        const bondNameColumn = visibleColumns.find(col =>
            col.columnCode === 'sInfoName' ||
            col.columnTitle === '债券简称' ||
            col.columnTitle.includes('债券简称') ||
            col.columnTitle.includes('简称')
        );

        // 移除债券简称列，然后将其插入到第一位
        let reorderedColumns = [];
        if (bondNameColumn) {
            // 从原数组中移除债券简称列
            const otherColumns = visibleColumns.filter(col => col !== bondNameColumn);
            // 将债券简称列放在第一位
            reorderedColumns = [bondNameColumn, ...otherColumns];
        } else {
            // 如果没有找到债券简称列，保持原有顺序
            reorderedColumns = visibleColumns;
        }

        tableColumns.value = reorderedColumns;
    }
};

// 获取新券行情数据（待接入）
const getNewBondsData = async () => {
    try {
        const data = {
            params: {
                ccid: "6d61d587625d429faabb52c851a1cbec",
                ownedModuleid: "708631605142536192"
            },
            page: {
                pageNo: 1,
                pageSize: 50
            }
        }
        const res = await getSql(data);
        console.log('获取新券行情数据', res.data.data.pageInfo.list);
        newBonds.value = res.data.data.pageInfo.list || [];

    } catch (error) {
        newBonds.value = [];
    }
};

// 债券类型弹窗相关
const bondTypePopup = ref(null);
const selectedBondTypes = ref([]); // 选中的债券类型代码数组
const bondTypeOptions = ref([]); // 债券类型选项数据

// 获取债权类型
const getBondTypeList = async () => {
    const res = await getBondType();
    const bondTypeList = res.data.data;

    // 处理债券类型数据，转换为分类结构
    const groupedData = [];

    // 找出所有主类型（没有bondTypeCode2或bondTypeCode2为空的项）
    const mainTypes = bondTypeList.filter(item => !item.bondTypeCode2);

    // 为每个主类型创建分类
    mainTypes.forEach(mainType => {
        const category = {
            bondTypeName: mainType.bondTypeName,
            bondTypeCode: mainType.bondTypeCode,
            list: []
        };

        // 查找该主类型下的子类型
        bondTypeList.forEach(item => {
            if (item.bondTypeCode === mainType.bondTypeCode && item.bondTypeCode2 && item.bondTypeName2) {
                category.list.push({
                    bondTypeName: item.bondTypeName2,
                    bondTypeCode: item.bondTypeCode2
                });
            }
        });

        // 只添加有子类型的分类
        if (category.list.length > 0) {
            groupedData.push(category);
        }
    });

    bondTypeOptions.value = groupedData;

    // 默认选中所有债券类型
    selectAllBondTypes();
};

// 切换债券类型选中状态
const toggleBondType = (bondTypeCode) => {
    const index = selectedBondTypes.value.indexOf(bondTypeCode);
    if (index > -1) {
        selectedBondTypes.value.splice(index, 1);
    } else {
        selectedBondTypes.value.push(bondTypeCode);
    }
};

// 全选所有债券类型
const selectAllBondTypes = () => {
    const allCodes = [];
    bondTypeOptions.value.forEach(category => {
        category.list.forEach(item => {
            allCodes.push(item.bondTypeCode);
        });
    });
    selectedBondTypes.value = allCodes;
};

// 清空债券类型选择
const clearBondTypes = () => {
    selectedBondTypes.value = [];
};

// 反选债券类型
const invertBondTypes = () => {
    const allCodes = [];
    bondTypeOptions.value.forEach(category => {
        category.list.forEach(item => {
            allCodes.push(item.bondTypeCode);
        });
    });
    selectedBondTypes.value = allCodes.filter(code => !selectedBondTypes.value.includes(code));
};

// 显示债券类型选择器
const showBondTypePicker = () => {
    bondTypePopup.value.open();
};

// 关闭债券类型选择器
const closeBondTypePicker = () => {
    bondTypePopup.value.close();
};

// 确认债券类型选择
const confirmBondTypeSelection = () => {
    closeBondTypePicker();
    // 重新查询数据，重置分页状态
    getBondList(1, false);
};

// 状态弹窗相关
const statusPopup = ref(null);
const selectedStatus = ref([]); // 修改为数组，支持多选

// 状态选项配置
const statusOptions = [
    { label: '存续', value: '存续' },
    { label: '到期', value: '到期' },
    { label: '预发行', value: '预发行' },
    { label: '发行失败', value: '发行失败' }
];

// 创新专项产品弹窗相关
const innovationPopup = ref(null);
const selectedInnovation = ref([]); // 修改为数组，支持多选

// 创新专项产品选项配置 - 改为分类结构
const innovationOptions = reactive([]);

// 获取创新专项产品数据
const getInnovationProductData = async () => {
    try {
        // 使用真实的创新专项产品数据
        const realData = [
            {
                label: "协会债",
                children: [
                    { label: "创投债" },
                    { label: "蓝色债" },
                    { label: "绿色债" },
                    { label: "转型债" },
                    { label: "可持续债" },
                    { label: "科创票据" },
                    { label: "碳中和债" },
                    { label: "永续票据" },
                    { label: "城市更新债" },
                    { label: "革命老区债" },
                    { label: "能源保供债" },
                    { label: "权益出资债" },
                    { label: "社会责任债" },
                    { label: "乡村振兴债" },
                    { label: "疫情防控债" },
                    { label: "住房租赁债" },
                    { label: "资产担保债" },
                    { label: "保障性安居工程债" },
                    { label: "不动产信托资产支持票据" }
                ]
            },
            {
                label: "公司债",
                children: [
                    { label: "扶贫债" },
                    { label: "绿色债" },
                    { label: "纾困债" },
                    { label: "大湾区债" },
                    { label: "可转换债" },
                    { label: "可持续债" },
                    { label: "可续期债" },
                    { label: "碳中和债" },
                    { label: "创新创业债" },
                    { label: "低碳转型债" },
                    { label: "科技创新债" },
                    { label: "乡村振兴债" },
                    { label: "项目收益债" },
                    { label: "一带一路债" },
                    { label: "疫情防控债" },
                    { label: "住房租赁债" },
                    { label: "支持优质企业债" }
                ]
            }
        ];

        innovationOptions.splice(0, innovationOptions.length, ...realData);

        // 初始化时保持空选择状态，不默认选中任何选项
        selectedInnovation.value = [];

    } catch (error) {
        // 删除了 console.error('获取创新专项产品数据失败', error);
    }
};

// 承销明细
const goToUnderwritingDetails = () => {
    uni.navigateTo({
        url: '/subPageB/underwriting-details/underwriting-details'
    });
};

const searchValue = ref('');
const handleSearch = () => {
    getBondList(1, false);
};
const getBondList = async (page = 1, append = false) => {
    // 如果是新查询，重置分页状态
    if (!append) {
        bondListPage.value = 1;
        hasMore.value = true;
        bondListLoading.value = true;
    }

    const data = {
        params: {
            ownedModuleid: "708631605142536192",
            ccid: "2642e060df324cddbcc58b88f22b472c",
            flag: "",
            lastissue: [],
            maturitydate: [],
            issueState: selectedStatus.value,
            bondtypes: selectedBondTypes.value,
            issuers: selectedInnovation.value,
            remainTerm: [],
            windcode: searchValue.value,
            lastissueStart: "",
            lastissueEnd: "",
            maturitydateStart: "",
            maturitydateEnd: "",
            analyseDate: ""
        },
        page: {
            pageNo: page,
            pageSize: pageSize.value,
            sort: null,
            direction: null
        }
    };
    try {
        const res = await getSql(data);

        if (res.data && res.data.data && res.data.data.pageInfo) {
            // 处理债券列表数据
            const apiData = res.data.data.pageInfo.list;

            // 保存原始数据
            if (append) {
                bondList.value = [...bondList.value, ...apiData];
                bondTableData.value = [...bondTableData.value, ...apiData];
            } else {
                bondList.value = apiData || [];
                bondTableData.value = apiData || [];
            }

            // 检查是否还有更多数据
            if (!apiData || apiData.length < pageSize.value) {
                hasMore.value = false;
            }
        }
    } catch (error) {
        // 删除了 console.error('获取债券列表失败', error);
        if (!append) {
            bondTableData.value = [];
            hasMore.value = false;
        }
        uni.showToast({
            title: '获取债券列表失败',
            icon: 'none'
        });
    } finally {
        if (!append) {
            bondListLoading.value = false;
        }
    }
};

const bondGroups = computed(() => {
    // 将债券数据按每4个一组进行分组
    const groups = [];
    for (let i = 0; i < newBonds.value.length; i += 4) {
        groups.push(newBonds.value.slice(i, i + 4));
    }
    return groups;
});

// 单元格点击显示债券详情
const showBondDetailFromCell = (row, column, rowIndex, colIndex) => {
    // 实现跳转到债券详情页的逻辑
    uni.navigateTo({
        url: `/subPageA/bond-detail/index?objectId=${row.objectId}`,
    });
};

// 定义cellStyle函数
const cellStyle = ({ row, column, rowIndex, columnIndex }) => {
    // 首列特殊处理：宽度自适应，显示完整文字
    if (columnIndex === 0) {
        return {
            width: 'auto',
            minWidth: '200rpx',
            whiteSpace: 'normal',
            overflow: 'visible',
            textOverflow: 'unset',
            wordBreak: 'break-word',
            lineHeight: '1.4'
        };
    }
    return {};
};

const loadMoreBondData = async () => {
    // 如果已经在加载中或没有更多数据，则不重复加载
    if (bondListLoading.value || !hasMore.value) return;

    // 设置加载中状态
    bondListLoading.value = true;

    try {
        // 调用真实的API来获取更多数据
        await getBondList(bondListPage.value + 1, true);
        bondListPage.value++;
    } catch (error) {
        // 删除了 console.error('加载更多数据失败', error);
    } finally {
        // 加载完成
        bondListLoading.value = false;
    }
};

// 表格加载更多回调
const handleTableLoadMore = () => {
    if (!bondListLoading.value && hasMore.value) {
        loadMoreBondData();
    }
};

// 显示状态选择器
const showStatusPicker = () => {
    statusPopup.value.open();
};

// 关闭状态选择器
const closeStatusPicker = () => {
    statusPopup.value.close();
};

// 切换状态选项选中状态（多选）
const toggleStatus = (value) => {
    const index = selectedStatus.value.indexOf(value);
    if (index > -1) {
        selectedStatus.value.splice(index, 1);
    } else {
        selectedStatus.value.push(value);
    }
};

// 全选状态选项
const selectAllStatus = () => {
    selectedStatus.value = statusOptions.map(option => option.value);
};

// 清空状态选项
const clearStatus = () => {
    selectedStatus.value = [];
};

// 反选状态选项
const invertStatus = () => {
    const allValues = statusOptions.map(option => option.value);
    selectedStatus.value = allValues.filter(value => !selectedStatus.value.includes(value));
};

// 确认状态选择
const confirmStatusSelection = () => {
    closeStatusPicker();
    // 重新查询数据，重置分页状态
    getBondList(1, false);
};

// 显示创新专项产品选择器
const showInnovationPicker = () => {
    innovationPopup.value.open();
};

// 关闭创新专项产品选择器
const closeInnovationPicker = () => {
    innovationPopup.value.close();
};

// 切换创新专项产品选中状态
const toggleInnovation = (label) => {
    const index = selectedInnovation.value.indexOf(label);
    if (index > -1) {
        selectedInnovation.value.splice(index, 1);
    } else {
        selectedInnovation.value.push(label);
    }
};

// 获取所有创新专项产品选项的标签
const getAllInnovationLabels = () => {
    const allLabels = [];
    innovationOptions.forEach(category => {
        category.children.forEach(option => {
            allLabels.push(option.label);
        });
    });
    return allLabels;
};

// 全选创新专项产品
const selectAllInnovation = () => {
    selectedInnovation.value = getAllInnovationLabels();
};

// 清空创新专项产品选择
const clearInnovation = () => {
    selectedInnovation.value = [];
};

// 反选创新专项产品
const invertInnovation = () => {
    const allLabels = getAllInnovationLabels();
    selectedInnovation.value = allLabels.filter(label => !selectedInnovation.value.includes(label));
};

// 确认创新专项产品选择
const confirmInnovationSelection = () => {
    closeInnovationPicker();
    // 重新查询数据，重置分页状态
    getBondList(1, false);
};

// 生命周期钩子
onMounted(async () => {
    try {
        // 并行获取表头和筛选条件数据
        await Promise.all([
            getCustomListHeadData(),
            getBondTypeList(),
            getInnovationProductData(),
            getNewBondsData() // 添加新券行情数据获取
        ]);

        // 初始化状态选项 - 默认全选
        selectAllStatus();

        // 所有初始化完成后再获取债券列表数据
        getBondList(1, false);

    } catch (error) {
        // 删除了 console.error('页面初始化失败', error);
        // 即使出错也尝试获取债券列表
        getBondList(1, false);
    }
});

// 新增方法
const goToValuationPage = (item) => {
    console.log('item', item);
    // 实现跳转到估值行情页面的逻辑
    uni.navigateTo({
        url: `/subPageB/valuation-market/valuation-market?params=${encodeURIComponent(JSON.stringify(item))}`
    });
};
</script>

<style lang="scss" scoped>
.container {
    padding: 0 20rpx;
    height: 100vh;
    box-sizing: border-box;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;

    /* 1.2 头部区域 */
    .fixed-header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
    }

    /* 1.3 内容区域 */
    .content-wrapper {
        flex: 1;
        position: absolute;
        /* 绝对定位 */
        top: 180rpx;
        /* 头部区域的高度 */
        left: 20rpx;
        right: 20rpx;
        bottom: 0;
        /* 扩展到容器底部 */
        overflow: hidden;
    }

    /* 1.4 滚动区域 */
    .scrollable-content {
        height: 100%;
        /* 占满内容区域 */
        width: 100%;
        padding: 10rpx 0 0;

        ::-webkit-scrollbar {
            display: none;
            width: 0;
            height: 0;
            color: transparent;
        }
    }

    /* 1.5 底部留白 */
    .bottom-space {
        height: 100rpx;
        width: 100%;
    }

    // 新券行情部分
    .market-section {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 30rpx 0;
        margin-bottom: 20rpx;
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 30rpx 20rpx;

            .title-wrapper {
                display: flex;
                align-items: center;

                .title-icon {
                    width: 48rpx;
                    height: 52rpx;
                    position: relative;
                    top: -10rpx;

                    &::before {
                        content: '';
                        position: absolute;
                        inset: 0;
                        background: linear-gradient(135deg, rgba(133, 111, 254, 0.6) 0%, rgba(255, 237, 221, 0) 100%);
                        clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
                    }
                }

                .title-text {
                    font-size: 32rpx;
                    font-weight: bold;
                    color: #333;
                    transform: translateX(-25rpx);
                }
            }
        }

        .market-scroll {
            width: 100%;
            overflow-x: auto;
        }

        .market-cards {
            display: flex;
            white-space: nowrap;
            padding: 5rpx 0;
            padding-left: 20rpx;

            .market-group {
                display: inline-block;
                width: 690rpx;

                &:last-child {
                    margin-right: 0;
                }
            }

            .market-grid {
                display: flex;
                justify-content: space-between;
                /* 确保左右两边的卡片平均分布 */
                margin-bottom: 15rpx;
                width: 100%;
                /* 确保宽度占满 */

                &:last-child {
                    margin-bottom: 0;
                }
            }

            .market-card {
                width: 330rpx;
                /* 稍微减小宽度以确保两个卡片有足够的间距 */
                height: auto;
                background-color: #FFF7ED;
                background-size: 100% 100%;
                background-repeat: no-repeat;
                border-radius: 12rpx;
                padding: 20rpx;
                box-sizing: border-box;
                /* 确保padding不会增加元素宽度 */
                margin-right: 15rpx;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                flex-shrink: 0;
                box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
                /* 防止卡片被压缩 */

                &:last-child {
                    margin-right: 0;
                    /* 最后一个卡片不需要右边距 */
                }

                .card-title {
                    font-size: 28rpx;
                    color: #000;
                    font-weight: 500;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .card-date {
                    font-size: 26rpx;
                    color: #999;
                    margin-top: 10rpx;
                }

                .card-bottom {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-end;
                    margin-top: 20rpx;
                }

                .card-rate {
                    font-size: 26rpx;
                    color: #999;
                    font-weight: 500;
                }

                .card-change {
                    font-size: 26rpx;
                    color: #E76056;
                    display: flex;
                    align-items: center;

                    &.down {
                        color: #62BB37;
                    }

                    .change-arrow {
                        margin-left: 5rpx;
                        width: 28rpx;
                        height: 28rpx;
                    }
                }
            }
        }
    }

    // 所有债券部分
    .all-bonds-section {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 30rpx 0;
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 30rpx 20rpx;

            .title-wrapper {
                display: flex;
                align-items: center;

                .title-icon {
                    width: 48rpx;
                    height: 52rpx;
                    position: relative;
                    top: -10rpx;

                    &::before {
                        content: '';
                        position: absolute;
                        inset: 0;
                        background: linear-gradient(135deg, rgba(133, 111, 254, 0.6) 0%, rgba(255, 237, 221, 0) 100%);
                        clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
                    }
                }

                .title-text {
                    font-size: 32rpx;
                    font-weight: bold;
                    color: #333;
                    transform: translateX(-25rpx);
                }
            }

            .more-link {
                display: flex;
                align-items: center;
                color: #FF8E2B;

                .more-text {
                    font-size: 28rpx;
                    margin-right: 10rpx;
                }
            }
        }

        // 搜索框
        .search-box {
            display: flex;
            align-items: center;
            background-color: #f5f5f5;
            border-radius: 8rpx;
            padding: 15rpx 20rpx;
            margin: 20rpx 30rpx;
            border-radius: 50rpx;

            uni-icons {
                margin-right: 10rpx;
            }

            input {
                flex: 1;
                font-size: 26rpx;
            }
        }

        // 筛选器
        .filter-options {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20rpx;
            padding: 10rpx 30rpx;

            .filter-option {
                display: flex;
                align-items: center;
                justify-content: center;

                &:last-child {
                    margin-right: 0;
                }

                text {
                    font-size: 24rpx;
                    color: #666;
                    margin-right: 10rpx;
                }

                .arrow {
                    margin-left: 8rpx;
                    font-size: 20rpx;
                    color: #696969;
                }
            }
        }

        // 债券列表 - 使用SimpleTable组件
        .bonds-list {
            padding: 10rpx 20rpx;
            position: relative;
        }
    }
}

.rotate-180 {
    transform: rotate(180deg);
}

/* 无数据提示样式 */
.no-data-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80rpx 0 60rpx 0;

    .no-data-text {
        font-size: 28rpx;
        color: #999999;
    }
}

/* 没有更多数据提示 */
.no-more-tip {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx 0;

    .no-more-text {
        font-size: 24rpx;
        color: #c0c4cc;
        position: relative;

        &::before,
        &::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 60rpx;
            height: 1rpx;
            background-color: #e4e7ed;
        }

        &::before {
            left: -80rpx;
        }

        &::after {
            right: -80rpx;
        }
    }
}

/* 弹窗样式 */
.popup-content {
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    padding: 30rpx 30rpx 0;
    max-height: 70vh;
    display: flex;
    flex-direction: column;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 30rpx;
    border-bottom: 2rpx solid #F5F5F5;

    .popup-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
    }

    .close-icon {
        padding: 10rpx;
    }
}

.popup-options {
    flex: 1;
    overflow-y: auto;
    padding: 20rpx 0;
    max-height: 50vh;
}

.options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;

    text {
        font-size: 26rpx;
        color: #999;
    }

    .options-actions {
        display: flex;
        gap: 30rpx;

        text {
            color: #FF8E2B;
            font-size: 26rpx;

            &:active {
                opacity: 0.8;
            }
        }
    }
}

.options-list {
    padding: 20rpx 0;

    .category-title {
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
        padding: 20rpx 0 10rpx;
        border-bottom: 1rpx solid #f0f0f0;
        margin-bottom: 10rpx;
    }

    .category-options {
        display: flex;
        flex-wrap: wrap;
    }

    .option-item {
        display: flex;
        align-items: center;
        padding: 20rpx 0;
        width: 50%;
        flex-shrink: 0;

        .checkbox {
            width: 36rpx;
            height: 36rpx;
            border-radius: 6rpx;
            border: 2rpx solid #DCDFE6;
            margin-right: 20rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;

            &.checked {
                background-color: #FF8E2B;
                border-color: #FF8E2B;
            }
        }

        .option-label {
            font-size: 28rpx;
            color: #333;
            flex: 1;
        }
    }
}

.popup-footer {
    padding: 30rpx;
    border-top: 2rpx solid #F5F5F5;

    .confirm-btn {
        width: 100%;
        height: 88rpx;
        background: #FF8E2B;
        border-radius: 44rpx;
        color: #FFFFFF;
        font-size: 32rpx;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;

        &:active {
            opacity: 0.9;
        }
    }
}

/* 单选框样式 */
.radio {
    width: 36rpx;
    height: 36rpx;
    border-radius: 50%;
    border: 1rpx solid #ddd;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 20rpx;

    &.checked {
        border-color: #FF8E2B;
    }

    .radio-inner {
        width: 20rpx;
        height: 20rpx;
        border-radius: 50%;
        background-color: #FF8E2B;
    }
}
</style>