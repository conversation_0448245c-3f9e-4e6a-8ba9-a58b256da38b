<template>
	<view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
		<!-- 顶部标题 -->
		<CustomHead title="注册进度" />

		<!-- 1. 顶部tab切换 -->
		<view class="tab-container">
			<view class="tab-item" :class="{ active: activeTab === 'association' }" @click="switchTab('association')">
				协会债
			</view>
			<view class="tab-item" :class="{ active: activeTab === 'corporate' }" @click="switchTab('corporate')">公司债
			</view>
		</view>

		<!-- 2. 卡片列表 -->
		<scroll-view scroll-y class="card-list" @scrolltolower="loadMore" @refresherpulling="onPulling"
			@refresherrefresh="onRefresh" @refresherrestore="onRestore" @refresherabort="onAbort"
			:refresher-enabled="true" :refresher-triggered="isRefreshing" :show-scrollbar="false">
			<!-- 无数据提示 -->
			<view class="no-data-container" v-if="noData">
				<text class="no-data-text">暂无数据</text>
			</view>

			<!-- 卡片内容 -->
			<view class="card-item" v-for="(item, index) in cardList" :key="index" v-else
				@click="showProgressDetail(item)">
				<view class="card-title">{{ item.b_info_fullname }}</view>

				<view class="card-info-row">
					<view class="card-info-col left">
						<view class="card-amount number-font">{{ formatDecimal(item.amount) || '0.0000' }}</view>
						<view class="card-label">金额(亿)</view>
					</view>
					<view class="card-info-col center">
						<view class="card-date number-font">{{ item.annDate }}</view>
						<view class="card-label">更新日期</view>
					</view>
					<view class="card-info-col right">
						<view class="card-status" :class="{
							'status-feedback': item.progress === '已通过发审会',
							'status-meeting': item.progress === '已上会'
						}">{{ item.progress }}</view>
						<view class="card-label">项目状态</view>
					</view>
				</view>

				<view class="card-detail-row">
					<!-- 公司债显示交易所 -->
					<view class="card-detail-item" v-if="activeTab === 'corporate'">
						<view class="card-detail-label">交易所</view>
						<view class="card-detail-value">{{ item.exchMarket }}</view>
					</view>
					<view class="card-detail-item">
						<view class="card-detail-label">项目类型</view>
						<view class="card-detail-value">{{ item.bondType }}</view>
					</view>
					<view class="card-detail-item">
						<view class="card-detail-label">管理人/主承销商</view>
						<view class="card-detail-value">{{ item.leadUnderwriter }}</view>
					</view>
				</view>
			</view>

			<!-- 加载更多提示 -->
			<view class="loading-more" v-if="isLoadingMore && !noData">加载中...</view>
			<view class="no-more" v-if="noMoreData && !noData">没有更多数据了</view>
		</scroll-view>

		<!-- 3. 进度详情弹窗 -->
		<view class="progress-modal" v-if="showModal">
			<view class="modal-mask" @click="closeModal"></view>
			<view class="modal-content">
				<view class="modal-close" @click="closeModal">
					<uni-icons type="close" size="20" color="#999"></uni-icons>
				</view>
				<view class="modal-title">{{ currentItem.bInfoFullname||currentItem.bondFullName }}</view>

				<!-- 进度时间线 -->
				<view class="timeline-container">
					<!-- 无数据缺省状态 -->
					<view class="no-progress-data" v-if="!progressSteps.length">
						<text class="no-data-text">暂无进度数据</text>
					</view>
					
					<!-- 有数据时显示时间线 -->
					<template v-else>
						<view class="timeline-item" v-for="(step, idx) in progressSteps" :key="idx"
							:class="{ 'active': idx == 0}">
							<view class="timeline-dot" :class="{ 'active-dot': idx == 0 }"></view>
							<view class="timeline-line" v-if="idx < progressSteps.length - 1"></view>
							<view class="timeline-content">
								<view class="timeline-date number-font" :class="{ 'active-date': idx == 0 }">{{ step.updateDate
								}}</view>
								<view class="timeline-status" :class="{ 'active-status': idx == 0 }">
									{{ step.statusName }}
								</view>
							</view>
						</view>
					</template>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import CustomHead from '@/components/head/head.vue';
import { getRegisterProgressList, getRegisterProgress, } from '@/api/registerAbove';
import { usePermissionStore } from '@/stores/permission';
import { getAssetUrl } from '@/config/assets';

// 使用permission store
const permissionStore = usePermissionStore();

// 当前激活的标签页
const activeTab = ref('association'); // 默认显示协会债

// 协会债ccid
const associationCcid = ref("3aed47702325497eb8667f81f61a315c");
// 公司债ccid
const corporateCcid = ref("051e23932f3e46f3946191431b8f1538");
// 当前使用的ccid
const currentCcid = ref(associationCcid.value);

// 卡片列表数据
const cardList = ref([]);
const pageNo = ref(1);
const pageSize = ref(10);
const noMoreData = ref(false);
const isRefreshing = ref(false);
const isLoadingMore = ref(false);
const noData = ref(false); // 无数据标志

// 进度详情弹窗
const showModal = ref(false);
const currentItem = ref({});
const progressSteps = ref([]);
// 格式化数值方法 - 保留小数点后四位
const formatDecimal = (value) => {
	// 如果值为null、undefined或空字符串，返回默认值
	if (value === null || value === undefined || value === '') {
		return '0.0000';
	}
	
	// 如果是字符串类型，先去除前后空格
	if (typeof value === 'string') {
		value = value.trim();
		// 如果去除空格后为空字符串，返回默认值
		if (value === '') {
			return '0.0000';
		}
	}
	
	// 转换为数字类型
	const num = Number(value);
	
	// 如果转换失败（NaN），返回默认值
	if (isNaN(num)) {
		return '0.0000';
	}
	
	// 保留四位小数，不足时自动补0
	return num.toFixed(4);
};
// 获取注册进度列表
const getRegisterProgressListData = () => {
    uni.showLoading({
        title: '加载中',
        mask: true
    });
	const data = {
		params: {
			// ownedModuleid: "1362361842164305920",
			ownedModuleid: "1362361842164305920",
			ccid: currentCcid.value
		},
		page: {
			pageNo: pageNo.value,
			pageSize: pageSize.value,
		}
	};
	
	getRegisterProgressList(data)
		.then(res => {
			uni.hideLoading();
			const list = res.data.data.pageInfo.list || [];
			
			if (pageNo.value === 1) {
				cardList.value = list;
				// 检查是否没有数据
				noData.value = list.length === 0;
			} else {
				cardList.value = [...cardList.value, ...list];
			}
			
			// 判断是否有更多数据
			const hasNextPage = res.data.data.pageInfo.nextPage !== 0;
			noMoreData.value = !hasNextPage;
			isLoadingMore.value = false;
			isRefreshing.value = false;
		})
		.catch(err => {
			uni.hideLoading();
			isLoadingMore.value = false;
			isRefreshing.value = false;
			noData.value = true;
		});
};

// 切换标签页
const switchTab = (tab) => {
	activeTab.value = tab;
	currentCcid.value = tab === 'association' ? associationCcid.value : corporateCcid.value;
	// 切换标签时重新加载数据
	resetCardList();
	getRegisterProgressListData();
};

// 显示进度详情
const showProgressDetail = (item) => {
    console.log('showProgressDetail', item);
	currentItem.value = item;
	progressSteps.value = []; // 清空之前的数据
	showModal.value = true;
	
    
	// 获取详情数据
	getRegisterProgress({
		bInfoFullname: item.bInfoFullname||item.bondFullName,
		sInfoCompcode: permissionStore.getUserInfo.outCompCode
	}).then(res => {
		progressSteps.value = res.data.data || [];
	}).catch(err => {
		// 处理错误情况
	});
};

// 关闭弹窗
const closeModal = () => {
	showModal.value = false;
};

// 重置列表
const resetCardList = () => {
	cardList.value = [];
	pageNo.value = 1;
	noMoreData.value = false;
	noData.value = false;
};

// 上拉加载更多
const loadMore = () => {
	if (noMoreData.value || isLoadingMore.value) return;
	
	isLoadingMore.value = true;
	pageNo.value += 1;
	getRegisterProgressListData();
};

// 下拉刷新相关函数
const onPulling = () => {
	// 下拉刷新触发中
};

const onRefresh = () => {
	isRefreshing.value = true;
	resetCardList();
	getRegisterProgressListData();
};

const onRestore = () => {
	// 下拉刷新恢复
};

const onAbort = () => {
	// 下拉刷新中止
};

// 页面加载时获取数据
onMounted(() => {
	// 默认加载协会债数据
	getRegisterProgressListData();
});
</script>

<style lang="scss" scoped>
.container {
	padding: 0 20rpx;
	background: linear-gradient(to bottom, #ffeac3, #fff);
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	height: 100vh;
	display: flex;
	flex-direction: column;
}

// 顶部标题样式
.title {
	font-size: 36rpx;
	font-weight: bold;
	padding: 30rpx 0;
	text-align: center;
}

// 1. 顶部tab切换样式
.tab-container {
	display: flex;
	margin-bottom: 20rpx;
	border-radius: 10rpx;

	.tab-item {
		flex: 1;
		padding: 30rpx 0;
		text-align: center;
		position: relative;
		font-size: 32rpx;
		color: #999;
		transition: all 0.3s ease;

		&.active {
			color: #FF9900;
			font-weight: bold;

			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 40%;
				height: 6rpx;
				background-color: #FF9900;
				border-radius: 6rpx 6rpx 0 0;
			}
		}
	}
}

// 2. 卡片列表样式
.card-list {
	flex: 1;
	height: 0;

	/* 隐藏滚动条 */
	scrollbar-width: none;
	/* Firefox */
	-ms-overflow-style: none;
	/* IE and Edge */

	/* WebKit浏览器 */
	&::-webkit-scrollbar {
		display: none;
		width: 0;
		height: 0;
	}
}

/* 针对小程序平台的强化处理 */
/* #ifdef MP-WEIXIN */
.card-list {
	-webkit-overflow-scrolling: touch;
}

.card-list ::-webkit-scrollbar {
	scrollbar-width: none;
	display: none;
	width: 0;
	height: 0;
}

/* #endif */

.card-item {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0rpx 14rpx 28rpx 0rpx rgba(219, 219, 219, 0.48);
}

.card-title {
	font-size: 32rpx;
	// font-weight: bold;
	line-height: 1.4;
	margin-bottom: 30rpx;
}

.card-info-row {
	display: flex;
	align-items: flex-end;
	justify-content: space-between;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 1px solid #f0f0f0;
}

.card-info-col {
	flex: 1;
	display: flex;
	flex-direction: column;

	&.left {
		align-items: flex-start;
	}

	&.center {
		align-items: flex-start;
	}

	&.right {
		align-items: flex-end;
	}
}

.card-amount {
	font-size: 44rpx;
	font-weight: bold;
	color: #333;
	line-height: 1.2;
}

.card-date {
	font-size: 32rpx;
	color: #333;
	line-height: 1.2;
}

.card-status {
	font-size: 32rpx;
	line-height: 1.2;
}

.status-feedback {
	color: #FF9900;
	font-weight: bold;
}

.status-meeting {
	color: #FF9900;
	font-weight: bold;
}

.card-label {
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
}

.card-detail-row {
	padding-top: 20rpx;
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.card-detail-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.card-detail-label {
	color: #999;
	font-size: 28rpx;
}

.card-detail-value {
	color: #333;
    max-width:400rpx;
	font-size: 28rpx;
	text-align: right;
	font-weight: 500;
}

// 加载提示样式
.loading-more,
.no-more {
	text-align: center;
	padding: 20rpx 0;
	color: #999;
	font-size: 26rpx;
}

// 无数据样式
.no-data-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
}

.no-data-image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 20rpx;
}

.no-data-text {
	font-size: 28rpx;
	color: #999;
}

// 3. 弹窗样式
.progress-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.modal-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
	position: relative;
	width: 85%;
	max-height: 80vh;
	background-color: #fff;
	border-radius: 20rpx;
	padding: 40rpx 30rpx;
	z-index: 1000;
	overflow-y: auto;
}

.modal-close {
	position: absolute;
	top: 15rpx;
	right: 15rpx;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1001;
}

.close-icon {
	font-size: 48rpx;
	color: #999;
}

.modal-title {
	font-size: 36rpx;
	font-weight: bold;
	text-align: left;
	margin-bottom: 40rpx;
	padding-right: 40rpx;
}

// 时间线样式
.timeline-container {
	position: relative;
}

.timeline-item {
	display: flex;
	position: relative;
	padding-bottom: 60rpx;
}

.timeline-item:last-child {
	padding-bottom: 0;
}

.timeline-line {
	position: absolute;
	left: 12rpx;
	top: 34rpx;
	bottom: 0;
	width: 0;
	border-left: 2rpx dashed #ddd;
	z-index: 1;
}

.timeline-dot {
	width: 24rpx;
	height: 24rpx;
	border-radius: 50%;
	background-color: #ccc;
	margin-top: 10rpx;
	z-index: 2;
	position: relative;
	left: 0;
}

.active-dot {
	background-color: #FF9900;
	position: relative;
	width: 36rpx;
	height: 36rpx;
	margin-top: 5rpx;
	margin-left: -6rpx;
	/* 调整位置，使大点与小点中心对齐 */
	display: flex;
	align-items: center;
	justify-content: center;

	&::before {
		content: '✓';
		color: white;
		font-size: 24rpx;
		font-weight: bold;
		line-height: 1;
	}

	&::after {
		content: '';
		position: absolute;
		width: 50rpx;
		height: 50rpx;
		border-radius: 50%;
		border: 2rpx solid #FF9900;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}
}

.timeline-content {
	margin-left: 30rpx;
	flex: 1;
	display: flex;
	justify-content: space-between;
}

.timeline-date {
	font-size: 30rpx;
	color: #999;
	margin-bottom: 6rpx;
}

.active-date {
	color: #FF9900;
	font-weight: bold;
}

.timeline-status {
	font-size: 32rpx;
	color: #999;
}

.active-status {
	color: #FF9900;
	font-weight: bold;
}

// 无数据缺省状态样式
.no-progress-data {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx 0;
}

.no-progress-data .no-data-image {
	width: 160rpx;
	height: 160rpx;
	margin-bottom: 20rpx;
}

.no-progress-data .no-data-text {
	font-size: 28rpx;
	color: #999;
}
</style>
