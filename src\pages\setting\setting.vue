<template>
	<view class="setting-container">
		<!-- 验证当前手机号界面 -->
		<view v-if="!verifySuccess">
			<view class="title">验证当前登录账号的手机号</view>
			<view class="subtitle">需要验证您的手机号进行下一步操作</view>
			<view class="phone-number">{{ phone }}</view>
			<view class="verification-code-wrapper">
				<input class="code-input" type="text" placeholder="请输入短信验证码" v-model="code" maxlength="6" />
				<text class="get-code-button" 
					:class="{ disabled: countdown > 0 }" 
					@tap="showCaptcha('current')">
					{{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
				</text>
			</view>
			<view class="next-button" @tap="handleNext">下一步</view>
		</view>
		
		<!-- 关联新手机号界面 -->
		<view v-else>
			<view class="title" style="margin-bottom: 70rpx;">您可关联新的手机号</view>
			<view class="input-wrapper">
				<input class="full-input" type="number" placeholder="请输入新手机号" v-model="newPhone" maxlength="11" />
			</view>
			<view class="verification-code-wrapper">
				<input class="code-input" type="number" placeholder="请输入短信验证码" v-model="newPhoneCode" maxlength="6" />
				<text class="get-code-button" 
					:class="{ disabled: newPhoneCountdown > 0 || !isValidNewPhone }" 
					@tap="showCaptcha('new')">
					{{ newPhoneCountdown > 0 ? `${newPhoneCountdown}s` : '获取验证码' }}
				</text>
			</view>
			<view class="next-button" @tap="handleConfirm">确定</view>
		</view>

		<!-- 滑块验证组件 -->
		<SlideCaptcha 
			:visible="captchaVisible" 
			@close="captchaVisible = false"
			@success="onCaptchaSuccess"
		/>
	</view>
</template>

<script setup>
	import { ref, computed } from 'vue';
	import SlideCaptcha from '@/components/SlideCaptcha/SlideCaptcha.vue';
	
    const user = uni.getStorageSync('mpUserInfo');
	// 通用响应式数据
	const captchaVisible = ref(false);
	const verifyType = ref(''); // 用于区分是哪个界面的验证码请求
	
	// 验证当前手机号数据
	const code = ref('');
	const countdown = ref(0);
	let timer = null;
	const phone = ref(user.mobile); // 当前页面显示的手机号
	const verifySuccess = ref(false); // 控制界面显示
	
	// 关联新手机号数据
	const newPhone = ref('');
	const newPhoneCode = ref('');
	const newPhoneCountdown = ref(0);
	let newPhoneTimer = null;
	
	// 计算属性：判断新手机号是否有效
	const isValidNewPhone = computed(() => {
		return /^1[3-9]\d{9}$/.test(newPhone.value);
	});

	// 显示滑块验证，type参数用于标识是哪个界面的验证码请求
	const showCaptcha = (type) => {
		// 如果是新手机号验证，先检查新手机号是否有效
		if (type === 'new') {
			// 如果正在倒计时则不处理
			if (newPhoneCountdown.value > 0) {
				return;
			}
			
			// 检查手机号是否有效
			if (!isValidNewPhone.value) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				});
				return;
			}
		} else {
			// 当前手机号验证，如果正在倒计时则不处理
			if (countdown.value > 0) {
				return;
			}
		}
		
		console.log('显示验证码弹窗，类型：', type);
		// 设置验证类型
		verifyType.value = type;
		// 显示滑块验证
		captchaVisible.value = true;
	};

	// 滑块验证成功回调
	const onCaptchaSuccess = () => {
		// 关闭验证弹窗
		captchaVisible.value = false;
		
		// 根据验证类型发送不同的验证码
		if (verifyType.value === 'new') {
			sendNewPhoneVerificationCode();
		} else {
			sendVerificationCode();
		}
	};

	// 当前手机号相关方法
	const startCountdown = () => {
		countdown.value = 60;
		timer = setInterval(() => {
			countdown.value--;
			if (countdown.value <= 0) {
				clearInterval(timer);
			}
		}, 1000);
	};

	const sendVerificationCode = () => {
		// 这里添加获取验证码的逻辑
		uni.showToast({
			title: '验证码已发送',
			icon: 'none'
		});
		
		// 开始倒计时
		startCountdown();
	};
	
	const handleNext = () => {
		if (!code.value) {
			uni.showToast({
				title: '请输入验证码',
				icon: 'none'
			});
			return;
		}
		
		if (code.value.length !== 6) {
			uni.showToast({
				title: '验证码应为6位数字',
				icon: 'none'
			});
			return;
		}
		
		// 这里添加验证逻辑，验证成功后切换到下一个界面
		uni.showToast({
			title: '验证成功',
			icon: 'success'
		});
		
		// 延迟切换界面，让toast显示完成
		setTimeout(() => {
			verifySuccess.value = true;
		}, 1000);
	};
	
	// 关联新手机号相关方法
	const startNewPhoneCountdown = () => {
		newPhoneCountdown.value = 60;
		newPhoneTimer = setInterval(() => {
			newPhoneCountdown.value--;
			if (newPhoneCountdown.value <= 0) {
				clearInterval(newPhoneTimer);
			}
		}, 1000);
	};
	
	const sendNewPhoneVerificationCode = () => {
		// 发送验证码
		uni.showToast({
			title: '验证码已发送',
			icon: 'none'
		});
		
		// 开始倒计时
		startNewPhoneCountdown();
	};
	
	const handleConfirm = () => {
		// 校验新手机号
		if (!isValidNewPhone.value) {
			uni.showToast({
				title: '请输入正确的手机号',
				icon: 'none'
			});
			return;
		}
		
		// 校验验证码
		if (!newPhoneCode.value || newPhoneCode.value.length !== 6) {
			uni.showToast({
				title: '请输入正确的验证码',
				icon: 'none'
			});
			return;
		}
		
		// 关联新手机号
		uni.showToast({
			title: '关联成功',
			icon: 'success'
		});
		
		// 延迟跳转或返回
		setTimeout(() => {
			uni.navigateBack();
		}, 1500);
	};
</script>

<style lang="scss" scoped>
.setting-container {
	padding: 50rpx 40rpx;
	display: flex;
	flex-direction: column;
	font-family: PingFang SC, Helvetica Neue, Arial, sans-serif;
	background-color: #F5F5F5;
	min-height: 100vh;

	.title {
		font-size: 38rpx;
		font-weight: 600;
		color: #000;
		margin-bottom: 16rpx;
	}

	.subtitle {
		font-size: 28rpx;
		color: #999;
		margin-bottom: 80rpx;
	}

	.phone-number {
		font-size: 44rpx;
		color: #000;
		margin-bottom: 20rpx;
		font-weight: 500;
		letter-spacing: 2rpx;
	}
	
	.input-wrapper {
		height: 100rpx;
		border-radius: 8rpx;
		margin-bottom: 45rpx;
		background-color: #fff;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
		
		.full-input {
			height: 100%;
			width: 100%;
			padding: 0 30rpx;
			font-size: 28rpx;
			box-sizing: border-box;
			
			&::placeholder {
				color: #999;
			}
		}
	}

	.verification-code-wrapper {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 100rpx;
		border-radius: 8rpx;
		margin-bottom: 45rpx;
		background-color: #fff;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);

		.code-input {
			flex: 1;
			height: 100rpx;
			padding: 0 30rpx;
			font-size: 28rpx;
			color: #333;
			border: none;
			outline: none;
			background-color: transparent;
			box-sizing: border-box;

			&::placeholder {
				color: #999;
			}
		}

		.get-code-button {
			height: 100%;
			padding: 0 30rpx;
			font-size: 28rpx;
			color: #FF8E2B;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: transparent;
			
			&.disabled {
				color: #FF8E2B;
				opacity: 0.7;
			}
		}
	}

	.next-button {
		height: 90rpx;
		line-height: 90rpx;
		font-size: 32rpx;
		color: #fff;
		background: linear-gradient(to right, #FFC77F, #FF9B25);
		border: none;
		border-radius: 10rpx;
		text-align: center;
		box-shadow: 0 4rpx 12rpx rgba(255, 155, 37, 0.3);
	}
}
</style>
