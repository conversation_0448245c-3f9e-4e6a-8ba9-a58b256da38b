<template>
	<view class="container">
		<!-- 固定的头部区域 -->
		<view class="fixed-header">
			<CustomHead title="发行定价" />
		</view>

		<!-- 内容区域 -->
		<view class="content">
			<!-- 表单项 -->
			<view class="form-item">
				<view class="label">发行人</view>
				<view class="value">{{ user.companyName }}</view>
			</view>

			<view class="form-item">
				<view class="label">发行期限</view>
				<view class="value-with-arrow" @click="openTermPicker">
					<text style="margin-right: 10rpx;">{{ selectedTerm }}</text>
					<uni-icons type="right" size="18"></uni-icons>
				</view>
			</view>

			<view class="form-item">
				<view class="label">发行方式</view>
				<view class="value-with-arrow" @click="openIssuanceTypePicker">
					<text style="margin-right: 10rpx;">{{ selectedIssuanceType }}</text>
					<uni-icons type="right" size="18"></uni-icons>
				</view>
			</view>

			<view class="form-item">
				<view class="label">是否可续期</view>
				<view class="value-with-arrow" @click="openRenewablePicker">
					<text style="margin-right: 10rpx;">{{ selectedRenewable }}</text>
					<uni-icons type="right" size="18"></uni-icons>
				</view>
			</view>

			<!-- 按钮区域 -->
			<view class="button-area">
				<view class="primary-button" @click="startCalculate">开始计算</view>
				<view class="secondary-button" @click="showHistory">历史记录</view>
			</view>


			<SelectionPopup v-model:visible="showTermPicker" :title="termTitle" :options="termOptions"
				:defaultSelected="selectedTerm" @confirm="handleTermConfirm" @cancel="handleTermCancel" />

			<SelectionPopup v-model:visible="showIssuanceTypePicker" :title="issuanceTypeTitle"
				:options="issuanceTypeOptions" :defaultSelected="selectedIssuanceType"
				@confirm="handleIssuanceTypeConfirm" @cancel="handleIssuanceTypeCancel" />

			<SelectionPopup v-model:visible="showRenewablePicker" :title="renewableTitle" :options="renewableOptions"
				:defaultSelected="selectedRenewable" @confirm="handleRenewableConfirm"
				@cancel="handleRenewableCancel" />
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import CustomHead from '@/components/head/head.vue';
import SelectionPopup from '@/components/common/SelectionPopup.vue';
import { calculateIssuancePricing } from '@/api/issuance';

const user = uni.getStorageSync('mpUserInfo');

// 发行期限相关
const showTermPicker = ref(false);
const termTitle = ref('选择发行期限');
const termOptions = ref(['1M', '2M', '3M', '6M', '9M', '1Y', '2Y', '3Y', '5Y', '7Y', '10Y', '15Y']);
const selectedTerm = ref('1Y');

// 发行方式相关
const showIssuanceTypePicker = ref(false);
const issuanceTypeTitle = ref('选择发行方式');
const issuanceTypeOptions = ref(['公募', '私募']);
const selectedIssuanceType = ref('公募');

// 是否可续期相关
const showRenewablePicker = ref(false);
const renewableTitle = ref('选择是否可续期');
const renewableOptions = ref(['不可续期', '可续期']);
const selectedRenewable = ref('不可续期');

// 监听发行方式变化
watch(selectedIssuanceType, (newValue) => {
	if (newValue === '私募') {
		selectedRenewable.value = '不可续期';
	}
});

// 监听是否可续期变化
watch(selectedRenewable, (newValue) => {
	// 移除之前添加"+N"的逻辑
});

// 点击打开发行期限选择
const openTermPicker = () => {
	showTermPicker.value = true;
};

// 发行期限确认
const handleTermConfirm = (value) => {
	selectedTerm.value = value;
	console.log('选择的发行期限:', value);
	showTermPicker.value = false;
};

// 发行期限取消
const handleTermCancel = () => {
	showTermPicker.value = false;
};

// 点击打开发行方式选择
const openIssuanceTypePicker = () => {
	showIssuanceTypePicker.value = true;
};

// 发行方式确认
const handleIssuanceTypeConfirm = (value) => {
	selectedIssuanceType.value = value;
	console.log('选择的发行方式:', value);
	showIssuanceTypePicker.value = false;
};

// 发行方式取消
const handleIssuanceTypeCancel = () => {
	showIssuanceTypePicker.value = false;
};

// 点击打开是否可续期选择
const openRenewablePicker = () => {
	showRenewablePicker.value = true;
};

// 是否可续期确认
const handleRenewableConfirm = (value) => {
	// 如果是私募，只能选择不可续期
	if (selectedIssuanceType.value === '私募' && value === '可续期') {
		uni.showToast({
			title: '私募不支持可续期',
			icon: 'none'
		});
		return;
	}
	selectedRenewable.value = value;
	showRenewablePicker.value = false;
};

// 是否可续期取消
const handleRenewableCancel = () => {
	showRenewablePicker.value = false;
};

// 开始计算
const startCalculate = () => {
	console.log('开始计算');
	// 这里添加计算逻辑
	calculateIssuancePricing({
		data: {
			valuationDt: '',
			bInfoIssuercode: user.outCompCode, // 发行人
			bInfoIssuetype: selectedIssuanceType.value === '公募' ? 439012000 : 439030000, // 发行方式
			term: selectedTerm.value, // 发行期限
			isRenewal: selectedRenewable.value === '可续期' ? 'Y' : 'N', // 是否可续期
		},
		page: {
			pageNo: 1,
			pageSize: 100
		}
	}).then(res => {
		console.log(res);
		if (res.data.code == '000') {
			uni.showToast({
				title: '计算成功',
				icon: 'none'
			});
		} else {
			uni.showToast({
				title: '计算失败',
				icon: 'none'
			});
		}
	});
};

// 查看历史记录
const showHistory = () => {
	console.log('查看历史记录');
	// 这里添加查看历史记录的逻辑
	uni.navigateTo({
		url: '/subPageB/history-pricing/history-pricing'
	});
};
</script>

<style lang="scss" scoped>
.container {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #f5f5f5;
	position: relative;
}

.fixed-header {
	width: 100%;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 100;
}

.content {
	margin-top: 176rpx;
	/* 调整顶部间距，根据实际情况修改 */
	padding: 20rpx 32rpx;
}

.form-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 32rpx;
	background-color: #fff;
	margin-bottom: 20rpx;
}

.form-item:first-child {
	border-top-left-radius: 16rpx;
	border-top-right-radius: 16rpx;
}

.form-item:last-of-type {
	border-bottom-left-radius: 16rpx;
	border-bottom-right-radius: 16rpx;
	margin-bottom: 40rpx;
}

.label {
	font-size: 28rpx;
	color: #333;
}

.value {
	font-size: 28rpx;
	color: #666;
	text-align: right;
}

.value-with-arrow {
	display: flex;
	align-items: center;
	font-size: 28rpx;
	color: #666;
}

.arrow {
	margin-left: 16rpx;
	color: #999;
}

.button-area {
	display: flex;
	flex-direction: column;
	gap: 32rpx;
	margin-top: 40rpx;
}

.primary-button {
	height: 88rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	background: linear-gradient(to right, #FFC268, #FF8E2B);
	border-radius: 10rpx;
	color: #fff;
	font-size: 32rpx;
}

.secondary-button {
	height: 88rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: #fff;
	border-radius: 10rpx;
	color: #333;
	font-size: 32rpx;
	border: 2rpx solid #ddd;
}
</style>