<template>
    <view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
        <!-- 固定的头部区域 -->
        <view class="fixed-header">
            <CustomHead title="市场利率" />
        </view>

        <!-- 顶部tab切换 -->
        <view class="tab-container">
            <view class="tab-item" :class="{ active: activeTab === 'corporate' }" @click="switchTab('corporate')">国债
            </view>
            <view class="tab-item" :class="{ active: activeTab === 'financial' }" @click="switchTab('financial')">国开债
            </view>
        </view>

        <!-- 可滚动的内容区域 -->
        <view class="content-wrapper">
            <scroll-view class="scrollable-content" scroll-y :show-scrollbar="false" enhanced>
                <view class="chart-card">
                    <view class="card-content">

                        <!-- ==================== 期限选择 ==================== -->
                        <PeriodSelector 
                            :periodOptions="periodOptions" 
                            @periodChange="handlePeriodChange"
                        />

                        <!-- ==================== 利率信息展示 ==================== -->
                        <RateInfoDisplay 
                            rateLabel="最新估值"
                            :rateValue="getSelectedPeriodData('value')"
                            changeLabel="涨跌BP"
                            :changeValue="getSelectedPeriodData('change')"
                            timeLabel="更新时间"
                            :timeValue="getSelectedPeriodData('time')"
                        />

                        <!-- ==================== 时间筛选器 ==================== -->
                        <TimeFilterSelect :isShow="true" @filterChange="handleTimeFilterChange"
                            @openCalendar="toggleModal" />

                        <!-- ==================== 图表上方当前数据点信息 ==================== -->
                        <view class="chart-data-point" v-if="chartData.categories.length > 0">
                            <view class="point-date">
                                <view class="point-indicator"></view>
                                <text class="number-font">{{ latestDataDate }}</text>
                            </view>
                            <view class="point-value">估值: <text class="number-font">{{ latestDataValue }}</text></view>
                            <view class="point-change" :class="latestDataChange > 0 ? 'up' : (latestDataChange < 0 ? 'down' : 'flat')">
                                较昨日: <text class="number-font">{{ latestDataChange > 0 ? '+' : '' }}{{ latestDataChange }}BP</text>
                            </view>
                        </view>

                        <!-- ==================== 图表展示区 ==================== -->
                        <view class="chart-component" v-show="isShowChart && chartData.categories.length > 0">
                            <RateChart 
                                :chartData="chartData" 
                                :specialPoints="chartSpecialPoints" 
                                @pointClick="handlePointClick"
                            />
                        </view>

                        <!-- 无数据时的图表占位区 -->
                        <view class="chart-placeholder" v-if="isShowChart && chartData.categories.length === 0">
                            <view class="empty-chart">
                                <text>暂无图表数据</text>
                            </view>
                        </view>
                    </view>
                </view>

                <!-- 期限列表卡片 -->
                <!-- <PeriodListCard :columns="columns" :rating="currentRateData.mainRating" :bondType="selectedBondType" :periodList="periodList" /> -->
				<PeriodListCard :bondTabType="activeTab" :bondType="selectedBondType" :periodList="periodList"
					:rating="currentRateData.mainRating" :columns="[
						{ title: '期限', field: 'period' },
						{ title: '最新估值(%)', field: 'value' },
						{ title: '涨跌BP', field: 'change' }
					]" />
                <!-- 底部留白区域，确保内容可以完全滚动显示 -->
                <view class="bottom-space"></view>
            </scroll-view>
        </view>

        <!-- 日期选择组件（浮动在内容上方） -->
        <DateRangePicker v-model:visible="showModal" :defaultStartDate="startDate" :defaultEndDate="endDate"
            @dateRangeChange="handleDateRangeChange" @update:visible="handleModalVisibleChange" />
    </view>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import PeriodListCard from '@/components/MarketRate/PeriodListCard.vue';
import PeriodSelector from '@/components/common/PeriodSelector.vue';
import CustomHead from '@/components/head/head.vue';
import DateRangePicker from '@/components/calendar/DateRangePicker.vue';
import TimeFilterSelect from '@/components/common/TimeFilterSelect.vue';
import RateChart from '@/components/RateChart/RateChart.vue';
import RateInfoDisplay from '@/components/MarketRate/RateInfoDisplay.vue';
import { getMarketRate, getMarketRateCard, getMarketRateTerm } from '@/api/marketRate';
import { getSHIBORTerm } from '@/api/dataDict';
import { getAssetUrl } from '@/config/assets';

// ==================== 页面变量区域 ====================
// Tab和基础配置相关
const activeTab = ref('corporate'); // 默认显示企业债
const bAnalCurveName = ref('中债国债收益率曲线');
const selectedBondType = ref('国债'); // 当前选中的债券类型

// 期限相关
const allPeriodOptions = ref([]);
const periodOptions = ref([]);
const activePeriod = ref('');
const activePeriodValue = ref(0);
const periodList = ref([]);

// 市场利率参数
const ccid = ref("98745c20df3747a1885c354f1ed85713");
const ownedModuleid = ref("708631605142536192");

// 计算近一年的起始日期和结束日期
const today = new Date();
const oneYearAgo = new Date();
oneYearAgo.setFullYear(today.getFullYear() - 1);

// 格式化日期为YYYY-MM-DD格式
const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};

const tradeDtStart = ref(formatDate(oneYearAgo));
const tradeDtEnd = ref(formatDate(today));
const activeTimeFilter = ref('1year');

// 当前数据显示相关
const currentRateData = ref({}); // 利率信息展示数据
const updateTime = ref('');

// 图表上方数据点信息
const latestDataDate = ref('--');
const latestDataValue = ref('0.0000%');
const latestDataChange = ref(0);

// 图表数据
const chartData = ref({
    categories: [],
    series: [{ name: '', data: [], color: '#FF9900' }]
});

// 弹窗和日期相关
const showModal = ref(false); 
const startDate = ref(oneYearAgo);
const endDate = ref(new Date());
const isShowChart = ref(true);

// 特殊点（最新数据点）
const chartSpecialPoints = computed(() => {
    const categories = chartData.value.categories;
    const series = chartData.value.series;
    
    if (!categories.length || !series.length) {
        return [];
    }

    // 标记最后一个点为特殊点
    const lastIndex = categories.length - 1;
    
    return [
        {
            index: lastIndex,
            value: series[0].data[lastIndex],
            color: '#FF9800',
            label: '最新值'
        }
    ];
});

// ==================== API请求方法区域 ====================
// 通用日期格式化函数，支持两种格式：YYYYMMDD 和 YYYY/MM/DD，统一转换为YYYY/MM/DD格式
const formatDateString = (dateStr) => {
    if (!dateStr) return '--';
    
    // 如果是YYYY/MM/DD格式，保持原样
    if (dateStr.includes('/')) {
        return dateStr;
    }
    
    // 如果是YYYYMMDD格式，转换为YYYY/MM/DD
    if (dateStr.length === 8) {
        return `${dateStr.substring(0, 4)}/${dateStr.substring(4, 6)}/${dateStr.substring(6, 8)}`;
    }
    
    // 其他情况直接返回原始字符串
    return dateStr;
};

// 通用的bAnalYieldAdd字段处理函数
const formatYieldChange = (yieldAddValue) => {
    if (yieldAddValue === null || yieldAddValue === undefined) {
        return 0;
    }
    
    // 如果是数字类型，直接返回
    if (typeof yieldAddValue === 'number') {
        return yieldAddValue;
    }
    
    // 如果是字符串类型，需要处理
    if (typeof yieldAddValue === 'string') {
        // 去除可能的加号，然后转换为数字
        const cleanValue = yieldAddValue.replace(/^\+/, '');
        const numValue = parseFloat(cleanValue);
        return isNaN(numValue) ? 0 : numValue;
    }
    
    // 其他情况返回0
    return 0;
};

// 获取期限字典
const fetchPeriodList = async () => {
	const data = 'BOND_TERM_MARKET_CURVE_RATIO';
	const res = await getSHIBORTerm(data);
	allPeriodOptions.value = res.data.data;
	filterPeriods(activeTab.value);
};

// 获取市场利率
const fetchMarketRateData = async () => {
    const data = {
        ccid: ccid.value,
        ownedModuleid: ownedModuleid.value, 
        bAnalCurveterm: [activePeriodValue.value],
        date: [tradeDtStart.value, tradeDtEnd.value],
        radioDate: 12,
        bAnalCurveName: bAnalCurveName.value,
        tradeDtStart: tradeDtStart.value,
        tradeDtEnd: tradeDtEnd.value
    }
    try {
        const res = await getMarketRate(data);
        
        // 获取当前选中期限的数据数组
        const periodKey = activePeriod.value;
        if (res.data && res.data.data && res.data.data[periodKey]) {
            updateChartData(res.data.data[periodKey]);
        } else {
            updateChartData([]);
        }
    } catch (error) {
        updateChartData([]);
    }
}


// 获取市场利率期限列表
const fetchMarketRateTerm = async () => {
	const data = {
		ccid: ccid.value,
		ownedModuleid: ownedModuleid.value,
		bAnalCurveName: bAnalCurveName.value
	}
    const res = await getMarketRateTerm(data);
	periodList.value = res.data.data.map(item => ({
		period: item.bAnalCurveterm,
		value: parseFloat(item.bAnalYield) || 0,
		change: formatYieldChange(item.bAnalYieldAdd),
		displayValue: item.bAnalYield ? parseFloat(item.bAnalYield).toFixed(4) : '0.0000',
        time: item.tradeDt
	}));
    
    // 获取期限列表后，更新当前利率数据
    updateCurrentRateFromPeriodList();
};

// ==================== 事件处理方法区域 ====================
// 根据tab筛选期限
const filterPeriods = (tab) => {
    if (tab == 'corporate') {
        const targetCnnames = ['5Y', '10Y', '15Y', '20Y', '30Y', '40Y', '50Y'];
        periodOptions.value = allPeriodOptions.value.filter(item => targetCnnames.includes(item.cnname));
        bAnalCurveName.value = '中债国债收益率曲线';
    } else {
        const targetCnnames = ['10Y', '15Y', '20Y', '30Y', '40Y', '50Y'];
        periodOptions.value = allPeriodOptions.value.filter(item => targetCnnames.includes(item.cnname));
        bAnalCurveName.value = '中债国开债收益率曲线';
    }
};

// 切换标签页
const switchTab = (tab) => {
    activeTab.value = tab;
    
    if (!allPeriodOptions.value?.length) {
        fetchPeriodList();
    } else {
        filterPeriods(tab);
    }
    
    fetchMarketRateTerm();
};

// 更新图表数据
const updateChartData = (periodData) => {
    // 检查数据是否为空
    if (!periodData || !Array.isArray(periodData) || periodData.length === 0) {
        // 清空图表数据
        chartData.value = {
            categories: [],
            series: [{ 
                name: activePeriod.value || '',
                data: [], 
                color: '#FF9900',
                rawData: []
            }]
        };
        
        // 清空图表上方信息
        latestDataDate.value = '--';
        latestDataValue.value = '--%';
        latestDataChange.value = 0;
        return;
    }
    
    // 将API返回的数据转换为图表所需格式
    const categories = periodData.map(item => {
        const dateStr = item.tradeDt;
        return formatDateString(dateStr);
    });
    
    const seriesData = periodData.map(item => parseFloat(item.bAnalYield));
    
    // 创建扩展的原始数据，包含格式化的日期和原始数据
    const formattedRawData = periodData.map(item => {
        const dateStr = item.tradeDt;
        const formattedDate = formatDateString(dateStr);
        return {
            ...item,
            formattedDate // 添加格式化后的日期字段，方便在图表中匹配
        };
    });
    
    // 更新图表数据
    chartData.value = {
        categories: categories,
        series: [
            {
                name: activePeriod.value,
                data: seriesData,
                color: '#FF9900',
                // 添加处理后的原始数据，包含bAnalYieldAdd字段
                rawData: formattedRawData
            }
        ]
    };
    
    // 更新图表上方信息
    if (periodData.length > 0) {
        const latestData = periodData[periodData.length - 1];
        
        // 格式化日期并更新
        const dateStr = latestData.tradeDt;
        const formattedDate = formatDateString(dateStr);
        updateTime.value = formattedDate;
        latestDataDate.value = formattedDate;
        latestDataValue.value = `${parseFloat(latestData.bAnalYield).toFixed(4)}%`;
        
        // 使用原始数据中的bAnalYieldAdd字段作为涨跌值
        latestDataChange.value = formatYieldChange(latestData.bAnalYieldAdd);
    }
}

// 处理期限变更事件
const handlePeriodChange = (data) => {
    activePeriod.value = data.period;
    activePeriodValue.value = data.itemcode;
    fetchMarketRateData();
    updateCurrentRateFromPeriodList(); // 更新当前利率显示
};

// 更新当前利率展示的数据，从期限列表中获取当前选中期限的数据
const updateCurrentRateFromPeriodList = () => {
    if (!periodList.value.length || !activePeriod.value) return;
    
    // 查找当前选中期限在期限列表中的数据
    const matchingPeriod = periodList.value.find(item => item.period === activePeriod.value);
    
    if (matchingPeriod) {
        currentRateData.value = {
            ...currentRateData.value,
            bAnalYield: matchingPeriod.value,
            bAnalYieldAdd: matchingPeriod.change
        };
        
        // 更新时间为当前时间
        const now = new Date();
        updateTime.value = formatDate(now);
    }
};

// 监听期限列表变化，更新当前利率数据
watch(() => periodList.value, () => {
    updateCurrentRateFromPeriodList();
}, { deep: true });

// 监听选中的期限变化
watch(() => activePeriod.value, () => {
    updateCurrentRateFromPeriodList();
});

// 处理图表点击事件
const handlePointClick = (pointInfo) => {
    console.log('点击图表数据点:', pointInfo);
    if (pointInfo) {
        latestDataDate.value = pointInfo.date;
        latestDataValue.value = pointInfo.value;
        // 使用原始数据中的bAnalYieldAdd字段
        if (pointInfo.bAnalYieldAdd !== undefined) {
            latestDataChange.value = formatYieldChange(pointInfo.bAnalYieldAdd);
        } else if (pointInfo.change !== undefined) {
            latestDataChange.value = formatYieldChange(pointInfo.change);
        }
    }
};

// 切换弹窗显示状态
const toggleModal = () => {
    showModal.value = !showModal.value;
};

// 处理日期区间变更
const handleDateRangeChange = (dateRange) => {
    tradeDtStart.value = dateRange[0];
    tradeDtEnd.value = dateRange[1];
    fetchMarketRateData();
};

// 处理弹窗可见性变化
const handleModalVisibleChange = () => {};

// 处理时间筛选变更
const handleTimeFilterChange = (data) => {
    activeTimeFilter.value = data.filter;
    tradeDtStart.value = data.dateRange[0];
    tradeDtEnd.value = data.dateRange[1];
    fetchMarketRateData();
};

// 监听showModal的变化
watch(() => showModal.value, (newValue) => {
    isShowChart.value = !newValue;
});

// 添加一个函数，用于获取当前选中期限的数据
const getSelectedPeriodData = (field) => {
    if (!periodList.value.length || !activePeriod.value) return '--';
    
    // 查找当前选中期限在期限列表中的数据
    const matchingPeriod = periodList.value.find(item => item.period === activePeriod.value);
    
    if (!matchingPeriod) return '--';
    
    if (field === 'value') {
        return matchingPeriod.value !== null && matchingPeriod.value !== undefined ? `${parseFloat(matchingPeriod.value).toFixed(4)}%` : '--';
    } else if (field === 'change') {
        return matchingPeriod.change !== null && matchingPeriod.change !== undefined ? matchingPeriod.change : '--';
    } else if (field === 'time') {
        if (!matchingPeriod.time) return '--';
        // 格式化日期 YYYYMMDD -> YYYY-MM-DD
        const dateStr = matchingPeriod.time;
        return dateStr ? formatDateString(dateStr) : '--';
    }
    
    return '--';
};

// 在组件挂载时调用
onMounted(() => {
    fetchPeriodList();
    fetchMarketRateTerm();
});
</script>

<style lang="scss" scoped>
/* ====================== 1. 页面布局相关样式 ====================== */
/* 1.1 页面容器 */
.container {
    padding: 0 20rpx;
    height: 100vh;
    box-sizing: border-box;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    /* 防止整体页面滚动 */
}

/* 1.2 头部区域 */
.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

/* 1.3 内容区域 */
.content-wrapper {
    flex: 1;
    /* 给固定头部留出空间 */
    overflow: hidden;
}

/* 1.4 滚动区域 */
.scrollable-content {
    height: 100%;
    padding: 10rpx 0 0;

    ::-webkit-scrollbar {
        display: none;
        width: 0;
        height: 0;
        color: transparent;
    }
}

/* 1.5 底部留白 */
.bottom-space {
    height: 100rpx;
    width: 100%;
}

/* ====================== 2. Tab切换样式 ====================== */
.tab-container {
    display: flex;
    margin-bottom: 20rpx;
    border-radius: 10rpx;
    margin-top: 180rpx;
    /* 给固定头部留出空间 */

    .tab-item {
        flex: 1;
        padding: 30rpx 0;
        text-align: center;
        position: relative;
        font-size: 32rpx;
        color: #999;
        transition: all 0.3s ease;
        font-weight: bold;

        &.active {
            color: #FF9900;
            font-weight: bold;

            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 40%;
                height: 6rpx;
                background-color: #FF9900;
                border-radius: 6rpx 6rpx 0 0;
            }
        }
    }
}

/* ====================== 3. 图表卡片样式 ====================== */
/* 3.1 卡片容器 */
.chart-card {
    background-color: white;
    border-radius: 20rpx;
    overflow: hidden;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 3.2 卡片内容 */
.card-content {
    padding: 30rpx;
}

/* 3.3 图表组件 */
.chart-component {
    background-color: white;
    border-radius: 16rpx;
    overflow: hidden;
    margin-top: 20rpx;
}

/* ====================== 4. 辅助样式 ====================== */
/* 4.1 加载状态 */
.loading-container {
    height: 400rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.8);
    margin: 20rpx 0;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);

    text {
        font-size: 28rpx;
        color: #999999;
    }
}


/* ==================== 5. 图表数据点信息样式 ==================== */
.chart-data-point {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    background-color: #F5F7FF;
    padding: 15rpx 20rpx;
    border: 4rpx solid #fafafa;
    box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(172, 172, 172, 0.2);
    border-radius: 10rpx;
    font-size: 22rpx;
}

.point-date {
    display: flex;
    align-items: center;
}

.point-indicator {
    width: 14rpx;
    height: 14rpx;
    border-radius: 50%;
    background-color: #6F7CD1;
    margin-right: 10rpx;
}

.point-value {
    color: #333;
}

.point-change {
    &.up {
        color: #E76056;
    }

    &.down {
        color: #52C41A;
    }
    
    &.flat {
        color: #FEB249;
    }
}

/* ==================== 6. 无数据时的图表占位区样式 ==================== */
.chart-placeholder {
    height: 400rpx;
    background-color: white;
    border-radius: 16rpx;
    overflow: hidden;
    margin-top: 20rpx;
    padding: 30rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    .empty-chart {
        text-align: center;
        color: #999;
        font-size: 28rpx;
    }
}
</style>