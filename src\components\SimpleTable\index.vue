<template>
    <view class="simple-table" :style="{ height: height }">
        <view class="simple-table-content">
            <scroll-view class="simple-table-scroll" scroll-y="true" scroll-x="true" :lower-threshold="50"
                @scrolltolower="onScrollToLower" @scroll="onScroll" :style="{ height: '100%' }" enhanced :bounces="false"
                :show-scrollbar="false">
                <view class="simple-table-wrapper">
                    <!-- 表头 -->
                    <view v-if="showHeader" class="simple-table-header">
                        <view class="simple-table-thead">
                            <view class="table-tr">
                                <view v-for="(column, index) in transColumns" :key="index" class="table-th"
                                    :class="{ 'stick-side': index < fixedLeftColumns.length }"
                                    :style="getHeaderCellStyle(column, index)" @click="onHeaderClick(column, index)">
                                    {{ column.label }}
                                </view>
                            </view>
                        </view>
                    </view>

                    <!-- 表格内容 -->
                    <view class="simple-table-tbody">
                        <!-- 数据行 -->
                        <view v-for="(row, rowIndex) in data" :key="getRowKey(row, rowIndex)" class="table-tr" :class="{
                            'current-row': highlight && currentRowKey === getRowKey(row, rowIndex),
                            'odd': stripe && rowIndex % 2 !== 0,
                            'even': stripe && rowIndex % 2 === 0
                        }" @click="onRowClick(row, rowIndex)">
                            <view v-for="(column, colIndex) in transColumns" :key="colIndex" class="table-td"
                                :class="{ 'stick-side': colIndex < fixedLeftColumns.length }"
                                :style="getCellStyle(column, colIndex, row, rowIndex)"
                                @click.stop="onCellClick(row, column, rowIndex, colIndex)">
                                {{ getCellValue(row, column) }}
                            </view>
                        </view>
                    </view>

                    <!-- 加载更多提示 -->
                    <view v-if="loading || isLoadingMore" class="loading-more">
                        <view class="loading-text">加载中...</view>
                    </view>
                </view>
            </scroll-view>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'

// 定义列配置类型
interface Column {
    name: string
    label: string
    width?: number
    align?: 'left' | 'center' | 'right'
    fixed?: boolean
    formatter?: (value: any, row: any, column: Column) => string
    emptyString?: string
    left?: number // 固定列的left偏移量
}

// 定义组件Props
interface Props {
    columns: Column[]
    data: any[]
    height?: string
    border?: boolean
    stripe?: boolean
    highlight?: boolean
    showHeader?: boolean
    loading?: boolean
    hasMore?: boolean
    rowKey?: string | ((row: any, index: number) => string | number)
    headerStyle?: Record<string, any>
    cellStyle?: Record<string, any> | ((params: { row: any, column: Column, rowIndex: number, colIndex: number }) => Record<string, any>)
}

const props = withDefaults(defineProps<Props>(), {
    columns: () => [],
    data: () => [],
    height: '600rpx',
    border: false,
    stripe: true,
    highlight: false,
    showHeader: true,
    loading: false,
    hasMore: true,
    rowKey: undefined,
    headerStyle: () => ({}),
    cellStyle: () => ({})
})

// 定义组件事件
const emit = defineEmits<{
    rowClick: [row: any, index: number]
    cellClick: [row: any, column: Column, rowIndex: number, colIndex: number]
    headerClick: [column: Column, index: number]
    loadMore: []
}>()

// 响应式数据
const isLoadingMore = ref(false)
const currentRowKey = ref<string | number | null>(null)

// 计算属性：固定列
const fixedLeftColumns = computed(() => {
    const arr: Column[] = []
    for (let i = 0; i < props.columns.length; i++) {
        const item = props.columns[i]
        if (item.fixed) {
            arr.push(item)
        } else {
            break
        }
    }
    return arr
})

// 计算属性：处理后的列配置（参考zb-table的transColumns逻辑）
const transColumns = computed(() => {
    let leftOffset = 0
    return props.columns.map((column, index) => {
        const processedColumn = { ...column }

        // 设置默认宽度
        if (!processedColumn.width) {
            processedColumn.width = 240 // 默认120px转换为240rpx
        }

        // 计算固定列的left位置
        if (processedColumn.fixed) {
            processedColumn.left = leftOffset
            leftOffset += processedColumn.width
        }

        // 设置默认空值显示
        processedColumn.emptyString = processedColumn.emptyString || '--'

        return processedColumn
    })
})

// 获取行唯一标识
const getRowKey = (row: any, index: number): string | number => {
    if (typeof props.rowKey === 'function') {
        return props.rowKey(row, index)
    }
    if (typeof props.rowKey === 'string') {
        return row[props.rowKey] || index
    }
    return index
}

// 获取单元格值
const getCellValue = (row: any, column: Column): string => {
    const value = row[column.name]

    if (column.formatter && typeof column.formatter === 'function') {
        return column.formatter(value, row, column)
    }

    if (value === null || value === undefined || value === '') {
        return column.emptyString || '--'
    }

    return String(value)
}

// 获取表头单元格样式（参考zb-table的实现）
const getHeaderCellStyle = (column: Column, index: number) => {
    const transColumn = transColumns.value[index]
    const baseStyle: Record<string, any> = {
        width: (column.width || 240) + 'rpx',
        minWidth: (column.width || 240) + 'rpx'
    }

    // 固定列样式（参考zb-table的zb-stick-side）
    if (column.fixed && transColumn.left !== undefined) {
        baseStyle.left = transColumn.left + 'rpx'
    }

    // 边框样式
    if (props.border) {
        baseStyle.borderRight = '2rpx solid #ebeef5'
        baseStyle.borderTop = '2rpx solid #ebeef5'
    }

    // 对齐方式
    if (column.align === 'center') {
        baseStyle.textAlign = 'center'
    } else if (column.align === 'right') {
        baseStyle.textAlign = 'right'
    }

    // 合并用户自定义样式
    return { ...baseStyle, ...props.headerStyle }
}

// 获取数据单元格样式（参考zb-table的实现）
const getCellStyle = (column: Column, colIndex: number, row: any, rowIndex: number) => {
    const transColumn = transColumns.value[colIndex]
    const baseStyle: Record<string, any> = {
        width: (column.width || 240) + 'rpx',
        minWidth: (column.width || 240) + 'rpx'
    }

    // 固定列样式（参考zb-table的zb-stick-side）
    if (column.fixed && transColumn.left !== undefined) {
        baseStyle.left = transColumn.left + 'rpx'
    }

    // 边框样式
    if (props.border) {
        baseStyle.borderRight = '2rpx solid #ebeef5'
        baseStyle.borderBottom = '2rpx solid #ebeef5'
    }

    // 对齐方式
    if (column.align === 'center') {
        baseStyle.textAlign = 'center'
    } else if (column.align === 'right') {
        baseStyle.textAlign = 'right'
    }

    // 用户自定义样式
    if (props.cellStyle) {
        if (typeof props.cellStyle === 'function') {
            const customStyle = props.cellStyle({ row, column, rowIndex, colIndex })
            Object.assign(baseStyle, customStyle)
        } else {
            Object.assign(baseStyle, props.cellStyle)
        }
    }

    return baseStyle
}

// 滚动事件监听
const onScroll = (e: any) => {
    // 可以在这里处理滚动事件，比如同步固定列的滚动位置
}

// 防抖处理上拉加载
let loadMoreTimer: number | null = null
const onScrollToLower = () => {
    // 防止重复触发
    if (isLoadingMore.value || !props.hasMore || props.loading) {
        return
    }

    // 清除之前的定时器
    if (loadMoreTimer) {
        clearTimeout(loadMoreTimer)
    }

    // 减少防抖延迟，提高响应速度
    loadMoreTimer = setTimeout(() => {
        isLoadingMore.value = true
        emit('loadMore')
    }, 50) as any  // 从100ms减少到50ms
}

// 监听loading状态变化，当父组件loading完成时重置isLoadingMore
watch(() => props.loading, (newLoading) => {
    if (!newLoading && isLoadingMore.value) {
        isLoadingMore.value = false
    }
}, { immediate: false })

// 行点击事件
const onRowClick = (row: any, index: number) => {
    if (props.highlight) {
        currentRowKey.value = getRowKey(row, index)
    }
    emit('rowClick', row, index)
}

// 单元格点击事件
const onCellClick = (row: any, column: Column, rowIndex: number, colIndex: number) => {
    emit('cellClick', row, column, rowIndex, colIndex)
}

// 表头点击事件
const onHeaderClick = (column: Column, index: number) => {
    emit('headerClick', column, index)
}

// 暴露方法给父组件
defineExpose({
    scrollToTop: () => {
        // 可以通过ref获取scroll-view并滚动到顶部
    },
    clearSelection: () => {
        currentRowKey.value = null
    },
    // 添加测试方法，手动触发loadMore
    triggerLoadMore: () => {
        onScrollToLower()
    }
})
</script>

<style lang="scss" scoped>
.simple-table {
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    font-size: 24rpx;
    position: relative;
    background: #fff;

    .simple-table-content {
        flex: 1;
        overflow: hidden;
        position: relative;
    }

    .simple-table-scroll {
        height: 100%;

        // 隐藏滚动条
        ::-webkit-scrollbar {
            display: none !important;
            width: 0 !important;
            height: 0 !important;
            -webkit-appearance: none;
            background: transparent;
        }
    }

    .simple-table-wrapper {
        min-width: 100%;
        width: fit-content;
    }

    // 表头样式（参考zb-table的zb-table-header）
    .simple-table-header {
        position: sticky;
        top: 0;
        z-index: 10;
        background: #fafafa;

        .simple-table-thead {
            .table-tr {
                display: flex;

                .table-th {
                    flex-shrink: 0;
                    width: 240rpx;
                    padding: 0 16rpx 0 20rpx;
                    height: 80rpx;
                    line-height: 80rpx;
                    box-sizing: border-box;
                    background: #fafafa;
                    font-weight: bold;
                    color: #333;
                    border-bottom: 2rpx solid #ebeef5;
                    word-break: keep-all;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;

                    // 固定列样式（参考zb-table的zb-stick-side）
                    &.stick-side {
                        position: sticky;
                        left: 0;
                        z-index: 11;
                        background: #fafafa;
                    }
                }
            }
        }
    }

    // 表格内容样式（参考zb-table的zb-table-tbody）
    .simple-table-tbody {
        .table-tr {
            display: flex;
            transition: background-color 0.3s;

            .table-td {
                flex-shrink: 0;
                width: 240rpx;
                padding: 0 16rpx 0 20rpx;
                height: 80rpx;
                line-height: 80rpx;
                box-sizing: border-box;
                background: #fff;
                color: #606266;
                border-bottom: 2rpx solid #ebeef5;
                word-break: keep-all;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;

                // 固定列样式（参考zb-table的zb-stick-side）
                &.stick-side {
                    position: sticky;
                    left: 0;
                    z-index: 9;
                    background: #fff;
                }
            }

            // 奇偶行样式（参考zb-table的odd/even）
            &.odd .table-td {
                background: #f9f9f9;

                &.stick-side {
                    background: #f9f9f9;
                }
            }

            &.even .table-td {
                background: #fff;

                &.stick-side {
                    background: #fff;
                }
            }

            // 当前选中行样式（参考zb-table的current-row）
            &.current-row .table-td {
                background-color: #ecf5ff;

                &.stick-side {
                    background-color: #ecf5ff;
                }
            }
        }
    }

    // 加载更多提示
    .loading-more {
        width: 100%;
        height: 100rpx;
        display: flex;
        justify-content: center;
        align-items: center;

        .loading-text {
            color: #909399;
            font-size: 24rpx;
        }
    }
}
</style>