const router = {
  push(obj: object | string) {
    uni.navigateTo({
      url: getUrlPath(obj)
    })
  },
  replace(obj: object | string) {
    uni.redirectTo({
      url: getUrlPath(obj)
    })
  },
  reLaunch(obj: object | string) {
    uni.reLaunch({
      url: getUrlPath(obj)
    })
  },
  switchTab(url: string) {
    uni.switchTab({
      url
    })
  },
  back(delta = 1) {
    uni.navigateBack({
      delta: delta
    })
  },
  extract(options: any) {
    if (options?.encodedData) {
      return JSON.parse(decodeURIComponent(options.encodedData))
    } else {
      return options
    }
  }
}
const getUrlPath = (obj: any) => {
  let url = ''
  if (typeof obj == 'string') {
    url = obj
  }
  if (typeof obj == 'object') {
    const queryStr = encodeURIComponent(JSON.stringify(obj.query))
    url = `${obj.path}?encodedData=${queryStr}`
  }
  return url
}

export default router
