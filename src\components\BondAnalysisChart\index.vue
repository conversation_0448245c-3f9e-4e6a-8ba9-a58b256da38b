<template>
    <view class="chart-wrapper">
        <!-- 柱状图容器，可以水平滑动 -->
        <scroll-view class="chart-scroll-view" scroll-x="true" show-scrollbar="false" enhanced>
            <view class="chart-container" :style="{ width: chartContainerWidth + 'px' }" id="bondChartContainer">
                <canvas canvas-id="bondAnalysisChart" id="bondAnalysisChart" class="chart-canvas"
                    type="2d"
                    :style="{ width: chartContainerWidth + 'px', height: '380rpx' }"></canvas>
            </view>
        </scroll-view>
    </view>
</template>

<script setup lang="ts">
import { ref, onMounted, getCurrentInstance, watch, nextTick, computed } from 'vue';

// 定义图表数据项的接口
interface ChartDataItem {
    type: string;
    value: number;
    color: {
        start: string;
        end: string;
    };
}

// 定义组件接收的属性
const props = defineProps({
    // 分析数据
    chartData: {
        type: Array as () => ChartDataItem[],
        required: true,
        default: () => []
    }
    // 移除totalAmount属性，不再需要
});

// 实例引用
const instance = getCurrentInstance();
const canvasContext = ref<any>(null);

// 图表容器宽度计算属性
const chartContainerWidth = ref(750); // 初始默认宽度

// 监听数据变化重绘图表
watch(() => props.chartData, () => {
    nextTick(() => {
        initChart();
    });
}, { deep: true });

// 初始化图表
const initChart = () => {
    try {
        if (!instance) {
            console.error('获取组件实例失败，无法绘制图表。');
            return;
        }

        // 获取系统信息，计算像素比例
        const systemInfo = uni.getSystemInfoSync();
        const screenWidth = systemInfo.windowWidth;
        const pixelRatio = systemInfo.pixelRatio || 2;
        const isIOS = systemInfo.platform === 'ios';

        // 计算Canvas尺寸，确保能容纳所有数据
        const itemWidth = 60; // 每个项目占据的宽度（包括间距）
        const minWidth = screenWidth - 40; // 最小宽度（屏幕宽度减去左右边距）

        // 当数据多于可见区域能容纳的数量时，扩展宽度
        const containerWidth = Math.max(minWidth, props.chartData.length * itemWidth);

        // 更新容器宽度
        chartContainerWidth.value = containerWidth;

        // 创建画布上下文 - 使用fields方法替代createCanvasContext
        const query = uni.createSelectorQuery().in(instance.proxy);
        query.select('#bondAnalysisChart')
            .fields({ node: true, size: true }, function(res) {
                // 这个回调函数在查询结果可用时调用，但我们使用exec的回调处理结果
            })
            .exec(res => {
                if (res[0]) {
                    const canvas = res[0].node;
                    const ctx = canvas.getContext('2d');
                    
                    if (!ctx) {
                        console.error('获取 Canvas 上下文失败 (bondAnalysisChart)。');
                        return;
                    }
                    
                    // 设置canvas尺寸，处理高清显示
                    canvas.width = containerWidth * pixelRatio;
                    canvas.height = (screenWidth * 0.5) * pixelRatio;
                    
                    // 缩放画布以匹配设备像素比
                    ctx.scale(pixelRatio, pixelRatio);
                    
                    canvasContext.value = ctx;
                    drawBondChart(ctx, isIOS);
                } else {
                    console.error('未获取到Canvas节点');
                }
            });
    } catch (e) {
        console.error('初始化图表失败:', e);
    }
};

// 绘制柱状图
const drawBondChart = (ctx, isIOS) => {
    try {
        if (!ctx) {
            console.error('Canvas上下文不存在，无法绘制图表。');
            return;
        }

        const systemInfo = uni.getSystemInfoSync();
        const screenWidth = systemInfo.windowWidth;
        const canvasWidth = chartContainerWidth.value;
        const canvasHeight = screenWidth * 0.5; // 宽高比约为2:1

        // 清空画布
        ctx.clearRect(0, 0, canvasWidth, canvasHeight);
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        // 计算绘图区域（像素单位）
        const padding = {
            top: canvasHeight * 0.1,
            right: 0, // 固定右边距
            bottom: canvasHeight * 0.15,
            left: 0 // 固定左边距
        };

        const chartWidth = canvasWidth - padding.left - padding.right;
        const chartHeight = canvasHeight - padding.top - padding.bottom;

        // 计算最大值，用于垂直比例尺
        const maxValue = Math.max(...props.chartData.map((item: ChartDataItem) => item.value)) * 1.2;

        // 绘制网格线 - 使用虚线
        ctx.beginPath();
        ctx.strokeStyle = '#E8E8E8';
        ctx.lineWidth = 1;
        
        // 处理不同平台的虚线实现
        if (isIOS) {
            // iOS上通过手动绘制短线段模拟虚线
            for (let i = 0; i <= 3; i++) {
                const y = padding.top + chartHeight * i / 3;
                for (let j = 0; j < chartWidth; j += 8) {
                    ctx.moveTo(padding.left + j, y);
                    ctx.lineTo(padding.left + j + 4, y);
                }
            }
        } else {
            try {
                // 标准平台使用setLineDash
                ctx.setLineDash ? ctx.setLineDash([4, 4]) : 
                    ctx.lineDashOffset ? (ctx.lineDash = [4, 4]) : null;
                
                // 水平网格线
                for (let i = 0; i <= 3; i++) {
                    const y = padding.top + chartHeight * i / 3;
                    ctx.moveTo(padding.left, y);
                    ctx.lineTo(padding.left + chartWidth, y);
                }
            } catch (e) {
                // 降级处理：如果虚线不支持，手动绘制
                for (let i = 0; i <= 3; i++) {
                    const y = padding.top + chartHeight * i / 3;
                    for (let j = 0; j < chartWidth; j += 8) {
                        ctx.moveTo(padding.left + j, y);
                        ctx.lineTo(padding.left + j + 4, y);
                    }
                }
            }
        }
        ctx.stroke();
        
        // 重置虚线样式
        try {
            ctx.setLineDash ? ctx.setLineDash([]) : 
                ctx.lineDashOffset ? (ctx.lineDash = []) : null;
        } catch (e) { /* 忽略错误 */ }

        // 绘制底部横线
        ctx.beginPath();
        ctx.strokeStyle = '#E0E0E0';
        ctx.lineWidth = 1;
        ctx.moveTo(padding.left, padding.top + chartHeight);
        ctx.lineTo(padding.left + chartWidth, padding.top + chartHeight);
        ctx.stroke();

        // 绘制柱状图
        const barCount = props.chartData.length;
        const barSpacing = chartWidth / barCount;
        const barWidth = Math.min(15, barSpacing * 0.3); // 将柱子宽度缩小一半

        props.chartData.forEach((item: ChartDataItem, index: number) => {
            const x = padding.left + index * barSpacing + (barSpacing - barWidth) / 2;
            const barHeight = (item.value / maxValue) * chartHeight;
            const y = padding.top + chartHeight - barHeight;

            // 创建线性渐变
            const gradient = ctx.createLinearGradient(
                x, y + barHeight, // 渐变起点（柱子底部）
                x, y // 渐变终点（柱子顶部）
            );
            gradient.addColorStop(0, item.color.start);
            gradient.addColorStop(1, item.color.end);

            // 绘制柱子，使用渐变填充
            ctx.fillStyle = gradient;
            ctx.fillRect(x, y, barWidth, barHeight);

            // 字体大小固定为14px
            const fontSize = 12; 

            // 绘制数值 - 放在柱子上方
            ctx.fillStyle = '#666666';
            ctx.font = `${fontSize}px sans-serif`;
            ctx.textAlign = 'center';
            ctx.fillText(item.value.toFixed(1), x + barWidth / 2, y - fontSize / 2);

            // 绘制类别名称
            ctx.fillStyle = '#666666';
            ctx.font = `${fontSize}px sans-serif`;
            ctx.textAlign = 'center';
            // 只显示类别的前3个字符，避免文字过长导致重叠
            const displayText = item.type.length > 3 ? item.type.substring(0, 3) + '..' : item.type;
            ctx.fillText(displayText, x + barWidth / 2, padding.top + chartHeight + fontSize * 1.5);
        });

        // iOS特有修复：额外绘制一个点解决渲染问题
        if (isIOS) {
            ctx.beginPath();
            ctx.fillStyle = '#FFFFFF';
            ctx.arc(-10, -10, 1, 0, 2 * Math.PI);
            ctx.fill();
        }
    } catch (e) {
        console.error('绘制图表失败:', e);
    }
};

// 初始化组件
onMounted(() => {
    nextTick(() => {
        initChart();
    });
});
</script>

<style lang="scss" scoped>
.chart-wrapper {
    width: 100%;

    // 柱状图容器，可以水平滑动
    .chart-scroll-view {
        width: 100%;
        overflow-x: auto;
        white-space: nowrap;
    }

    .chart-container {
        height: 380rpx;
        position: relative;
        min-width: 100%;
        background-color: #ffffff;
    }

    .chart-canvas {
        height: 380rpx;
        width: 100% !important;
        display: block;
    }
}

:deep(::-webkit-scrollbar) {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    -webkit-appearance: none;
    background: transparent;
}
</style>