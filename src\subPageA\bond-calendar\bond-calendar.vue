<template>
	<view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
		<!-- 固定的头部区域 -->
		<view class="fixed-header">
			<CustomHead title="债券日历"/>
		</view>
		<view class="content-wrapper">
			<scroll-view class="scrollable-content" scroll-y :show-scrollbar="false" enhanced>
				<!-- 2.1 日历区域 -->
				<view class="calendar-wrapper">
					<view class="calendar-header">
						<text class="arrow" @tap="prevMonth">&#9664;</text>
						<text class="month-label">{{ currentYear }}年{{ currentMonth }}月</text>
						<text class="arrow" @tap="nextMonth">&#9654;</text>
					</view>

					<!-- 周标题 -->
					<view class="week-row">
						<text v-for="(w, i) in weeks" :key="i" class="week-label">{{ w }}</text>
					</view>

					<!-- 日期网格 -->
					<view class="day-grid">
						<view
							v-for="(item, idx) in monthDays"
							:key="idx"
							class="day-cell"
							:class="{ disabled: !item.currentMonth }"
							@tap="selectDay(item)"
						>
							<view class="day-number" :class="{ today: item.isToday, selected: item.isSelected }">
								{{ item.day || '' }}
							</view>
							<!-- 小圆点 -->
							<view v-if="item.events && item.events.length" class="dot-list">
								<view
									v-for="(e, eIdx) in item.events.slice(0, 4)"
									:key="eIdx"
									class="dot"
									:style="{ backgroundColor: e.color }"
								/>
							</view>
						</view>
					</view>
				</view>

				<!-- 2.2 时间轴事件列表 -->
				<view class="timeline-wrapper">
					<view v-for="group in eventsByDate" :key="group.date" class="timeline-group">
						<!-- 日期 -->
						<view class="date-left">
							<text class="date">{{ formatDateStr(group.date, 'YYYY.M.D') }}</text>
							<text class="weekday">{{ weekLabelStr(group.date) }}</text>
						</view>

						<!-- 纵向分隔线 -->
						<view class="line-box">
							<view class="circle" :style="{ backgroundColor: group.list[0]?.color || '#FA7B1C' }" />
						</view>

						<!-- 事件卡片 -->
						<view class="card-list">
							<view
								v-for="(evt, idx) in group.list"
								:key="idx"
								class="event-card"
								:style="{ borderColor: evt.color }"
							>
								<view class="title-row">
									<view class="evt-dot" :style="{ backgroundColor: evt.color }" />
									<text class="title">{{ evt.title }}</text>
								</view>
								<view v-if="evt.subtitle" class="subline">{{ evt.subtitle }}</view>
								<view v-if="evt.scale" class="subline">总规模：{{ evt.scale }}</view>
								<view v-if="evt.rate" class="subline">利率：{{ evt.rate }}</view>
								<view v-if="evt.term" class="subline">期限：{{ evt.term }}</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script setup lang="ts">
import CustomHead from '@/components/head/head.vue';
import { ref, computed, watch } from 'vue';
import dayjs from 'dayjs';
import { getAssetUrl } from '@/config/assets';

// --------------- 1. 常量 ---------------
const weeks = ['一', '二', '三', '四', '五', '六', '日'];

// --------------- 2. 响应式状态 ---------------
const today = dayjs();
const currentYear = ref(today.year());
const currentMonth = ref(today.month() + 1); // month 1-12
const selectedDate = ref(today.format('YYYY-MM-DD'));

// mock 数据，真实场景应从接口获取
interface EventItem {
	date: string; // YYYY-MM-DD
	title: string;
	subtitle?: string;
	scale?: string;
	rate?: string;
	term?: string;
	color: string;
}

const events = ref<EventItem[]>([
	{
		date: '2025-03-29',
		title: '利率债发行',
		subtitle: '利率银行债：3只',
		scale: '200.0000亿元',
		color: '#FA7B1C',
	},
	{
		date: '2025-03-30',
		title: '同业存单到期(大于200亿)',
		subtitle: '债券简称：24农业银行CD029',
		scale: '300.0000亿元',
		rate: '2.2300%',
		term: '1Y',
		color: '#7048E8',
	},
	{
		date: '2025-03-30',
		title: '利率债发行',
		subtitle: '利率银行债：2只',
		scale: '100.0000亿元',
		color: '#FA7B1C',
	},
]);

// --------------- 3. 计算日期网格 ---------------
interface DayCell {
	date: string;
	day: number | null;
	currentMonth: boolean;
	isToday: boolean;
	isSelected: boolean;
	events: EventItem[];
}

const monthDays = ref<DayCell[]>([]);

function buildMonthDays() {
	const firstDay = dayjs(`${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-01`);
	const startWeekDay = firstDay.day() === 0 ? 7 : firstDay.day(); // Monday =1
	const daysInMonth = firstDay.daysInMonth();

	// 需要填充到完整的 7 * 6 = 42 个格子
	const cells: DayCell[] = [];

	// 上月填充
	const prevMonthLastDay = firstDay.subtract(1, 'month');
	for (let i = startWeekDay - 1; i > 0; i--) {
		const date = prevMonthLastDay.date(prevMonthLastDay.daysInMonth() - i + 1);
		cells.push(genCell(date, false));
	}

	// 当月
	for (let i = 1; i <= daysInMonth; i++) {
		const date = firstDay.date(i);
		cells.push(genCell(date, true));
	}

	// 下月填充
	let nextIdx = 1;
	while (cells.length < 42) {
		const date = firstDay.add(1, 'month').date(nextIdx++);
		cells.push(genCell(date, false));
	}

	monthDays.value = cells;
}

function genCell(dateObj: dayjs.Dayjs, isCurrent: boolean): DayCell {
	const dateStr = dateObj.format('YYYY-MM-DD');
	const dayEvents = events.value.filter((e) => e.date === dateStr);
	return {
		date: dateStr,
		day: dateObj.date(),
		currentMonth: isCurrent,
		isToday: dateStr === today.format('YYYY-MM-DD'),
		isSelected: dateStr === selectedDate.value,
		events: dayEvents,
	};
}

// --------------- 4. 导航操作 ---------------
function prevMonth() {
	const date = dayjs(`${currentYear.value}-${currentMonth.value}-01`).subtract(1, 'month');
	currentYear.value = date.year();
	currentMonth.value = date.month() + 1;
}

function nextMonth() {
	const date = dayjs(`${currentYear.value}-${currentMonth.value}-01`).add(1, 'month');
	currentYear.value = date.year();
	currentMonth.value = date.month() + 1;
}

function selectDay(cell: DayCell) {
	if (!cell.currentMonth) return;
	selectedDate.value = cell.date;
	monthDays.value.forEach((c) => (c.isSelected = c.date === cell.date));
}

// --------------- 5. 事件分组 ---------------
const eventsByDate = computed(() => {
	const map: Record<string, EventItem[]> = {};
	events.value.forEach((e) => {
		if (!map[e.date]) {
			map[e.date] = [];
		}
		map[e.date].push(e);
	});
	// 按日期排序
	const sorted = Object.keys(map)
		.sort((a, b) => (dayjs(a).isBefore(dayjs(b)) ? -1 : 1))
		.map((d) => ({ date: d, list: map[d] }));
	return sorted;
});

// --------------- 6. 工具函数 ---------------
function formatDateStr(dateStr: string, pattern = 'YYYY-MM-DD') {
	return dayjs(dateStr).format(pattern);
}

function weekLabelStr(dateStr: string) {
	const idx = dayjs(dateStr).day();
	const map = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
	return map[idx];
}

// --------------- 7. 初始化 ---------------
watch([currentYear, currentMonth, events], buildMonthDays, { immediate: true });
</script>

<style lang="scss" scoped>
/* 1.1 页面容器 */
.container {
	padding: 0 20rpx;
	height: 100vh;
	box-sizing: border-box;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;
	/* 防止整体页面滚动 */
}

/* 1.2 头部区域 */
.fixed-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 100;
}

/* 1.3 内容区域 */
.content-wrapper {
	flex: 1;
	margin-top: 180rpx;
	padding: 20rpx 0;
	overflow: auto;
	/* 主滚动容器 */
	position: relative;
}

/* 1.4 滚动区域 */
.scrollable-content {
	height: 100%;

	::-webkit-scrollbar {
		display: none;
		width: 0;
		height: 0;
		color: transparent;
	}
}

/* ---------- 2. 日历 ---------- */
.calendar-wrapper {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 20rpx 24rpx 32rpx;
	margin-bottom: 24rpx;
}

.calendar-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;

	.arrow {
		font-size: 32rpx;
		color: #666;
		width: 60rpx;
		text-align: center;
	}

	.month-label {
		font-weight: 600;
		font-size: 32rpx;
		color: #000;
	}
}

.week-row {
	display: grid;
	grid-template-columns: repeat(7, 1fr);
	text-align: center;
	margin-bottom: 8rpx;

	.week-label {
		font-size: 26rpx;
		color: #999;
	}
}

.day-grid {
	display: grid;
	grid-template-columns: repeat(7, 1fr);
	row-gap: 12rpx;
	column-gap: 0;
}

.day-cell {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-height: 68rpx;

	&.disabled {
		opacity: 0.3;
	}

	.day-number {
		width: 48rpx;
		height: 48rpx;
		line-height: 48rpx;
		text-align: center;
		border-radius: 50%;
		font-size: 26rpx;
		color: #333;

		&.today {
			background: rgba(250, 123, 28, 0.1);
			color: #fa7b1c;
		}

		&.selected {
			background: #fa7b1c;
			color: #fff;
		}
	}

	.dot-list {
		display: flex;
		margin-top: 4rpx;
		gap: 4rpx;
	}

	.dot {
		width: 8rpx;
		height: 8rpx;
		border-radius: 50%;
	}
}

/* ---------- 3. 时间轴列表 ---------- */
.timeline-wrapper {
	padding-bottom: 80rpx;
}

.timeline-group {
	display: flex;
	margin-bottom: 40rpx;
}

.date-left {
	width: 120rpx;
	text-align: right;
	padding-right: 12rpx;

	.date {
		font-size: 26rpx;
		color: #fa7b1c;
	}

	.weekday {
		font-size: 24rpx;
		color: #999;
	}
}

.line-box {
	position: relative;
	width: 32rpx;
	display: flex;
	justify-content: center;

	&::after {
		content: '';
		position: absolute;
		top: 0;
		bottom: 0;
		width: 2rpx;
		background: #e6e6e6;
	}

	.circle {
		width: 12rpx;
		height: 12rpx;
		border-radius: 50%;
		margin-top: 6rpx;
	}
}

.card-list {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.event-card {
	background: rgba(250, 123, 28, 0.05);
	border-width: 2rpx;
	border-style: solid;
	border-radius: 20rpx;
	padding: 24rpx;

	.title-row {
		display: flex;
		align-items: center;
		margin-bottom: 12rpx;

		.evt-dot {
			width: 16rpx;
			height: 16rpx;
			border-radius: 50%;
			margin-right: 12rpx;
		}

		.title {
			font-size: 28rpx;
			font-weight: 600;
		}
	}

	.subline {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 6rpx;
	}
}
</style>