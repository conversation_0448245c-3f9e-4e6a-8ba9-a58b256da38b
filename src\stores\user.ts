import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { usePermissionStore } from './permission'

export const useUserStore = defineStore('user', () => {
    // State
    const isLoggedIn = ref(false)

    // Getters
    const getLoginStatus = computed(() => isLoggedIn.value)

    // Actions
    // 登录
    const login = async (token: string) => {
        const permissionStore = usePermissionStore()

        // 设置token
        permissionStore.setToken(token)

        // 获取用户信息和权限
        const success = await permissionStore.getUserInfoAndPermissions()
        if (success) {
            isLoggedIn.value = true
            return true
        }
        return false
    }

    // 登出
    const logout = () => {
        const permissionStore = usePermissionStore()
        permissionStore.clearState()
        isLoggedIn.value = false
    }

    return {
        // State
        isLoggedIn,
        // Getters
        getLoginStatus,
        // Actions
        login,
        logout
    }
}, {
    persist: true // 数据持久化
}) 