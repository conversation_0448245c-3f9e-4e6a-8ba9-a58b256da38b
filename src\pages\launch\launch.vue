<!-- pages/launch/launch.vue -->
<template>
	<view class="launch-container" :style="{ backgroundImage: `url('${getAssetUrl('/launch/launch_bg.png')}')` }">
		<!-- 背景渐变 -->
		<view class="bg-gradient"></view>

		<!-- 银行Logo和标语 -->
		<view class="logo-container">
			<image :src="getAssetUrl('/launch/launch_log.svg')" mode="widthFix"></image>
		</view>

		<!-- 底部文字 -->
		<view class="footer">
			<text class="footer-text">- 本系统由成都银行投行·智管家提供 -</text>
			<text class="footer-text">最终解释权归投行·智管家所有</text>
		</view>

		<!-- 倒计时文字（左上角） -->
		<view class="countdown-container">
			<text class="countdown">{{ count }}s</text>
			<text class="skip-btn" @click="handleSkip">跳过</text>
		</view>
	</view>
</template>

<script setup>
import { ref } from "vue";
import { onLoad, onUnload } from "@dcloudio/uni-app";
import { getAssetUrl } from '@/config/assets';

const count = ref(3); // 倒计时初始值
let timer = null;

// 统一的跳转函数，包含清理逻辑
const navigateToLoginGuide = () => {
	if (timer) {
		clearInterval(timer);
		timer = null;
	}
	uni.setStorageSync("isFirstLaunch", true);
	uni.redirectTo({ url: "/pages/login_guide/login_guide" });
};

// 倒计时逻辑
const startCountdown = () => {
	if (timer) return;
	timer = setInterval(() => {
		if (count.value <= 0) {
			navigateToLoginGuide();
			return;
		}
		count.value--;
	}, 1000);
};

// 主要逻辑放在 onLoad 中
onLoad(() => {
	startCountdown()
});

// 可选：处理跳过按钮 (如果你的 UI 中有跳过按钮)
const handleSkip = () => {
	navigateToLoginGuide();
};

// 页面卸载时确保清除定时器
onUnload(() => {
	if (timer) {
		clearInterval(timer);
		timer = null;
	}
});
</script>

<style lang="scss" scoped>
.launch-container {
	position: relative;
	width: 100vw;
	height: 100vh;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background-size: 100% 100%;
	.bg-gradient {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: -1;
	}

	.dots-btn {
		position: absolute;
		top: 50rpx;
		right: 120rpx;
		font-size: 60rpx;
		color: #333;
	}

	.target-btn {
		position: absolute;
		top: 50rpx;
		right: 40rpx;
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		border: 2rpx solid #333;
		display: flex;
		align-items: center;
		justify-content: center;

		.target-inner {
			width: 20rpx;
			height: 20rpx;
			border-radius: 50%;
			background-color: #333;
		}
	}

	.logo-container {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 400rpx;
		margin-bottom: 100rpx;
		image {
			width: 80%;
			margin: auto;
			height: 200rpx;
		}
	}

	.footer {
		position: absolute;
		bottom: 60rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.footer-text {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 10rpx;
		letter-spacing: 4rpx;
	}

	.countdown-container {
		position: absolute;
		top: 100rpx;
		left: 40rpx;
		display: flex;
		align-items: center;
	}

	.countdown {
		color: #fff;
		font-size: 32rpx;
		background: rgba(0, 0, 0, 0.5);
		padding: 10rpx 30rpx;
		border-radius: 40rpx;
		margin-right: 20rpx;
	}

	.skip-btn {
		color: #fff;
		font-size: 28rpx;
		background: rgba(0, 0, 0, 0.5);
		padding: 10rpx 30rpx;
		border-radius: 40rpx;
	}
}
</style>
