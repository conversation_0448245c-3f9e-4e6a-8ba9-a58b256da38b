<template>
    <view class="chart-container">
        <canvas v-show="visible" canvas-id="rateChart" id="rateChart" class="rate-chart" @tap="handleCanvasTap"></canvas>
    </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, getCurrentInstance, computed } from 'vue';

const props = defineProps({
    // 支持两种数据格式：单曲线数据和多曲线数据
    rateData: { type: Array, default: () => [] }, // 单曲线格式
    chartData: { type: Object, default: () => ({ categories: [], series: [] }) }, // 多曲线格式
    seriesIndex: { type: Number, default: 0 }, // 当使用chartData时，指定要显示的曲线索引
    specialPoints: { type: Array, default: () => [] },
    showExtremePoints: { type: Boolean, default: true }, // 是否显示最高点和最低点
    visible: { type: Boolean, default: true }, // 控制图表的显示与隐藏
    decimalPlaces: { type: Number, default: 4 }, // Y轴值小数位数，0表示整数
    yAxisUnit: { type: String, default: '%' } // Y轴单位，默认为百分比
});

const emits = defineEmits(['pointClick']);

const instance = getCurrentInstance(); // 获取当前组件实例

const chartContext = ref(null);
const canvasElement = ref(null);
const canvasWidth = ref(300);
const canvasHeight = ref(300); // Default height
const specialPointsCoordinates = ref([]);
const allPointsCoordinates = ref([]);
// 记录当前选中点的X坐标，用于绘制竖线
const selectedPointX = ref(null);
const selectedPointColor = ref('#FF9800');
const selectedPointIndex = ref(null); // 新增：记录选中点的索引
let debounceTimer = null;

// 添加动画相关变量
const animationInProgress = ref(false);
const animationProgress = ref(0);
const animationDuration = 800; // 动画持续时间(毫秒)
let animationTimer = null;
let animationStartTime = 0;

// 处理数据统一，支持两种数据格式
const normalizedRateData = computed(() => {
    // 如果有直接传入的rateData，优先使用
    if (Array.isArray(props.rateData) && props.rateData.length > 0) {
        return props.rateData;
    }
    
    // 否则从chartData中提取数据
    const chartData = props.chartData;
    if (!chartData || !chartData.categories || !chartData.series || chartData.categories.length === 0 || chartData.series.length === 0) {
        return [];
    }
    
    // 确定要使用的系列索引
    const seriesIndex = Math.min(props.seriesIndex, chartData.series.length - 1);
    if (seriesIndex < 0 || !chartData.series[seriesIndex] || !Array.isArray(chartData.series[seriesIndex].data)) {
        return [];
    }
    
    // 将多曲线数据转换为单曲线格式
    return chartData.categories.map((date, index) => {
        return {
            date,
            rate: index < chartData.series[seriesIndex].data.length ? 
                  chartData.series[seriesIndex].data[index] : 0
        };
    });
});

// 定义一个函数用于验证和清洗数据
const validateRateData = (data) => {
    if (!Array.isArray(data) || data.length === 0) {
        return [];
    }
    
    // 过滤掉无效数据，确保每个点都有有效的日期和利率值
    return data.filter(point => {
        // 检查点是否为对象
        if (!point || typeof point !== 'object') {
            return false;
        }
        
        // 检查date属性
        if (!point.date || typeof point.date !== 'string') {
            return false;
        }
        
        // 检查rate属性是否为数字，且不是NaN或Infinity
        if (point.rate === undefined || point.rate === null || 
            typeof point.rate !== 'number' || 
            isNaN(point.rate) || 
            !isFinite(point.rate)) {
            return false;
        }
        
        return true;
    });
};

// 计算最高点和最低点 - 使用计算属性缓存结果
const extremePoints = computed(() => {
    // 首先获取规范化后的数据
    const rateData = normalizedRateData.value;
    // 然后验证数据
    const validData = validateRateData(rateData);
    
    if (validData.length === 0) return [];
    
    // 找到最高点和最低点的索引
    let maxIndex = 0;
    let minIndex = 0;
    let maxRate = validData[0].rate;
    let minRate = validData[0].rate;
    
    validData.forEach((point, index) => {
        if (point.rate > maxRate) {
            maxRate = point.rate;
            maxIndex = index;
        }
        if (point.rate < minRate) {
            minRate = point.rate;
            minIndex = index;
        }
    });
    
            // 创建特殊点对象
        const points = [];
    
    // 如果最高点和最低点不是同一个点，则添加两个点
    if (maxIndex !== minIndex) {
        points.push({
            index: maxIndex,
            value: validData[maxIndex].rate,
            color: '#FF5722',
            // label: '最高点'
        });
        
        points.push({
            index: minIndex,
            value: validData[minIndex].rate,
            color: '#4CAF50',
            // label: '最低点'
        });
    } else if (validData.length > 0) {
        // 如果只有一个点，只添加一个特殊点
        points.push({
            index: maxIndex,
            value: validData[maxIndex].rate,
            color: '#FF9900',
            // label: '单点'
        });
    }
    
    return points;
});

// 合并用户指定的特殊点和极值点
const getAllSpecialPoints = () => {
    // 只返回极值点，忽略其他特殊点
    return extremePoints.value;
};

// 抽取Y轴范围计算为独立函数，避免重复计算
const calculateYAxisRange = (rateData, specialPoints = []) => {
    // 首先验证数据
    const validData = validateRateData(rateData);
    
    // 如果没有数据，设置默认值
    if (validData.length === 0) {
        return { yMin: 0, yMax: 1, yStep: 0.2 };
    }
    
    // 计算最大值和最小值
    let maxYVal = Math.max(...validData.map(item => item.rate));
    let minYVal = Math.min(...validData.map(item => item.rate));
    
    // 处理无效的极值情况
    if (!isFinite(maxYVal) || isNaN(maxYVal)) maxYVal = 1;
    if (!isFinite(minYVal) || isNaN(minYVal)) minYVal = 0;
    
    if (specialPoints.length > 0) {
        // 过滤出有效的特殊点
        const validSpecialPoints = specialPoints.filter(point => 
            point && typeof point.value === 'number' && 
            isFinite(point.value) && !isNaN(point.value)
        );
        
        if (validSpecialPoints.length > 0) {
            const specialMax = Math.max(...validSpecialPoints.map(item => item.value));
            const specialMin = Math.min(...validSpecialPoints.map(item => item.value));
            maxYVal = Math.max(maxYVal, specialMax);
            minYVal = Math.min(minYVal, specialMin);
        }
    }
    
    // 添加上下边距，确保数据点不会太靠近边缘
    const range = maxYVal - minYVal;
    const paddingPercent = 0.1; // 上下各添加10%的边距
    
    // 避免最小值和最大值相等的情况
    if (range === 0) {
        minYVal = minYVal > 0 ? minYVal * 0.9 : (minYVal === 0 ? 0 : minYVal * 1.1);
        maxYVal = maxYVal > 0 ? maxYVal * 1.1 : (maxYVal === 0 ? 0.5 : maxYVal * 0.9);
    } else {
        minYVal = minYVal - range * paddingPercent;
        maxYVal = maxYVal + range * paddingPercent;
    }
    
    // 对于接近0的情况，设置最小值为0
    if (minYVal > 0 && minYVal < range * 0.2) {
        minYVal = 0;
    }
    
    let yMin = minYVal;
    let yMax = maxYVal;
    
    // 计算合适的步长
    let yStep = (yMax - yMin) / 4; // 默认分为4段
    const niceSteps = [0.1, 0.2, 0.5, 1, 2, 5, 10, 20, 50, 100];
    
    if (!yStep || yStep <= 0) yStep = 0.1;
    let stepMagnitude = Math.pow(10, Math.floor(Math.log10(yStep)));
    let normalizedStep = yStep / stepMagnitude;
    let niceStep = niceSteps.find(s => s >= normalizedStep);
    if (!niceStep) niceStep = Math.ceil(normalizedStep);
    yStep = niceStep * stepMagnitude;
    
    // 调整最大最小值，使其为步长的整数倍
    yMin = Math.floor(yMin / yStep) * yStep;
    yMax = Math.ceil(yMax / yStep) * yStep;
    
    return { yMin, yMax, yStep };
};

// 防抖函数
function debounce(func, wait) {
    return (...args) => {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => {
            func(...args);
        }, wait);
    };
}

// 首先，提取X坐标计算逻辑为一个函数，确保一致性
const calculateXPosition = (index, dataLength, margin, chartWidth) => {
    const xPadding = chartWidth * 0.05; // 两端各缩进5%
    return margin.left + xPadding + ((dataLength > 1 ? (index / (dataLength - 1)) : 0.5) * (chartWidth - 2 * xPadding));
};

// 格式化数值的函数
const formatNumber = (value) => {
    if (props.decimalPlaces === 0) {
        // 整数格式
        return Math.round(value).toString() + props.yAxisUnit;
    } else {
        // 小数格式
        return value.toFixed(props.decimalPlaces) + props.yAxisUnit;
    }
};

// 初始化图表
const initChart = () => {
    // 获取父容器尺寸来设置Canvas尺寸
    nextTick(() => {
        if (!instance) {
            return;
        }
        
        // 使用uni.createSelectorQuery获取父容器尺寸
        const query = uni.createSelectorQuery().in(instance.proxy);
        query.select('.chart-container').boundingClientRect((data) => {
            if (data && typeof data.width === 'number' && typeof data.height === 'number') {
                const needsResize = canvasWidth.value !== data.width || canvasHeight.value !== data.height;
                
                // 只有当尺寸变化时才更新
                if (needsResize) {
                    canvasWidth.value = data.width;
                    canvasHeight.value = data.height;
                }
                
                // 直接重新创建canvas上下文，避免缓存问题
                const ctx = uni.createCanvasContext('rateChart', instance.proxy);
                if (ctx) {
                    chartContext.value = ctx;
                    // 确保在下一个渲染周期执行绘制
                    nextTick(() => {
                        drawChart();
                    });
                }
            }
        }).exec();
    });
};

// 绘制图表
const drawChart = (withAnimation = true) => {
    try {
        // 验证数据
        const validData = validateRateData(normalizedRateData.value);
        
        if (!chartContext.value || validData.length === 0) {
            // 如果没有数据或上下文，可以绘制一个提示信息或清空画布
            if (chartContext.value) {
                const ctx = chartContext.value;
                const width = canvasWidth.value;
                const height = canvasHeight.value;
                ctx.clearRect(0, 0, width, height);
                ctx.setFillStyle('#ffffff');
                ctx.fillRect(0, 0, width, height);
                ctx.setTextAlign('center');
                ctx.setFillStyle('#999999');
                ctx.setFontSize(14);
                ctx.fillText('暂无数据', width / 2, height / 2);
                ctx.draw(true);
            }
            return;
        }

        const ctx = chartContext.value;
        const width = canvasWidth.value;
        const height = canvasHeight.value;

        // 清除画布
        ctx.clearRect(0, 0, width, height);

        // 设置背景色
        ctx.setFillStyle('#ffffff');
        ctx.fillRect(0, 0, width, height);

        // 设置边距
        const margin = {
            top: 40,
            right: 0,
            bottom: 40,
            left: 60
        };

        // 计算绘图区域
        const chartWidth = width - margin.left - margin.right;
        const chartHeight = height - margin.top - margin.bottom;

        // Y轴单位
        ctx.setTextAlign('center');
        ctx.setFontSize(13);
        ctx.setFillStyle('#666666');
        ctx.fillText('利率(' + props.yAxisUnit + ')', margin.left - 30, margin.top - 20);

        // 计算Y轴范围 - 使用独立函数
        const { yMin, yMax, yStep } = calculateYAxisRange(validData, extremePoints.value);

        // 绘制网格线
        drawGridLines(ctx, margin, chartWidth, chartHeight, yMin, yMax, yStep);

        // 计算数据点坐标 - 优化：只计算一次
        const points = validData.map((point, index) => {
            const x = calculateXPosition(index, validData.length, margin, chartWidth);
            const y = margin.top + chartHeight - (yMax > yMin ? ((point.rate - yMin) / (yMax - yMin)) * chartHeight : chartHeight / 2);
            return { x, y };
        });

        // 绘制X轴刻度文本
        drawXAxisLabels(ctx, validData, points, margin, chartWidth, chartHeight);

        // 为每个数据点存储坐标但不绘制可见标注点 - 优化：只保存必要的信息
        allPointsCoordinates.value = points.map((point, index) => ({
            x: point.x,
            y: point.y,
            radius: 4,
            date: validData[index].date,
            value: validData[index].rate,
            color: '#FF9800'
        }));

        // 获取所有要绘制的特殊点(只包括极值点)
        const allSpecialPoints = extremePoints.value;
        specialPointsCoordinates.value = [];

        // 执行绘制网格线和坐标轴等静态元素
        ctx.draw(true);
        
        // 如果使用动画且有多个点，则启动动画
        if (withAnimation && points.length > 1) {
            // 停止之前可能在进行的动画
            clearAnimationTimer();
            
            // 初始化动画状态
            animationInProgress.value = true;
            animationProgress.value = 0;
            animationStartTime = Date.now();
            
            // 启动动画循环 - 仅绘制曲线部分
            animateDrawCurve(ctx, points, margin, chartHeight, yMin, yMax, allSpecialPoints);
        } else {
            // 不需要动画，直接绘制曲线
            drawCurveWithProgress(ctx, points, 1.0); // 1.0表示绘制100%
            
            // 绘制特殊点
            drawSpecialPoints(ctx, validData, allSpecialPoints, points, margin, chartHeight, yMin, yMax);
            
            // 执行绘制
            ctx.draw(true);
        }
    } catch (error) {
        // 出错时尝试清理和重置
        resetChart();
    }
};

// 抽取网格线绘制为单独函数
const drawGridLines = (ctx, margin, chartWidth, chartHeight, yMin, yMax, yStep) => {
    // 先绘制网格线
    ctx.beginPath();
    ctx.setStrokeStyle('#e0e0e0');
    ctx.setLineDash([5, 5]);
    
    // 绘制所有横向网格线，不包括底部的线（避免与X轴重叠）
    for (let y = yMin + yStep; y <= yMax + yStep * 0.001; y += yStep) {
        const yPos = margin.top + chartHeight - ((y - yMin) / (yMax - yMin)) * chartHeight;
        ctx.moveTo(margin.left, yPos);
        ctx.lineTo(margin.left + chartWidth, yPos);
    }
    ctx.stroke();
    
    // 重置虚线样式，准备绘制坐标轴
    ctx.setLineDash([]);
    
    // 绘制Y轴刻度文本
    ctx.setFillStyle('#999999');
    ctx.setFontSize(12);
    ctx.setTextAlign('right');
    for (let y = yMin; y <= yMax + yStep * 0.001; y += yStep) {
        const yPos = margin.top + chartHeight - ((y - yMin) / (yMax - yMin)) * chartHeight;
        // 绘制文本刻度
        ctx.fillText(formatNumber(y), margin.left - 10, yPos + 4);
    }

    // 绘制X轴实线
    ctx.beginPath();
    ctx.setStrokeStyle('#999999');
    ctx.setLineWidth(1);
    const xAxisY = margin.top + chartHeight;
    ctx.moveTo(margin.left, xAxisY);
    ctx.lineTo(margin.left + chartWidth, xAxisY);
    ctx.stroke();
};

// 抽取X轴标签绘制为单独函数
const drawXAxisLabels = (ctx, validData, points, margin, chartWidth, chartHeight) => {
    const xAxisY = margin.top + chartHeight;
    ctx.setTextAlign('center');
    ctx.setFillStyle('#999999');
    ctx.setFontSize(12);
    
    const dataLength = validData.length;
    if (dataLength === 1) {
        // 单个数据点情况 - 居中显示
        const label = validData[0].date;
        const centerX = margin.left + chartWidth / 2;
        ctx.fillText(label, centerX, xAxisY + 20);
    } else if (dataLength > 1) {
        // 多个数据点情况 - 显示多个时间刻度
        // 优化：计算步长，减少绘制次数
        const maxTicksToShow = Math.min(5, dataLength);
        const step = Math.max(1, Math.floor(dataLength / maxTicksToShow));
        
        // 绘制刻度和刻度文本
        for (let i = 0; i < dataLength; i += step) {
            // 确保显示第一个和最后一个点
            if (i === 0 || i + step >= dataLength) {
                const point = validData[i];
                const x = points[i].x;
                
                // 绘制刻度线
                ctx.beginPath();
                ctx.setStrokeStyle('#999999');
                ctx.setLineWidth(1);
                ctx.moveTo(x, xAxisY);
                ctx.lineTo(x, xAxisY + 5);
                ctx.stroke();
                
                // 获取日期文本和测量文本宽度
                const dateText = point.date;
                const textWidth = ctx.measureText ? ctx.measureText(dateText).width : dateText.length * 6; // 估算文本宽度
                
                // 绘制刻度文本
                if (i === 0) {
                    // 第一个点左对齐
                    ctx.setTextAlign('left');
                    ctx.fillText(dateText, x, xAxisY + 20);
                } else if (i === dataLength - 1 || i + step >= dataLength) {
                    // 最后一个点，确保不超出边界
                    ctx.setTextAlign('right');
                    
                    // 计算合适的x坐标，确保文本不会超出右边界
                    const rightBoundary = margin.left + chartWidth;
                    const safeX = Math.min(x, rightBoundary - 2); // 留出2px的边距
                    
                    ctx.fillText(dateText, safeX, xAxisY + 20);
                } else {
                    // 中间点居中对齐
                    ctx.setTextAlign('center');
                    ctx.fillText(dateText, x, xAxisY + 20);
                }
            }
        }
    }
};

// 使用动画进度绘制曲线
const drawCurveWithProgress = (ctx, points, progress) => {
    if (points.length <= 1) {
        // 只有一个点时
        if (points.length === 1) {
            // 单点绘制
            ctx.beginPath();
            ctx.setFillStyle('#FF9800');
            ctx.arc(points[0].x, points[0].y, 3, 0, Math.PI * 2);
            ctx.fill();
        }
        return;
    }

    // 计算当前动画帧应绘制到的点索引
    const maxPointIndex = Math.floor(progress * points.length);
    
    // 确保至少有两个点可绘制
    if (maxPointIndex < 1) return;
    
    const pointsToRender = points.slice(0, maxPointIndex + 1);
    
    // 绘制平滑曲线
    ctx.beginPath();
    ctx.setStrokeStyle('#FF9800');
    ctx.setLineWidth(1.5);
    ctx.setLineDash([]);
    ctx.setLineCap('round');
    ctx.setLineJoin('round');
    
    // 移到第一个点
    ctx.moveTo(pointsToRender[0].x, pointsToRender[0].y);
    
    // 优化：如果点数较少，使用贝塞尔曲线；如果点数较多，使用直线以提高性能
    const useDirectLines = pointsToRender.length > 50;
    
    if (useDirectLines) {
        // 点数较多时使用直线连接以提高性能
        for (let i = 1; i < pointsToRender.length; i++) {
            ctx.lineTo(pointsToRender[i].x, pointsToRender[i].y);
        }
    } else {
        // 点数较少时使用贝塞尔曲线使其平滑
        for (let i = 0; i < pointsToRender.length - 1; i++) {
            const currentPoint = pointsToRender[i];
            const nextPoint = pointsToRender[i + 1];
            const cp1x = currentPoint.x + (nextPoint.x - currentPoint.x) / 3;
            const cp1y = currentPoint.y;
            const cp2x = nextPoint.x - (nextPoint.x - currentPoint.x) / 3;
            const cp2y = nextPoint.y;
            ctx.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, nextPoint.x, nextPoint.y);
        }
    }
    
    // 添加阴影效果
    ctx.setShadow(0, 2, 4, 'rgba(255, 152, 0, 0.2)');
    ctx.stroke();
    ctx.setShadow(0, 0, 0, 'rgba(0, 0, 0, 0)');
};

// 绘制特殊点（最高点、最低点等）
const drawSpecialPoints = (ctx, validData, specialPoints, 
                          points, margin, chartHeight, yMin, yMax) => {
    // 计算图表区域宽度
    const chartWidth = canvasWidth.value - margin.left - margin.right;
    
    // 绘制特殊点
    specialPoints.forEach((point, pointIndex) => {
        if (point.index >= 0 && point.index < validData.length) {
            const dataPoint = validData[point.index];
            const x = points[point.index].x;
            const y = points[point.index].y;

            specialPointsCoordinates.value[pointIndex] = {
                x,
                y,
                radius: 4,
                date: dataPoint.date,
                value: point.value,
                color: point.color
            };

            ctx.beginPath();
            ctx.setFillStyle('rgba(255, 255, 255, 0.8)');
            ctx.arc(x, y, 6, 0, Math.PI * 2);
            ctx.fill();

            ctx.beginPath();
            ctx.setFillStyle(point.color);
            ctx.arc(x, y, 4, 0, Math.PI * 2);
            ctx.fill();

            // 绘制数值标注前先计算合适的位置
            const valueText = formatNumber(point.value);
            const textWidth = ctx.measureText ? ctx.measureText(valueText).width : valueText.length * 7;
            const textHeight = 12; // 文本高度估算
            const padding = 5; // 文本到边界的内边距

            // 默认在点上方显示
            let textX = x;
            let textY = y - 12;
            let textAlign = 'center';

            // 检查顶部边界
            if (textY - textHeight < margin.top + padding) {
                // 如果标注会超出顶部边界，将其放在点的下方
                textY = y + 18;
            }

            // 检查左右边界
            if (x - textWidth/2 < margin.left + padding) {
                // 如果会超出左边界，右对齐显示
                textX = margin.left + padding + textWidth/2;
                textAlign = 'center';
            } else if (x + textWidth/2 > margin.left + chartWidth - padding) {
                // 如果会超出右边界，左对齐显示
                textX = margin.left + chartWidth - padding - textWidth/2;
                textAlign = 'center';
            }

            ctx.setFillStyle(point.color);
            ctx.setTextAlign(textAlign);
            ctx.setFontSize(12);
            ctx.fillText(valueText, textX, textY);
            
            // 在特殊点上方显示标签
            if (point.label) {
                // 标签位置也需要调整
                let labelY = textY - 12; // 标签默认在值上方
                
                // 如果标签会超出顶部边界
                if (labelY - 10 < margin.top + padding) {
                    // 将标签放在值的下方（如果值在点下方）
                    if (textY > y) {
                        labelY = textY + 12;
                    } else {
                        // 移动到可见区域内
                        labelY = margin.top + padding + 10;
                    }
                }
                
                ctx.setFillStyle(point.color);
                ctx.setFontSize(10);
                ctx.fillText(point.label, textX, labelY);
            }
        }
    });
    
    // 如果有选中的点，绘制竖直虚线和半透明标注点
    if (selectedPointX.value !== null) {
        const x = selectedPointX.value;
        // 绘制竖直虚线
        ctx.beginPath();
        ctx.setStrokeStyle(selectedPointColor.value);
        ctx.setLineDash([5, 5]);
        ctx.setLineWidth(1);
        ctx.moveTo(x, margin.top);
        ctx.lineTo(x, margin.top + chartHeight);
        ctx.stroke();
        
        // 重置线型
        ctx.setLineDash([]);
        
        // 如果是普通数据点，绘制半透明标注
        if (selectedPointIndex.value !== null && selectedPointIndex.value >= 0 && selectedPointIndex.value < validData.length) {
            const pointCoord = allPointsCoordinates.value[selectedPointIndex.value];
            
            // 绘制半透明标注点
            ctx.beginPath();
            ctx.setFillStyle('rgba(255, 152, 0, 0.5)');
            ctx.arc(pointCoord.x, pointCoord.y, 4, 0, Math.PI * 2);
            ctx.fill();
            
            // 绘制数值标签，确保在可见范围内
            const valueText = formatNumber(validData[selectedPointIndex.value].rate);
            const textWidth = ctx.measureText ? ctx.measureText(valueText).width : valueText.length * 7;
            const textHeight = 12; // 文本高度估算
            const padding = 5; // 文本到边界的内边距
            
            // 默认位置
            let textX = pointCoord.x;
            let textY = pointCoord.y - 12;
            let textAlign = 'center';
            
            // 检查顶部边界
            if (textY - textHeight < margin.top + padding) {
                // 如果标注会超出顶部边界，将其放在点的下方
                textY = pointCoord.y + 18;
            }
            
            // 检查左右边界
            if (pointCoord.x - textWidth/2 < margin.left + padding) {
                // 如果会超出左边界，调整位置
                textX = margin.left + padding + textWidth/2;
            } else if (pointCoord.x + textWidth/2 > margin.left + chartWidth - padding) {
                // 如果会超出右边界，调整位置
                textX = margin.left + chartWidth - padding - textWidth/2;
            }
            
            ctx.setFillStyle('#FF9800');
            ctx.setTextAlign(textAlign);
            ctx.setFontSize(12);
            ctx.fillText(valueText, textX, textY);
        }
    }
};

// 动画曲线绘制
const animateDrawCurve = (ctx, points, margin, chartHeight, 
                         yMin, yMax, specialPoints) => {
    // 获取当前动画进度
    const elapsedTime = Date.now() - animationStartTime;
    let progress = Math.min(elapsedTime / animationDuration, 1.0);
    
    // 使用缓动函数让动画更平滑
    progress = easeInOutQuad(progress);
    animationProgress.value = progress;
    
    // 获取当前画布宽度
    const width = canvasWidth.value;
    
    // 清除上一帧的曲线部分（只清除曲线区域，保留坐标轴等）
    ctx.save(); // 保存当前上下文状态
    ctx.beginPath();
    ctx.rect(margin.left, margin.top, width - margin.left - margin.right, chartHeight);
    ctx.clip(); // 设置裁剪区域
    
    // 只清除曲线区域，不清除网格线（通过重绘背景色实现）
    ctx.setFillStyle('#ffffff');
    ctx.fillRect(margin.left, margin.top, width - margin.left - margin.right, chartHeight);
    
    // 重新绘制网格线，确保它们始终可见
    const { yMin: gridYMin, yMax: gridYMax, yStep } = calculateYAxisRange(validateRateData(normalizedRateData.value), []);
    
    // 只在裁剪区域内重绘网格线
    ctx.beginPath();
    ctx.setStrokeStyle('#e0e0e0');
    ctx.setLineDash([5, 5]);
    
    for (let y = gridYMin + yStep; y <= gridYMax + yStep * 0.001; y += yStep) {
        const yPos = margin.top + chartHeight - ((y - gridYMin) / (gridYMax - gridYMin)) * chartHeight;
        ctx.moveTo(margin.left, yPos);
        ctx.lineTo(margin.left + width - margin.right, yPos);
    }
    ctx.stroke();
    
    // 绘制曲线
    drawCurveWithProgress(ctx, points, progress);
    ctx.restore(); // 恢复上下文状态
    
    // 当动画完成时绘制特殊点
    if (progress >= 1.0) {
        drawSpecialPoints(ctx, validateRateData(normalizedRateData.value), specialPoints, 
                          points, margin, chartHeight, yMin, yMax);
        
        animationInProgress.value = false;
    }
    
    // 绘制当前帧
    ctx.draw(true);
    
    // 继续动画，如果未完成
    if (progress < 1.0) {
        animationTimer = setTimeout(() => {
            animateDrawCurve(ctx, points, margin, chartHeight, yMin, yMax, specialPoints);
        }, 16); // 约60fps
    }
};

// 缓动函数：使动画更平滑
const easeInOutQuad = (t) => {
    return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;
};

// 清除动画计时器
const clearAnimationTimer = () => {
    if (animationTimer) {
        clearTimeout(animationTimer);
        animationTimer = null;
    }
    animationInProgress.value = false;
};

// 优化防抖函数，提高性能
const debouncedDrawChart = debounce((withAnimation = true) => {
    drawChart(withAnimation);
}, 100); // 增加延迟时间以减少频繁重绘

// 重置图表 - 更新以处理动画
const resetChart = () => {
    selectedPointX.value = null;
    selectedPointIndex.value = null;
    specialPointsCoordinates.value = [];
    allPointsCoordinates.value = [];
    
    // 清除动画状态
    clearAnimationTimer();
    
    // 清除定时器
    clearTimeout(debounceTimer);
};

// 优化点击检测 - 使用更高效的算法
const handleCanvasTap = (e) => {
    const touch = e.touches[0] || e.changedTouches[0];
    if (!touch) return;

    const x = touch.x;
    const y = touch.y;
    const clickRadius = 20; // 点击检测半径

    // 获取画布上下文
    if (!chartContext.value) return;
    const ctx = chartContext.value;
    
    // 检测是否点击了特殊点（优先检查）
    let clickedSpecialPoint = false;
    for (const point of specialPointsCoordinates.value) {
        const distanceSquared = Math.pow(x - point.x, 2) + Math.pow(y - point.y, 2);
        // 使用平方距离比较，避免开平方运算提高性能
        if (distanceSquared <= Math.pow(point.radius * 2, 2)) {
            selectedPointX.value = point.x;
            selectedPointColor.value = point.color;
            selectedPointIndex.value = null;
            
            // 获取原始数据中可能存在的bAnalYieldAdd字段
            const rawData = props.chartData?.series?.[0]?.rawData;
            let bAnalYieldAdd;
            
            if (rawData && rawData.length) {
                // 查找匹配日期的原始数据
                const dataItem = rawData.find(item => item.formattedDate === point.date);
                if (dataItem) {
                    bAnalYieldAdd = dataItem.bAnalYieldAdd;
                }
            }
            
            emits('pointClick', {
                date: point.date,
                value: formatNumber(point.value),
                color: point.color,
                bAnalYieldAdd: bAnalYieldAdd
            });
            
            // 只绘制选中状态，不重新绘制整个图表
            drawSelectedPoint(ctx);
            clickedSpecialPoint = true;
            break;
        }
    }
    
    if (clickedSpecialPoint) return;
    
    // 如果没有点击特殊点，检查是否点击了普通数据点
    let nearestIndex = -1;
    let minDistanceSquared = Number.MAX_VALUE;
    
    // 优化：直接遍历原始点数组而不是计算出的点坐标
    for (let i = 0; i < allPointsCoordinates.value.length; i++) {
        const point = allPointsCoordinates.value[i];
        const distanceSquared = Math.pow(x - point.x, 2) + Math.pow(y - point.y, 2);
        
        if (distanceSquared < minDistanceSquared) {
            minDistanceSquared = distanceSquared;
            nearestIndex = i;
        }
    }
    
    // 如果找到了最近的点，且在有效点击范围内
    if (nearestIndex !== -1 && minDistanceSquared <= Math.pow(clickRadius, 2)) {
        const point = allPointsCoordinates.value[nearestIndex];
        selectedPointX.value = point.x;
        selectedPointColor.value = point.color;
        selectedPointIndex.value = nearestIndex;
        
        // 获取原始数据中可能存在的bAnalYieldAdd字段
        const rawData = props.chartData?.series?.[0]?.rawData;
        let bAnalYieldAdd;
        
        if (rawData && rawData.length) {
            // 获取当前选中的点的日期
            const pointDate = point.date;
            // 查找匹配日期的原始数据
            const dataItem = rawData.find(item => item.formattedDate === pointDate);
            if (dataItem) {
                bAnalYieldAdd = dataItem.bAnalYieldAdd;
            }
        }
        
        emits('pointClick', {
            date: point.date,
            value: formatNumber(point.value),
            color: point.color,
            bAnalYieldAdd: bAnalYieldAdd
        });
        
        // 只绘制选中状态，不重新绘制整个图表
        drawSelectedPoint(ctx);
        return;
    }
    
    // 如果点击了空白区域，清除竖线和标注点
    if (selectedPointX.value !== null) {
        selectedPointX.value = null;
        selectedPointIndex.value = null;
        
        // 此时需要重绘图表以清除选中状态
        drawChart(false); // 不使用动画重绘
    }
};

// 新增函数：只绘制选中点状态，不重新绘制整个图表
const drawSelectedPoint = (ctx) => {
    if (!chartContext.value) return;
    
    // 获取画布尺寸
    const width = canvasWidth.value;
    const height = canvasHeight.value;
    
    // 计算绘图区域
    const margin = {
        top: 40,
        right: 0,
        bottom: 40,
        left: 60
    };
    const chartHeight = height - margin.top - margin.bottom;
    const chartWidth = width - margin.left - margin.right;
    
    // 创建一个新的图层用于绘制选中态，保留之前的绘制内容
    ctx.save();
    
    // 清除之前可能绘制的选中态指示器（虚线和标注）
    // 注意：这里只清除中间区域，不清除网格线
    ctx.beginPath();
    ctx.rect(margin.left, margin.top, chartWidth, chartHeight);
    ctx.clip();
    
    // 清除区域但保留网格线
    ctx.setFillStyle('#ffffff');
    ctx.fillRect(margin.left, margin.top, chartWidth, chartHeight);
    
    // 重新绘制网格线
    const { yMin, yMax, yStep } = calculateYAxisRange(validateRateData(normalizedRateData.value), extremePoints.value);
    
    // 只在裁剪区域内重绘网格线
    ctx.beginPath();
    ctx.setStrokeStyle('#e0e0e0');
    ctx.setLineDash([5, 5]);
    
    for (let y = yMin + yStep; y <= yMax + yStep * 0.001; y += yStep) {
        const yPos = margin.top + chartHeight - ((y - yMin) / (yMax - yMin)) * chartHeight;
        ctx.moveTo(margin.left, yPos);
        ctx.lineTo(margin.left + chartWidth, yPos);
    }
    ctx.stroke();
    
    // 重置线型
    ctx.setLineDash([]);
    
    // 重新绘制曲线（完整的）
    const validData = validateRateData(normalizedRateData.value);
    const points = allPointsCoordinates.value;
    
    if (points.length > 1) {
        ctx.beginPath();
        ctx.setStrokeStyle('#FF9800');
        ctx.setLineWidth(1.5);
        ctx.setLineCap('round');
        ctx.setLineJoin('round');
        
        // 移到第一个点
        ctx.moveTo(points[0].x, points[0].y);
        
        // 绘制整条曲线
        const useDirectLines = points.length > 50;
        
        if (useDirectLines) {
            for (let i = 1; i < points.length; i++) {
                ctx.lineTo(points[i].x, points[i].y);
            }
        } else {
            for (let i = 0; i < points.length - 1; i++) {
                const currentPoint = points[i];
                const nextPoint = points[i + 1];
                const cp1x = currentPoint.x + (nextPoint.x - currentPoint.x) / 3;
                const cp1y = currentPoint.y;
                const cp2x = nextPoint.x - (nextPoint.x - currentPoint.x) / 3;
                const cp2y = nextPoint.y;
                ctx.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, nextPoint.x, nextPoint.y);
            }
        }
        
        ctx.setShadow(0, 2, 4, 'rgba(255, 152, 0, 0.2)');
        ctx.stroke();
        ctx.setShadow(0, 0, 0, 'rgba(0, 0, 0, 0)');
    }
    
    // 重绘所有特殊点
    const allSpecialPoints = extremePoints.value;
    
    allSpecialPoints.forEach((point, pointIndex) => {
        if (point.index >= 0 && point.index < validData.length) {
            const dataPoint = validData[point.index];
            const x = points[point.index].x;
            const y = points[point.index].y;

            ctx.beginPath();
            ctx.setFillStyle('rgba(255, 255, 255, 0.8)');
            ctx.arc(x, y, 6, 0, Math.PI * 2);
            ctx.fill();

            ctx.beginPath();
            ctx.setFillStyle(point.color);
            ctx.arc(x, y, 4, 0, Math.PI * 2);
            ctx.fill();

            // 绘制数值标注前先计算合适的位置
            const valueText = formatNumber(point.value);
            const textWidth = ctx.measureText ? ctx.measureText(valueText).width : valueText.length * 7;
            const textHeight = 12; // 文本高度估算
            const padding = 5; // 文本到边界的内边距

            // 默认在点上方显示
            let textX = x;
            let textY = y - 12;
            let textAlign = 'center';

            // 检查顶部边界
            if (textY - textHeight < margin.top + padding) {
                // 如果标注会超出顶部边界，将其放在点的下方
                textY = y + 18;
            }

            // 检查左右边界
            if (x - textWidth/2 < margin.left + padding) {
                // 如果会超出左边界，右对齐显示
                textX = margin.left + padding + textWidth/2;
                textAlign = 'center';
            } else if (x + textWidth/2 > margin.left + chartWidth - padding) {
                // 如果会超出右边界，左对齐显示
                textX = margin.left + chartWidth - padding - textWidth/2;
                textAlign = 'center';
            }

            ctx.setFillStyle(point.color);
            ctx.setTextAlign(textAlign);
            ctx.setFontSize(12);
            ctx.fillText(valueText, textX, textY);
            
            // 在特殊点上方显示标签
            if (point.label) {
                // 标签位置也需要调整
                let labelY = textY - 12; // 标签默认在值上方
                
                // 如果标签会超出顶部边界
                if (labelY - 10 < margin.top + padding) {
                    // 将标签放在值的下方（如果值在点下方）
                    if (textY > y) {
                        labelY = textY + 12;
                    } else {
                        // 移动到可见区域内
                        labelY = margin.top + padding + 10;
                    }
                }
                
                ctx.setFillStyle(point.color);
                ctx.setFontSize(10);
                ctx.fillText(point.label, textX, labelY);
            }
        }
    });
    
    // 如果有选中的点，绘制竖直虚线和半透明标注点
    if (selectedPointX.value !== null) {
        const x = selectedPointX.value;
        
        // 绘制竖直虚线
        ctx.beginPath();
        ctx.setStrokeStyle(selectedPointColor.value);
        ctx.setLineDash([5, 5]);
        ctx.setLineWidth(1);
        ctx.moveTo(x, margin.top);
        ctx.lineTo(x, margin.top + chartHeight);
        ctx.stroke();
        
        // 重置线型
        ctx.setLineDash([]);
        
        // 如果是普通数据点，绘制半透明标注
        if (selectedPointIndex.value !== null && selectedPointIndex.value >= 0 && selectedPointIndex.value < validData.length) {
            const pointCoord = allPointsCoordinates.value[selectedPointIndex.value];
            
            // 绘制半透明标注点
            ctx.beginPath();
            ctx.setFillStyle('rgba(255, 152, 0, 0.5)');
            ctx.arc(pointCoord.x, pointCoord.y, 4, 0, Math.PI * 2);
            ctx.fill();
            
            // 绘制数值标签，确保在可见范围内
            const valueText = formatNumber(validData[selectedPointIndex.value].rate);
            const textWidth = ctx.measureText ? ctx.measureText(valueText).width : valueText.length * 7;
            const textHeight = 12; // 文本高度估算
            const padding = 5; // 文本到边界的内边距
            
            // 默认位置
            let textX = pointCoord.x;
            let textY = pointCoord.y - 12;
            let textAlign = 'center';
            
            // 检查顶部边界
            if (textY - textHeight < margin.top + padding) {
                // 如果标注会超出顶部边界，将其放在点的下方
                textY = pointCoord.y + 18;
            }
            
            // 检查左右边界
            if (pointCoord.x - textWidth/2 < margin.left + padding) {
                // 如果会超出左边界，调整位置
                textX = margin.left + padding + textWidth/2;
            } else if (pointCoord.x + textWidth/2 > margin.left + chartWidth - padding) {
                // 如果会超出右边界，调整位置
                textX = margin.left + chartWidth - padding - textWidth/2;
            }
            
            ctx.setFillStyle('#FF9800');
            ctx.setTextAlign(textAlign);
            ctx.setFontSize(12);
            ctx.fillText(valueText, textX, textY);
        }
    }
    
    ctx.restore();
    
    // 使用true参数绘制，保留之前的内容
    ctx.draw(true);
};

// 强制初始化画布
const forceReinitialize = () => {
    // 释放旧的上下文
    chartContext.value = null;
    canvasElement.value = null;
    
    // 重置状态
    resetChart();
    
    // 延迟重新初始化
    setTimeout(() => {
        initChart();
    }, 50);
};

// 修改数据监听，确保深度变化时能正确处理
watch([() => normalizedRateData.value, () => props.specialPoints, () => props.showExtremePoints], (newValues, oldValues) => {
    // 如果图表不可见，则不进行绘制
    if (!props.visible) return;
    
    // 判断是否是完全不同的数据集（通过长度快速判断）
    const isCompletelyNewData = 
        !oldValues[0] || 
        !newValues[0] || 
        oldValues[0].length !== newValues[0].length || 
        JSON.stringify(newValues[0]) !== JSON.stringify(oldValues[0]);
    
    // 确保在 DOM 更新后执行初始化
    if (isCompletelyNewData) {
        // 数据完全改变时重新初始化
        forceReinitialize();
    } else {
        // 部分更新时使用防抖重绘
        debouncedDrawChart();
    }
}, { deep: true, immediate: true }); // 添加immediate以确保首次加载时绘制

// 添加对chartData和seriesIndex的监听
watch([() => props.chartData, () => props.seriesIndex], () => {
    // 图表不可见时不进行绘制
    if (!props.visible) return;
    
    // 当chartData或seriesIndex变化时，重新初始化图表
    forceReinitialize();
}, { deep: true, immediate: true }); // 添加immediate以确保首次加载时绘制

// 优化可见性变化处理，确保切换显示状态时正确重建
watch(() => props.visible, (newVal, oldVal) => {
    if (newVal && !oldVal) {
        // 从隐藏变为显示时，完全重新初始化
        nextTick(() => {
            forceReinitialize();
        });
    } else if (!newVal && chartContext.value) {
        // 变为隐藏时清理资源
        resetChart();
    }
});

// 在 onMounted 添加额外调试信息
onMounted(() => {
    // 组件挂载后初始化图表，仅当visible为true时初始化
    if (props.visible) {
        // 使用 nextTick 确保 canvas 元素已准备好
        nextTick(() => {
            initChart();
        });
    }
});

// 为了确保样式正确，检查容器的CSS样式
watch([() => canvasWidth.value, () => canvasHeight.value], ([newWidth, newHeight]) => {
    // 监听画布尺寸变化
}, { immediate: true });

onUnmounted(() => {
    // 清理防抖定时器
    clearTimeout(debounceTimer);
    // 清理动画计时器
    clearAnimationTimer();
    // 释放引用
    chartContext.value = null;
    canvasElement.value = null;
    specialPointsCoordinates.value = [];
    allPointsCoordinates.value = [];
    selectedPointX.value = null;
    selectedPointIndex.value = null;
});

</script>

<style lang="scss" scoped>
.chart-container {
    margin: 0;
    /* Remove margin if parent handles spacing */
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    /* Ensure container takes necessary space */
    box-sizing: border-box;
    width: 100%;
    height: 300px; /* 设置固定高度 */
    position: relative; /* 确保容器有定位 */
}

.rate-chart {
    width: 100% !important;
    height: 100% !important; /* 使canvas高度为父容器的100% */
    display: block; /* 确保canvas是块级元素 */
    position: absolute; /* 绝对定位确保尺寸正确 */
    top: 0;
    left: 0;
}
</style>