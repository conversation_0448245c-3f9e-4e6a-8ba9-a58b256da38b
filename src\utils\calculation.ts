/**
 * 数据计算相关工具函数
 */

/**
 * 计算数组总和
 * @param arr 要计算的数组
 * @returns 数组元素之和
 */
export const sum = (arr: number[]): number => {
	if (!arr || arr.length === 0) return 0;
	return arr.reduce((total, current) => total + current, 0);
};

/**
 * 计算数组平均值
 * @param arr 要计算的数组
 * @returns 数组元素的平均值
 */
export const average = (arr: number[]): number => {
	if (!arr || arr.length === 0) return 0;
	return sum(arr) / arr.length;
};

/**
 * 格式化数字为指定小数位数
 * @param num 要格式化的数字
 * @param digits 小数位数，默认为2
 * @returns 格式化后的数字
 */
export const formatNumber = (num: number, digits: number = 2): number => {
	return Number(num.toFixed(digits));
};

/**
 * 将百分比值转换为小数
 * @param percent 百分比值（如：5.25表示5.25%）
 * @returns 转换后的小数值
 */
export const percentToDecimal = (percent: number): number => {
	return percent / 100;
};

/**
 * 将小数转换为百分比值
 * @param decimal 小数值（如：0.0525表示5.25%）
 * @returns 转换后的百分比值
 */
export const decimalToPercent = (decimal: number): number => {
	return decimal * 100;
};

/**
 * 计算两个数之间的差值
 * @param a 第一个数
 * @param b 第二个数
 * @returns 两数差值的绝对值
 */
export const difference = (a: number, b: number): number => {
	return Math.abs(a - b);
};

/**
 * 计算变化率（百分比）
 * @param oldValue 原始值
 * @param newValue 新值
 * @returns 变化率（百分比）
 */
export const changeRate = (oldValue: number, newValue: number): number => {
	if (oldValue === 0) return 0;
	return formatNumber(((newValue - oldValue) / Math.abs(oldValue)) * 100);
};

/**
 * 处理期限表示，将1M, 2M, 1Y等转换为对应的数值
 * 例如：1M -> 0.08 (1/12)，1Y -> 1
 * @param term 期限表示，如'1M'(1个月),'1Y'(1年)
 * @returns 转换后的数值，保留两位小数
 */
export const convertTerm = (term: string): number => {
	if (!term) return 0;

	const value = parseFloat(term);
	const unit = term.replace(/[\d.]/g, '');

	if (unit === 'M') {
		return formatNumber(value / 12);
	} else if (unit === 'Y') {
		return value;
	}

	return 0;
};

/**
 * 将时间戳转换为YYYY-MM-DD HH:MM:SS格式的日期时间字符串
 * @param timestamp 时间戳（毫秒）
 * @param defaultValue 默认值，当timestamp无效时返回，默认为'--'
 * @returns 格式化后的日期时间字符串
 */
export const formatTimestampToDate = (timestamp: number | string | null | undefined, defaultValue: string = '--'): string => {
	if (!timestamp) return defaultValue;
	const date = new Date(Number(timestamp));
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0'); 
	const day = String(date.getDate()).padStart(2, '0');
	const hours = String(date.getHours()).padStart(2, '0');
	const minutes = String(date.getMinutes()).padStart(2, '0');
	const seconds = String(date.getSeconds()).padStart(2, '0');
	return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}; 