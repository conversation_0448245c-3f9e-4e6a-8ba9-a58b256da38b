// src/api/user.ts
import request from '../utils/request' // 导入封装好的请求函数
import type { ResultData, RequestOptions } from '../utils/request' // 导入类型定义

// 定义获取用户信息的接口函数
export function getUserInfo(userId: string): Promise<ResultData> {
    // 准备请求选项
    const options: RequestOptions = {
        url: `/user/${userId}`, // 具体的 API 端点
        method: 'GET',          // 请求方法
        sld: true               // 请求时显示加载提示
        // 这里可以根据需要添加其他选项，如 data, pem 等
    }
    // 调用 request 函数发起请求
    return request(options)
}

// 定义更新用户信息的接口函数
interface UserProfile {
    name?: string;
    email?: string;
    // ... 其他字段
}

export function updateUserProfile(userId: string, profileData: UserProfile): Promise<ResultData> {
    // 准备请求选项
    const options: RequestOptions = {
        url: `/user/${userId}`, // 具体的 API 端点
        method: 'PUT',          // 请求方法
        data: profileData,      // 需要更新的用户信息数据
        sld: true,              // 请求时显示加载提示
        pem: true               // 假设这个接口的错误需要页面单独处理
    }
    // 调用 request 函数发起请求
    return request(options)
}

// 定义一个需要 POST 请求并传递参数的示例
interface LoginCredentials {
    username: string;
    password?: string; // 密码可能是可选的，取决于认证方式
}

export function login(credentials: LoginCredentials): Promise<ResultData> {
    const options: RequestOptions = {
        url: '/auth/login',
        method: 'POST',
        data: credentials, // 将登录凭证作为请求体发送
        sld: true
    }
    return request(options)
}

// 定义获取数据列表的接口函数（示例）
export function getItemList(params?: any){
    // 准备请求选项
    const options: RequestOptions = {
        url: '/wx/carousel/list', // 假设的列表数据 API 端点
        method: 'GET',
        data: params, // 可以传递分页、过滤等参数
        sld: true     // 显示加载提示
    }
    // 调用 request 函数发起请求
    return request(options)
}

// 你可以根据实际需要继续添加更多的 API 函数... 