<template>
    <view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
        <!-- 固定的头部区域 -->
        <view class="fixed-header">
            <CustomHead title="政策利率" />
        </view>

        <!-- 顶部tab切换 -->
        <view class="tab-container">
            <view class="tab-item" :class="{ active: activeTab === 'lpr' }" @click="switchTab('lpr')">LPR</view>
            <view class="tab-item" :class="{ active: activeTab === 'cd' }" @click="switchTab('cd')">同业存单</view>
            <view class="tab-item" :class="{ active: activeTab === 'shibor' }" @click="switchTab('shibor')">SHIBOR
            </view>
        </view>

        <!-- 可滚动的内容区域 -->
        <view class="content-wrapper">
            <scroll-view class="scrollable-content" scroll-y :show-scrollbar="false" enhanced>
                <view class="chart-card">
                    <view class="card-content">

                        <!-- ==================== 期限选择 ==================== -->
                        <!-- LPR和SHIBOR标签下显示PeriodSelector组件 -->
                        <PeriodSelector v-if="activeTab !== 'cd'" :periodOptions="periodOptions"
                            @periodChange="handlePeriodChange" />

                        <!-- 同业存单标签下显示评级和期限选择组件 -->
                        <view class="rating-period-selector" v-if="activeTab === 'cd'">
                            <view class="selector-item" @click="showRatingPickerDialog">
                                <text>评级:</text>
                                <view class="dropdown-wrapper">
                                    <text class="selected-value">{{ selectedRating }}</text>
                                    <text class="dropdown-arrow">▼</text>
                                </view>
                            </view>

                            <view class="divider"></view>

                            <view class="selector-item" @click="showPeriodPickerDialog">
                                <text>期限:</text>
                                <view class="dropdown-wrapper">
                                    <text class="selected-value">{{ displayPeriod }}</text>
                                    <text class="dropdown-arrow">▼</text>
                                </view>
                            </view>
                        </view>

                        <!-- 评级选择弹出框 -->
                        <SelectionPopup v-model:visible="showRatingPicker" title="选择评级" :options="ratingOptions"
                            :defaultSelected="selectedRating" @confirm="handleRatingConfirm"
                            @cancel="handleRatingCancel" />

                        <!-- 期限选择弹出框 -->
                        <SelectionPopup v-model:visible="showPeriodPicker" :labelField="'cnname'"
                            :valueField="'itemcode'" title="发行期限" :options="cdPeriodOptions"
                            :defaultSelected="selectedPeriod" @confirm="handlePeriodConfirm"
                            @cancel="handlePeriodCancel" />

                        <!-- ==================== 利率信息展示 ==================== -->
                        <RateInfoDisplay :rateValue="currentRate" :changeValue="rateDiff" :timeValue="updateTime" />

                        <!-- ==================== 时间筛选器 ==================== -->
                        <TimeFilterSelect :isShow="true" @filterChange="handleTimeFilterChange"
                            @openCalendar="toggleModal" />

                        <!-- ==================== 图表上方当前数据点信息 ==================== -->
                        <view class="chart-data-point">
                            <view class="point-date">
                                <view class="point-indicator"></view>
                                <text class="number-font">{{ latestDataDate }}</text>
                            </view>
                            <view class="point-value">估值: <text class="number-font">{{ latestDataValue }}</text></view>
                            <view class="point-change"
                                :class="latestDataChange > 0 ? 'up' : (latestDataChange < 0 ? 'down' : 'flat')">
                                较昨日: <text class="number-font">{{ latestDataChange > 0 ? '+' : '' }}{{ latestDataChange
                                    }}BP</text>
                            </view>
                        </view>

                        <!-- ==================== 图表展示区 ==================== -->
                        <view class="chart-component">
                            <RateChart :chartData="chartStaticData" @point-touch="handleChartPointTouch" />
                        </view>
                    </view>
                </view>

                <!-- 期限列表卡片 -->
                <PeriodListCard :bondTabType="activeTab" :bondType="selectedBondType" :periodList="periodList"
                    :rating="selectedRating" :columns="[
                        { title: '期限', field: 'period' },
                        { title: '最新利率(%)', field: 'value' },
                        { title: '涨跌BP', field: 'change' }
                    ]" />

                <!-- 底部留白区域，确保内容可以完全滚动显示 -->
                <view class="bottom-space"></view>
            </scroll-view>
        </view>

        <!-- 日期选择组件（浮动在内容上方） -->
        <DateRangePicker v-model:visible="showModal" :defaultStartDate="startDate" :defaultEndDate="endDate"
            @dateRangeChange="handleDateRangeChange" @update:visible="handleModalVisibleChange" />
    </view>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import PeriodListCard from '@/components/MarketRate/PeriodListCard.vue';
import CustomHead from '@/components/head/head.vue';
import DateRangePicker from '@/components/calendar/DateRangePicker.vue';
import TimeFilterSelect from '@/components/common/TimeFilterSelect.vue';
import RateChart from '@/components/MarketRate/RateChart.vue';
import SelectionPopup from '@/components/common/SelectionPopup.vue';
import PeriodSelector from '@/components/common/PeriodSelector.vue';
import RateInfoDisplay from '@/components/MarketRate/RateInfoDisplay.vue';
import { getAssetUrl } from '@/config/assets';

import { getMarketRate, getLPRRate, getSHIBORRate } from '@/api/marketRate';
import { getCDTerm, getLPRTerm, getSHIBORTerm, getSql } from '@/api/dataDict';

// 通用的bAnalYieldAdd字段处理函数
const formatYieldChange = (yieldAddValue) => {
    if (yieldAddValue === null || yieldAddValue === undefined) {
        return 0;
    }
    
    // 如果是数字类型，直接返回
    if (typeof yieldAddValue === 'number') {
        return yieldAddValue;
    }
    
    // 如果是字符串类型，需要处理
    if (typeof yieldAddValue === 'string') {
        // 去除可能的加号，然后转换为数字
        const cleanValue = yieldAddValue.replace(/^\+/, '');
        const numValue = parseFloat(cleanValue);
        return isNaN(numValue) ? 0 : numValue;
    }
    
    // 其他情况返回0
    return 0;
};

const ccid = ref('c7ef8f306c8645cbb9feaf0c3bf5e91e');
const ownedModuleid = ref('708631605142536192');
const bAnalCurveName = ref('LPR');

// 加载状态控制
const isLoadingPeriodList = ref(false);
const isLoadingChartData = ref(false);

// 格式化日期：处理两种格式，统一转换为YYYY/MM/DD格式
// 1. LPR、SHIBOR和同业存单: 20250220格式转换为2025/02/20格式
const formatTradeDate = (dateStr, tabType = null) => {
    if (!dateStr || typeof dateStr !== 'string') {
        return '--';
    }

    // 统一处理8位数字格式的日期
    if (dateStr.length === 8) {
        const year = dateStr.substring(0, 4);
        const month = dateStr.substring(4, 6);
        const day = dateStr.substring(6, 8);
        return `${year}/${month}/${day}`;
    }

    // 如果已经是包含/的格式，直接返回
    if (dateStr.includes('/')) {
        return dateStr;
    }

    // 其他格式返回--
    return '--';
};

// 更新利率信息展示
const updateRateInfoDisplay = () => {
    if (periodList.value.length === 0) {
        resetRateInfoDisplay();
        return;
    }

    // 根据当前选中的期限找到对应的数据
    let targetData;

    if (activeTab.value === 'cd') {
        // 同业存单，需要获取对应的cnname进行匹配
        const selectedPeriodItem = cdPeriodOptions.value.find(item => item.itemcode === selectedPeriod.value);
        const selectedPeriodName = selectedPeriodItem ? selectedPeriodItem.cnname : '';
        targetData = periodList.value.find(item => item.period === selectedPeriodName);
    } else {
        // LPR和SHIBOR，需要获取对应的cnname进行匹配
        const activePeriodItem = periodOptions.value.find(item => item.itemcode === activePeriod.value);
        const activePeriodName = activePeriodItem ? activePeriodItem.cnname : '';

        targetData = periodList.value.find(item => item.period === activePeriodName);
    }

    // 如果找不到对应的数据，就显示--

    if (targetData) {
        // 更新显示信息
        currentRate.value = targetData.value || '--';
        // 确保涨跌值是数字类型，或者在没有数据时设置为'--'
        rateDiff.value = targetData.change !== undefined && targetData.change !== null ?
            Number(targetData.change) : '--';
        updateTime.value = formatTradeDate(targetData.tradeDt, activeTab.value);
    } else {
        // 如果没有找到任何数据，重置显示
        resetRateInfoDisplay();
    }
}

// 重置利率信息显示
const resetRateInfoDisplay = () => {
    currentRate.value = '--';
    rateDiff.value = '--'; // 将类型改为字符串，这样会显示"--"
    updateTime.value = '--';
}

// 当前激活的标签页
const activeTab = ref('lpr'); // 默认显示LPR

// ==================== 页面变量区域 ====================
// Tab和基础配置相关
const selectedBondType = ref('LPR'); // 当前选中的债券类型

// 期限相关
const periodOptions = ref(['1Y', '5Y']);
// 为三个标签页创建独立的期限变量
const lprPeriod = ref('');
const shiborPeriod = ref('');
const activePeriod = ref(''); // 当前活动的期限，根据当前标签页设置
const activePeriodValue = ref(1); // 默认为1Y对应的数值

// 同业存单评级和期限选择相关变量
const selectedRating = ref('');
const selectedPeriod = ref(''); // 默认值改为itemcode
const showRatingPicker = ref(false);
const showPeriodPicker = ref(false);
const ratingOptions = ref([]);
const cdPeriodOptions = ref([]);

// 市场利率参数
// 计算近一年的日期范围
const today = new Date();
const oneYearAgo = new Date();
oneYearAgo.setFullYear(today.getFullYear() - 1);

// 格式化为YYYY-MM-DD格式
const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};

const tradeDtStart = ref(formatDate(oneYearAgo));
const tradeDtEnd = ref(formatDate(today));
const activeTimeFilter = ref('1year');

// 当前数据显示相关
const currentRate = ref('--');
const rateDiff = ref('--');

// 弹窗和日期相关
const showModal = ref(false); // 初始不显示弹窗
const startDate = ref(new Date('2023-10-09'));
const endDate = ref(new Date());

// 更新时间
const updateTime = ref('--');

// 图表上方数据点信息 - 显示最新数据
const latestDataDate = ref('--');
const latestDataValue = ref('--');
const latestDataChange = ref(0);

// 期限列表数据
const periodList = ref([]);

// 图表数据（采用市场利率页面的格式）
const chartStaticData = ref([]);

// 删除特殊点计算，使用市场利率页面的简单数组格式

// 统一的数据加载控制器，用于防止重复请求和控制请求顺序
const loadData = (options = {}) => {
    const {
        loadPeriodOptions = false,
        loadPeriodList = false,
        loadChartData = false,
        tabChanged = false
    } = options;

    // 标签页切换需要加载期限选项
    if (loadPeriodOptions) {
        if (activeTab.value === 'lpr') {
            fetchLPRTerm().then(() => {
                // 只有在标签切换时才在期限选项加载完成后加载图表数据
                if (tabChanged && loadChartData && !isLoadingChartData.value) {
                    isLoadingChartData.value = true;
                    fetchChartData().then(() => {
                        isLoadingChartData.value = false;
                    }).catch(() => {
                        isLoadingChartData.value = false;
                    });
                }
            });
        } else if (activeTab.value === 'cd') {
            fetchCDTerm().then(() => {
                // 只有在标签切换时才在期限选项加载完成后加载图表数据
                if (tabChanged && loadChartData && !isLoadingChartData.value) {
                    isLoadingChartData.value = true;
                    fetchChartData().then(() => {
                        isLoadingChartData.value = false;
                    }).catch(() => {
                        isLoadingChartData.value = false;
                    });
                }
            });
        } else if (activeTab.value === 'shibor') {
            fetchSHIBORTerm().then(() => {
                // 只有在标签切换时才在期限选项加载完成后加载图表数据
                if (tabChanged && loadChartData && !isLoadingChartData.value) {
                    isLoadingChartData.value = true;
                    fetchChartData().then(() => {
                        isLoadingChartData.value = false;
                    }).catch(() => {
                        isLoadingChartData.value = false;
                    });
                }
            });
        }
    }

    // 加载期限列表 - 独立处理
    if (loadPeriodList && !isLoadingPeriodList.value) {
        isLoadingPeriodList.value = true;
        fetchPeriodList().then(() => {
            isLoadingPeriodList.value = false;
        }).catch(() => {
            isLoadingPeriodList.value = false;
        });
    }

    // 加载图表数据 - 完全独立于期限列表
    // 只有非标签页切换时才直接加载图表数据
    if (loadChartData && !isLoadingChartData.value && !tabChanged) {
        isLoadingChartData.value = true;
        fetchChartData().then(() => {
            isLoadingChartData.value = false;
        }).catch(() => {
            isLoadingChartData.value = false;
        });
    }
};

// ==================== 切换标签页 ====================
const switchTab = (tab) => {
    activeTab.value = tab;

    if (tab === 'lpr') {
        selectedRating.value = '';
        selectedPeriod.value = '';

        bAnalCurveName.value = 'LPR';
        selectedBondType.value = 'LPR';
        ccid.value = 'c7ef8f306c8645cbb9feaf0c3bf5e91e';

        // 设置LPR期限为之前保存的值
        activePeriod.value = lprPeriod.value;
    } else if (tab === 'cd') {
        selectedRating.value = 'AAA-';
        selectedPeriod.value = '';

        bAnalCurveName.value = '中债同业存单收益率曲线';
        selectedBondType.value = '中债同业存单收益率曲线';
        ccid.value = '9bedcb8dbb2640d191160e6948dd4b6c';
    } else if (tab === 'shibor') {
        selectedRating.value = '';
        selectedPeriod.value = '';

        bAnalCurveName.value = 'SHIBOR';
        selectedBondType.value = 'SHIBOR';
        ccid.value = '5fc2fa869a0447d096a5457963787610';

        // 设置SHIBOR期限为之前保存的值，如果没有则设为空
        activePeriod.value = shiborPeriod.value || '';
    }

    // 统一调用数据加载，标签页变化时需要获取新的期限选项、期限列表和图表数据
    loadData({
        loadPeriodOptions: true,
        loadPeriodList: true,
        loadChartData: true,
        tabChanged: true
    });
};

// 获取LPR的期限字典
const fetchLPRTerm = async () => {
    const data = "LPR_YEAR"
    try {
        const res = await getLPRTerm(data);
        periodOptions.value = res.data.data.map(item => ({
            cnname: item.cnname,
            itemcode: item.itemcode
        }));

        // 如果还没有选择期限，设置默认值
        if (!lprPeriod.value && periodOptions.value.length > 0) {
            lprPeriod.value = periodOptions.value[0].itemcode;
            activePeriod.value = lprPeriod.value;
        }
    } catch (error) {
        // 获取LPR期限字典失败
    }
}

// 获取SHIBOR期限字典
const fetchSHIBORTerm = async () => {
    const data = "SHIBOR_YEAR"
    try {
        const res = await getSHIBORTerm(data);
        periodOptions.value = res.data.data.map(item => ({
            cnname: item.cnname,
            itemcode: item.itemcode
        }));

        // 如果还没有选择期限，设置默认值
        if (!shiborPeriod.value && periodOptions.value.length > 0) {
            shiborPeriod.value = periodOptions.value[0].itemcode;
            activePeriod.value = shiborPeriod.value;
        }
    } catch (error) {
        // 获取SHIBOR期限字典失败
    }
}

// 获取同业存单期限字典
const fetchCDTerm = async () => {
    const data = [
        "TYCD_MAIN_RATING",
        "BOND_TERM_POLICY_CURVE_RATIO"
    ]
    try {
        const res = await getCDTerm(data);
        cdPeriodOptions.value = res.data.data.BOND_TERM_POLICY_CURVE_RATIO.map(item => ({
            cnname: item.cnname,
            itemcode: item.itemcode
        }));
        ratingOptions.value = res.data.data.TYCD_MAIN_RATING.map(item => ({
            cnname: item.cnname,
            itemcode: item.itemcode
        }));

        // 设置默认评级为AAA-，找不到则选第一个选项
        if (ratingOptions.value.length > 0 && !selectedRating.value) {
            const aaaRating = ratingOptions.value.find(item => item.cnname === 'AAA-');
            selectedRating.value = aaaRating ? aaaRating.itemcode : ratingOptions.value[0].itemcode;
        }
        if (cdPeriodOptions.value.length > 0 && !selectedPeriod.value) {
            selectedPeriod.value = cdPeriodOptions.value[0].itemcode;
        }
    } catch (error) {
        // 获取同业存单期限字典失败
    }
}

// 获取期限列表数据
const fetchPeriodList = async () => {
    const data = {
        params: {
            b_anal_curvename: selectedBondType.value,
            ownedModuleid: ownedModuleid.value,
            ccid: ccid.value,
            main_rating: selectedRating.value,
        },
        page: {
            pageNo: 1,
            pageSize: 20
        }
    }
    try {
        const res = await getSql(data);
        if (res.data && res.data.data && res.data.data.pageInfo && res.data.data.pageInfo.list && res.data.data.pageInfo.list.length > 0) {
            periodList.value = res.data.data.pageInfo.list.map(item => ({
                period: item.bAnalCurveterm,
                value: parseFloat(item.bAnalYield) || 0, // 转换为数字类型
                change: formatYieldChange(item.bAnalYieldAdd),
                tradeDt: item.tradeDt
            }));

            // 获取数据后，更新利率信息展示
            updateRateInfoDisplay();
        } else {
            periodList.value = []
            // 无数据时重置显示
            resetRateInfoDisplay();
        }
    } catch (error) {
        // 获取期限列表数据失败
        // 错误时重置显示
        resetRateInfoDisplay();
    }
}

// 获取图表数据 - 简化版本
const fetchChartData = async () => {
    // 确定当前选中的期限值
    let selectedPeriodText;

    if (activeTab.value === 'cd') {
        // 同业存单，需要获取对应的cnname进行匹配
        const selectedPeriodItem = cdPeriodOptions.value.find(item => item.itemcode === selectedPeriod.value);
        selectedPeriodText = selectedPeriodItem ? selectedPeriodItem.itemcode : '';

        // 对于同业存单，如果还没有评级或期限选择，使用默认值
        if (!selectedPeriodText && cdPeriodOptions.value.length > 0) {
            selectedPeriod.value = cdPeriodOptions.value[0].itemcode;
            selectedPeriodText = cdPeriodOptions.value[0].itemcode;
        }

        if (!selectedRating.value && ratingOptions.value.length > 0) {
            selectedRating.value = ratingOptions.value[0].itemcode;
        }
    } else {
        // LPR和SHIBOR，需要获取对应的cnname进行匹配
        const activePeriodItem = periodOptions.value.find(item => item.itemcode === activePeriod.value);
        selectedPeriodText = activePeriodItem ? activePeriodItem.itemcode : '';

        // 如果还没有期限选择，使用默认值
        if (!selectedPeriodText && periodOptions.value.length > 0) {
            if (activeTab.value === 'lpr') {
                lprPeriod.value = periodOptions.value[0].itemcode;
                activePeriod.value = lprPeriod.value;
            } else {
                shiborPeriod.value = periodOptions.value[0].itemcode;
                activePeriod.value = shiborPeriod.value;
            }
            selectedPeriodText = periodOptions.value[0].itemcode;
        }
    }

    // 无效期限时不请求数据
    if (!selectedPeriodText) {
        // 无效的期限选择，不请求图表数据
        updateChartData([]);
        return;
    }

    // 同业存单需要评级参数，确保评级存在
    if (activeTab.value === 'cd' && !selectedRating.value && ratingOptions.value.length > 0) {
        selectedRating.value = ratingOptions.value[0].itemcode;
    }

    try {
        if (activeTab.value === 'lpr') {
            const params = {
                bAnalCurveterm: selectedPeriodText,
                tradeDtStart: tradeDtStart.value,
                tradeDtEnd: tradeDtEnd.value
            }
            const res = await getLPRRate(params);
            const periodData = res.data?.data;
            // 确保在没有数据时也更新图表
            updateChartData(periodData || []);
        } else if (activeTab.value === 'shibor') {
            const params = {
                bAnalCurvetermList: [selectedPeriodText],
                date: [tradeDtStart.value, tradeDtEnd.value],
                radioDate: 12,
                ownedModuleid: ownedModuleid.value,
                tradeDtStart: tradeDtStart.value,
                tradeDtEnd: tradeDtEnd.value,
                ccid: '12ef221ca55f42e396528c6beecce30a'
            }
            const res = await getSHIBORRate(params);
            
            // 使用cnname匹配数据
            const activePeriodItem = periodOptions.value.find(item => item.itemcode === activePeriod.value);
            const activePeriodName = activePeriodItem ? activePeriodItem.cnname : '';
            console.log('SHIBOR - activePeriod.value:', activePeriod.value);
            console.log('SHIBOR - activePeriodItem:', activePeriodItem);
            console.log('SHIBOR - activePeriodName:', activePeriodName);
            console.log('SHIBOR - res.data.data:', res.data.data);
            console.log('SHIBOR - selectedPeriodText:', selectedPeriodText);
            
            // 获取期限对应的数据
            let periodData = null;
            if (res.data && res.data.data && activePeriodName) {
                periodData = res.data.data[activePeriodName];
            }
            
            console.log('SHIBOR - periodData:', periodData);
            
            // 无论数据是否为null都要更新图表
            updateChartData(periodData || []);
        } else {
            const params = {
                bAnalCurveterm: [selectedPeriodText],
                date: [tradeDtStart.value, tradeDtEnd.value],
                radioDate: 12,
                mainRating: activeTab.value === 'cd' ? [selectedRating.value] : [],
                bAnalCurveName: selectedBondType.value,
                tradeDtStart: tradeDtStart.value,
                tradeDtEnd: tradeDtEnd.value
            }
            const res = await getMarketRate(params);
            // 获取当前选中期限的数据数组

            if (res.data && res.data.data) {
                // 同业存单使用cnname作为键
                const selectedPeriodItem = cdPeriodOptions.value.find(item => item.itemcode === selectedPeriod.value);
                const selectedCnname = selectedPeriodItem ? selectedPeriodItem.cnname : '';
                const periodData = res.data.data[selectedCnname];
                // 确保在没有数据时也更新图表
                updateChartData(periodData || []);
            } else {
                // 没有数据时，使用空数组更新图表
                updateChartData([]);
            }
        }
    } catch (error) {
        // 获取图表数据失败
        // 错误时使用空数组更新图表
        updateChartData([]);
    }
}

// 更新图表数据（采用市场利率页面的格式）
const updateChartData = (periodData) => {
    if (!periodData || periodData.length === 0) {
        // 没有数据，清空图表
        chartStaticData.value = [];
        // 清空图表上方数据
        latestDataDate.value = '--';
        latestDataValue.value = '--';
        latestDataChange.value = 0;
        return;
    }

    // 转换接口数据为图表数据格式（类似市场利率页面）
    chartStaticData.value = periodData.map(item => ({
        date: formatTradeDate(item.tradeDt, activeTab.value),
        value: parseFloat(item.bAnalYield) || 0,
        bpChange: formatYieldChange(item.bAnalYieldAdd)
    }));

    // 更新图表上方最新数据点信息
    if (chartStaticData.value.length > 0) {
        const latestData = chartStaticData.value[chartStaticData.value.length - 1];
        latestDataDate.value = latestData.date;
        latestDataValue.value = `${latestData.value.toFixed(4)}%`;
        latestDataChange.value = latestData.bpChange;
    } else {
        latestDataDate.value = '--';
        latestDataValue.value = '--';
        latestDataChange.value = 0;
    }
}

// 处理期限变更事件 - 用于LPR和SHIBOR标签
const handlePeriodChange = (data) => {
    // 直接使用 itemcode 作为期限值
    activePeriod.value = data.itemcode;

    // 根据当前标签页保存期限值到对应变量
    if (activeTab.value === 'lpr') {
        lprPeriod.value = data.itemcode;
    } else if (activeTab.value === 'shibor') {
        shiborPeriod.value = data.itemcode;
    }

    // 同时加载期限列表和图表数据
    loadData({
        loadPeriodList: true, 
        loadChartData: true
    });
};

// 显示评级选择弹出框 - 用于同业存单标签
const showRatingPickerDialog = () => {
    showRatingPicker.value = true;
};

// 显示期限选择弹出框 - 用于同业存单标签
const showPeriodPickerDialog = () => {
    showPeriodPicker.value = true;
};

// 处理评级确认 - 用于同业存单标签
const handleRatingConfirm = (rating) => {
    selectedRating.value = rating;

    // 评级变更需要重新获取期限列表和图表数据
    loadData({
        loadPeriodList: true,
        loadChartData: true
    });
};

// 处理评级取消 - 用于同业存单标签
const handleRatingCancel = () => {
    // 取消操作，不做任何处理
};

// 处理期限确认 - 用于同业存单标签
const handlePeriodConfirm = (value, item) => {
    // 直接存储itemcode值
    selectedPeriod.value = value;

    // 同时加载期限列表和图表数据
    loadData({ 
        loadPeriodList: true,
        loadChartData: true 
    });
};

// 处理期限取消 - 用于同业存单标签
const handlePeriodCancel = () => {
    // 取消操作，不做任何处理
};

// 处理图表触摸事件（采用市场利率页面的方式）
const handleChartPointTouch = (pointData) => {
    // 更新图表上方的数据点信息
    latestDataDate.value = pointData.date;
    latestDataValue.value = `${pointData.value}%`;
    latestDataChange.value = pointData.bpChange === 'N/A' ? 0 : pointData.bpChange;
};

// 切换弹窗显示状态
const toggleModal = () => {
    showModal.value = !showModal.value;
};

// 处理日期区间变更
const handleDateRangeChange = (dateRange) => {
    tradeDtStart.value = dateRange[0];
    tradeDtEnd.value = dateRange[1];

    // 时间范围变化只需要更新图表数据
    loadData({ loadChartData: true });
};

// 处理弹窗可见性变化
const handleModalVisibleChange = (visible) => {
    showModal.value = visible;
};

// 处理时间筛选变更
const handleTimeFilterChange = (data) => {
    activeTimeFilter.value = data.filter;
    tradeDtStart.value = data.dateRange[0];
    tradeDtEnd.value = data.dateRange[1];

    // 时间范围变化只需要更新图表数据
    loadData({ loadChartData: true });
};

// 监听activeTab变化
watch(() => activeTab.value, (newTab) => {
    // 标签切换时会调用switchTab方法，已经处理了数据生成，这里不需要重复
});

// 在组件挂载时调用
onMounted(() => {
    // 使用统一的数据加载方法
    loadData({
        loadPeriodOptions: true,
        loadPeriodList: true,
        loadChartData: true  // 确保初始化时也加载图表数据
    });
});

// 渲染显示的期限名称（cnname）
const displayPeriod = computed(() => {
    if (!cdPeriodOptions.value.length) return '1M';
    const found = cdPeriodOptions.value.find(item => item.itemcode === selectedPeriod.value);
    return found ? found.cnname : '1M';
});
</script>

<style lang="scss" scoped>
/* ====================== 1. 页面布局相关样式 ====================== */
/* 1.1 页面容器 */
.container {
    padding: 0 20rpx;
    height: 100vh;
    box-sizing: border-box;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    /* 防止整体页面滚动 */
}

/* 1.2 头部区域 */
.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

/* 1.3 内容区域 */
.content-wrapper {
    flex: 1;
    /* 给固定头部留出空间 */
    overflow: hidden;
}

/* 1.4 滚动区域 */
.scrollable-content {
    height: 100%;
    padding: 10rpx 0 0;

    ::-webkit-scrollbar {
        display: none;
        width: 0;
        height: 0;
        color: transparent;
    }
}

/* 1.5 底部留白 */
.bottom-space {
    height: 100rpx;
    width: 100%;
}

/* ====================== 2. Tab切换样式 ====================== */
.tab-container {
    display: flex;
    margin-bottom: 20rpx;
    border-radius: 10rpx;
    margin-top: 180rpx;
    /* 给固定头部留出空间 */

    .tab-item {
        flex: 1;
        padding: 30rpx 0;
        text-align: center;
        position: relative;
        font-size: 32rpx;
        color: #999;
        transition: all 0.3s ease;
        font-weight: bold;

        &.active {
            color: #FF9900;
            font-weight: bold;

            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 40%;
                height: 6rpx;
                background-color: #FF9900;
                border-radius: 6rpx 6rpx 0 0;
            }
        }
    }
}

/* ====================== 3. 图表卡片样式 ====================== */
/* 3.1 卡片容器 */
.chart-card {
    background-color: white;
    border-radius: 20rpx;
    overflow: hidden;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 3.2 卡片内容 */
.card-content {
    padding: 30rpx;
}

/* 3.3 图表组件 */
.chart-component {
    background-color: white;
    border-radius: 16rpx;
    overflow: hidden;
    margin-top: 20rpx;
}

/* ====================== 4. 辅助样式 ====================== */
/* 4.1 加载状态 */
.loading-container {
    height: 400rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.8);
    margin: 20rpx 0;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);

    text {
        font-size: 28rpx;
        color: #999999;
    }
}

/* ==================== 5. 图表数据点信息样式 ==================== */
.chart-data-point {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    background-color: #F5F7FF;
    padding: 15rpx 20rpx;
    border: 4rpx solid #fafafa;
    box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(172, 172, 172, 0.2);
    border-radius: 10rpx;
    font-size: 22rpx;
}

.point-date {
    display: flex;
    align-items: center;
}

.point-indicator {
    width: 14rpx;
    height: 14rpx;
    border-radius: 50%;
    background-color: #6F7CD1;
    margin-right: 10rpx;
}

.point-value {
    color: #333;
}

.point-change {
    &.up {
        color: #FF4D4F;
    }

    &.down {
        color: #52C41A;
    }

    &.flat {
        color: #FEB249;
    }
}

/* ==================== 6. 利率信息卡片样式 (已移至 RateInfoDisplay.vue) ==================== */
/* .rate-info-card { ... } */
/* .rate-info { ... } */

/* ====================== 7. 评级期限选择器样式 ====================== */
.rating-period-selector {
    display: flex;
    justify-content: center;
    margin-bottom: 20rpx;
    align-items: center;
}

.selector-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;

    text {
        color: #666;
        font-size: 28rpx;
        margin-right: 10rpx;
    }
}

.divider {
    width: 2rpx;
    height: 36rpx;
    background-color: #DDDDDD;
    margin: 0 30rpx;
}

.dropdown-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    text:nth-child(2) {
        font-size: 20rpx !important;
    }
}

.selected-value {
    color: #333;
    font-weight: bold;
}

.dropdown-arrow {
    font-size: 20rpx;
    color: #999;
    margin-left: 10rpx;
}


</style>