{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "moduleResolution": "node", "verbatimModuleSyntax": true, "ignoreDeprecations": "5.0", "allowJs": true, "noEmit": true, "sourceMap": false, "noImplicitAny": false, "allowSyntheticDefaultImports": true, "baseUrl": ".", "outDir": "./dist", "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom"], "types": ["@dcloudio/types", "miniprogram-api-typings", "@uni-helper/uni-app-types", "@uni-helper/uni-ui-types"], "skipLibCheck": true, "esModuleInterop": true}, "vueCompilerOptions": {"plugins": ["@uni-helper/uni-app-types/volar-plugin"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "shims-uni.d.ts", "global.d.ts", "auto-imports.d.ts"], "exclude": ["node_modules", "dist", "src/uni_modules/**/*.js"]}