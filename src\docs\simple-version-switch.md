# 简单版本切换功能

## 功能说明

在"我的"页面可以切换企业版和集团版，切换后会重新获取权限数据并通知其他页面更新。

## 实现方式

### 1. Permission Store 添加版本切换方法

```typescript
// 简单的版本切换方法
const switchVersion = async (targetVersionId: string) => {
    try {
        setSysVersion(targetVersionId)
        const response = await getPermissionList({})
        
        if (response.data.code === '000') {
            setPermissions(response.data.data)
            return { success: true }
        } else {
            throw new Error('获取权限数据失败')
        }
    } catch (error) {
        console.error('版本切换失败:', error)
        return { success: false, error }
    }
}
```

### 2. 我的页面版本切换

```vue
<script setup>
// 版本切换处理
const handleVersionSwitch = async (e) => {
    const switchToGroup = e.detail.value;
    uni.showLoading({ title: '切换中...' });
    
    try {
        // 找到目标版本
        let targetVersion = null;
        if (switchToGroup) {
            targetVersion = versionList.value.find(v => 
                v.id === 'group_mp' || v.name?.includes('集团')
            ) || versionList.value[1];
        } else {
            targetVersion = versionList.value.find(v => 
                v.id === 'company_mp' || v.name?.includes('企业')
            ) || versionList.value[0];
        }
        
        // 切换版本
        const result = await permissionStore.switchVersion(targetVersion.id);
        
        if (result.success) {
            // 更新UI并通知其他页面
            currentVersionLabel.value = targetVersion.name;
            uni.$emit('versionChanged', {
                version: targetVersion.id,
                versionName: targetVersion.name
            });
            uni.showToast({ title: '切换成功', icon: 'success' });
        }
    } catch (error) {
        // 错误处理
        isGroupVersion.value = !switchToGroup;
        uni.showToast({ title: '切换失败，请重试', icon: 'error' });
    } finally {
        uni.hideLoading();
    }
}
</script>
```

### 3. 其他页面监听版本变化

```vue
<script setup>
onMounted(() => {
    uni.$on('versionChanged', (data) => {
        console.log('版本已切换:', data.version);
        // 重新获取页面数据
        fetchPageData();
    });
});

onUnmounted(() => {
    uni.$off('versionChanged');
});
</script>
```

## 使用说明

1. 用户在"我的"页面看到版本切换开关
2. 开关显示当前版本名称
3. 只有多个版本权限时才能切换
4. 切换时显示加载状态
5. 切换成功后通知其他页面更新数据

就这么简单！ 