import SlideCaptcha from './SlideCaptcha/SlideCaptcha.vue';
import PeriodListCard from './MarketRate/PeriodListCard.vue';
import CustomHead from './head/head.vue'

// 组件列表
const components = [
	CustomHead,
	SlideCaptcha,
	PeriodListCard
]

// 定义安装方法
const install = (app) => {
	// 注册组件
	components.forEach(component => {
		app.component(component.name || getComponentName(component), component)
	})
}

// 从文件名获取组件名称
const getComponentName = (component) => {
	const fileNameMatch = component.__file?.match(/([^/]+)\.vue$/)
	return fileNameMatch ? fileNameMatch[1] : 'CustomHead'
}

// 判断是否直接引入文件
if (typeof window !== 'undefined' && window.Vue) {
	install(window.Vue)
}

export default {
	install
}

// 导出组件
export {
	SlideCaptcha,
	PeriodListCard,
	CustomHead
} 