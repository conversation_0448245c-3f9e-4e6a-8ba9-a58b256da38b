<template>
    <cover-view class="custom-tab-bar">
        <cover-view class="tab-container">
            <cover-view v-for="(item, index) in filteredTabList" :key="index" class="tab-item" @click="handleTabClick(item.index, item.pagePath)">
                <cover-image :src="selectIndex === item.index ? item.selectedIconPath : item.iconPath" class="tab-icon"
                    mode="aspectFit" />
                <cover-view class="tab-text" :class="{ active: selectIndex === item.index }">
                    {{ item.text }}
                </cover-view>
            </cover-view>
        </cover-view>
        <cover-view class="safe-area-inset-bottom"></cover-view>
    </cover-view>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'

// 定义 props
const props = defineProps({
    selectNumber: {
        type: Number,
        default: 0
    },
    permissionData: {
        type: Array,
        default: () => []
    }
})

// 定义 emits
const emit = defineEmits(['tabChange'])

// 响应式数据
const selectIndex = ref(0)
const safeAreaBottom = ref(0)

// tab 列表配置
const tabList = [
    {
        index: 0,
        pagePath: "pages/home/<USER>",
        iconPath: "/static/tarbar/home.png",
        selectedIconPath: "/static/tarbar/home1.png",
        text: "首页",
        alwaysShow: true
    },
    {
        index: 1,
        pagePath: "pages/issuance/index",
        iconPath: "/static/tarbar/issuance.png",
        selectedIconPath: "/static/tarbar/issuance1.png",
        text: "发行查询",
        alwaysShow: false
    },
    {
        index: 2,
        pagePath: "pages/selfchose/index",
        iconPath: "/static/tarbar/selfchose.png",
        selectedIconPath: "/static/tarbar/selfchose1.png",
        text: "自选债券",
        alwaysShow: false
    },
    {
        index: 3,
        pagePath: "pages/payment/index",
        iconPath: "/static/tarbar/payment.png",
        selectedIconPath: "/static/tarbar/payment1.png",
        text: "付息兑付",
        alwaysShow: false
    },
    {
        index: 4,
        pagePath: "pages/mine/index",
        iconPath: "/static/tarbar/mine.png",
        selectedIconPath: "/static/tarbar/mine1.png",
        text: "我的",
        alwaysShow: true
    }
]

// 计算属性：过滤后的 tab 列表
const filteredTabList = computed(() => {
    // 如果没有权限数据，返回必显示的tab
    if (!props.permissionData || props.permissionData.length === 0) {
        return tabList.filter(tab => tab.alwaysShow)
    }

    // 权限数据已经是过滤后的TabBar权限（combinationName为'小程序'的权限）
    const tabPermissions = props.permissionData

    return tabList.filter(tab => {
        // 首页和我的始终显示
        if (tab.alwaysShow) {
            return true
        }
        
        // 查找对应的权限项
        const permission = tabPermissions.find(item => 
            item.modulename === tab.text && 
            item.moduletype !== 'directory'
        )
        
        return permission && permission.showflag === 'Y'
    })
})

// 监听 selectNumber 的变化
watch(() => props.selectNumber, (val) => {
    selectIndex.value = val
}, { immediate: true })

// 处理 tab 点击事件
const handleTabClick = (index, path) => {
    if (selectIndex.value === index) return
    
    emit('tabChange', index, path)
    
    // 切换页面 (可以根据需要保留或删除)
    uni.switchTab({
        url: '/' + path
    })
}

// 获取安全区域
const getSafeAreaInset = () => {
    // 获取系统信息
    uni.getSystemInfo({
        success: (res) => {
            // 获取底部安全区域高度
            safeAreaBottom.value = res.safeAreaInsets?.bottom || 0
        }
    })
}

// 组件挂载时执行
onMounted(() => {
    getSafeAreaInset()
})
</script>

<style lang="scss" scoped>
.custom-tab-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #ffffff;
    box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);
    z-index: 999999999;
    display: flex;
    flex-direction: column;

    .tab-container {
        display: flex;
        height: 100rpx;
        width: 100%;
    }

    .tab-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 10rpx 0;
    }

    .tab-icon {
        width: 50rpx;
        height: 50rpx;
        margin-bottom: 4rpx;
    }

    .tab-text {
        font-size: 24rpx;
        color: #000000;

        &.active {
            color: #FF8E2B;
        }
    }

    .safe-area-inset-bottom {
        width: 100%;
        height: env(safe-area-inset-bottom);
    }
}

// 在这里再添加一层样式,处理tabItem的布局
.custom-tab-bar {
    &:before {
        content: "";
        display: flex;
        width: 100%;
        height: 10rpx;
    }

    &>cover-view:not(.safe-area-inset-bottom) {
        display: flex;
        width: 100%;
        height: 100rpx;
    }
}
</style>