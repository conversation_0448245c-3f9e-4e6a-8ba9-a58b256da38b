<template>
	<view class="rate-info-card">
		<view class="rate-info">
			<view class="info-item">
				<view class="rate-value number-font">{{ formattedRateValue }}</view>
				<view class="rate-label">{{ rateLabel }}</view>
			</view>
			<view class="info-item">
				<view class="rate-change" :class="changeDirectionClass" v-if="changeValue !== '--' && typeof changeValue === 'number'">
					<view v-if="changeValue > 0" class="rate-change-icon-container">
						<text class="number-font">{{ formattedChangeValue }}</text>
						<image class="rate-add-icon" :src="getAssetUrl('/add-red.png')" mode=""></image>
					</view>
					<view v-else-if="changeValue < 0" class="rate-change-icon-container number-font">
						<text class="number-font">{{ formattedChangeValue }}</text>
						<image class="rate-down-icon" :src="getAssetUrl('/down-green.png')" mode=""></image>
					</view>
					<view v-else-if="changeValue === 0" class="rate-change-icon-container number-font">
						<text class="number-font">0</text>
					</view>
				</view>
				<view class="rate-change" v-else>
					<text class="number-font">--</text>
				</view>
				<view class="rate-label">{{ changeLabel }}</view>
			</view>
			<view class="info-item">
				<view class="update-time number-font">{{ timeValue }}</view>
				<view class="rate-label">{{ timeLabel }}</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { computed } from 'vue';
import { getAssetUrl } from '@/config/assets';

const props = defineProps({
	rateLabel: {
		type: String,
		default: '最新利率'
	},
	rateValue: {
		type: [String, Number],
		default: '--'
	},
	changeLabel: {
		type: String,
		default: '涨跌BP'
	},
	changeValue: {
		type: [String, Number], // Can be a number or '--'
		default: '--'
	},
	timeLabel: {
		type: String,
		default: '更新时间'
	},
	timeValue: {
		type: String,
		default: '--'
	}
});

const formattedRateValue = computed(() => {
	const num = parseFloat(props.rateValue);
	if (!isNaN(num)) {
		return num.toFixed(4) + '%';
	}
	return props.rateValue; // Handles '--' or other non-numeric strings
});

const formattedChangeValue = computed(() => {
	const num = parseFloat(props.changeValue);
	if (!isNaN(num)) {
		if (num === 0) return '0'; // Display "0" instead of "0.00"
		return num.toFixed(2);
	}
	return props.changeValue; // Handles '--'
});

const changeDirectionClass = computed(() => {
	if (typeof props.changeValue === 'number') {
		if (props.changeValue > 0) return 'up';
		if (props.changeValue < 0) return 'down';
		if (props.changeValue === 0) return 'zero';
	}
	return ''; // Default or for '--'
});

</script>

<style lang="scss" scoped>

/* 利率信息卡片样式 */
.rate-info-card {
	background-color: #f5f5f5;
	border-radius: 16rpx;
	border: 2rpx solid #fff;
	margin-bottom: 40rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.03);
}

.rate-info {
	display: flex;
	justify-content: space-between;

	.info-item {
		display: flex;
		flex-direction: column;
		align-items: flex-start;

		.rate-value,
		.rate-change {
			font-size: 36rpx;
			height: 50rpx;
			line-height: 50rpx;
			color: #333;
			margin-bottom: 6rpx;
		}

		.update-time {
			font-size: 30rpx;
			color: #333;
			height: 50rpx;
			line-height: 50rpx;
			margin-bottom: 6rpx;
		}

		.rate-change {
			&.up {
				color: #E76056;
			}

			&.down {
				color: #62BB37;
			}

			&.zero {
				color: #FEB249;
			}

			.rate-change-icon-container {
				display: flex;
				align-items: center;

				.rate-down-icon {
					width: 35rpx;
					height: 35rpx;
					transform: rotate(34deg);
				}

				.rate-add-icon {
					width: 35rpx;
					height: 35rpx;
					transform: rotate(-20deg);
				}
			}
		}

		.rate-label {
			font-size: 24rpx;
			color: #999;
			height: 34rpx;
			line-height: 34rpx;
		}
	}
}
</style> 