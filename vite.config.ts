import uni from '@dcloudio/vite-plugin-uni'
import path from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import { ConfigEnv, defineConfig, loadEnv, UserConfig } from 'vite'


export default defineConfig(({ mode }: ConfigEnv): UserConfig => {
    const env = loadEnv(mode, process.cwd())
    return {
        resolve: {
            alias: {
                '@': path.resolve(__dirname, './src')
            }
        },
        plugins: [
            AutoImport({
                imports: ['vue', 'uni-app', 'pinia']
            }),
            uni()
            
        ],
        css: {
            preprocessorOptions: {
                scss: {
                    additionalData: `
                        $assets-base-url: '${env.VITE_ASSETS_BASE_URL}';
                        @import "@/styles/global.scss";
                    `
                }
            }
        },
        server: {
            host: '0.0.0.0',
            port: 8080,
            open: true,
            hmr: true,
            proxy: {
                '/api': {
                    target: env.VITE_BASE_URL,
                    secure: false,
                    changeOrigin: true,
                    rewrite: path => path.replace(/^\/api/, '')
                }
            }
        },
        build: {
            terserOptions: {
                compress: {
                    keep_infinity: true,
                    drop_console: true,
                    drop_debugger: true
                }
            }
        }
    }
})
