<template>
    <view class="bond-type">
        <view class="period-label">期限</view>
        <scroll-view class="period-scroll" scroll-x :show-scrollbar="false" enhanced>
            <view class="period-select">
                <view class="period-btn" v-for="(item, index) in periodOptions" :key="index"
                    :class="{ active: isItemActive(item) }" @click="selectPeriod(item)">
                    <text class="number-font">{{ getDisplayText(item) }}</text>
                </view>
            </view>
        </scroll-view>
    </view>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';

// 定义props
const props = defineProps({
    periodOptions: {
        type: Array,
        required: true,
        default: () => []
    }
});

// 定义emit事件
const emit = defineEmits(['periodChange']);

// 当前选中的期限
const activePeriod = ref(null);

// 判断是否为对象格式
const isObjectFormat = (item) => {
    return typeof item === 'object' && item !== null && 'cnname' in item && 'itemcode' in item;
};

// 获取显示文本
const getDisplayText = (item) => {
    return isObjectFormat(item) ? item.cnname : item;
};

// 判断是否为激活状态
const isItemActive = (item) => {
    if (!activePeriod.value) return false;
    if (isObjectFormat(item)) {
        return isObjectFormat(activePeriod.value) && 
               activePeriod.value.itemcode === item.itemcode;
    }
    return activePeriod.value === item;
};

// 选择期限
const selectPeriod = (item) => {
    if (isItemActive(item)) {
        return;
    }
    activePeriod.value = item;

    // 触发change事件，传递不同格式的数据
    if (isObjectFormat(item)) {
        emit('periodChange', {
            period: item.cnname,
            itemcode: item.itemcode,
            originalItem: item
        });
    } else {
        emit('periodChange', {
            period: item,
            itemcode: item,
            originalItem: item
        });
    }
};

// 处理期限选项变化
const handlePeriodOptionsChange = () => {
    if (props.periodOptions && props.periodOptions.length > 0) {
        // 选中第一个选项
        const firstPeriod = props.periodOptions[0];
        activePeriod.value = firstPeriod;
        
        // 触发change事件
        if (isObjectFormat(firstPeriod)) {
            emit('periodChange', {
                period: firstPeriod.cnname,
                itemcode: firstPeriod.itemcode,
                originalItem: firstPeriod
            });
        } else {
            emit('periodChange', {
                period: firstPeriod,
                itemcode: firstPeriod,
                originalItem: firstPeriod
            });
        }
    }
};

// 监听periodOptions变化
watch(() => props.periodOptions, (newOptions) => {
    handlePeriodOptionsChange();
}, { deep: true });

// 组件挂载时初始化
onMounted(() => {
    handlePeriodOptionsChange();
});
</script>

<style lang="scss" scoped>
/* 期限选择样式 */
.bond-type {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 20rpx;

    .period-label {
        font-size: 32rpx;
        font-weight: bold;
        color: #000;
    }

    .period-scroll {
        flex: 1;
        width: 70%;
        white-space: nowrap;
        margin-left: 20rpx;

        &::-webkit-scrollbar {
            display: none;
            width: 0;
            height: 0;
            background: transparent;
        }

        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .period-select {
        display: inline-flex;
        padding: 10rpx 0;

        .period-btn {
            display: inline-block;
            padding: 10rpx 20rpx;
            margin-left: 15rpx;
            border-radius: 60rpx;
            background-color: #f0f0f0;
            font-size: 24rpx;
            transition: all 0.2s ease;
            color: #333;
            white-space: nowrap;

            &.active {
                background-color: #FF9900;
                color: white;
            }
        }
    }
}
</style>