import { cloneDeep } from 'lodash' // 从 lodash 库导入 cloneDeep 函数，用于实现对象的深拷贝。
import qs from 'qs' // 从 qs 库导入 qs 对象，用于处理 URL 查询字符串的序列化和解析。
import { usePermissionStore } from '@/stores/permission' // 导入权限 store

const baseUrl = import.meta.env.VITE_BASE_API // 定义基础 URL，从 Vite 环境变量 VITE_BASE_API 中获取 API 的基础路径。
let requestList = new Map() // 创建一个 Map 对象，用于缓存进行中的请求，键是请求的唯一标识，值是请求的响应数据。

/**
 * 扩展UniApp请求选项接口
 * 定义自定义的请求选项接口 RequestOptions，继承自 UniApp.RequestOptions。
 */
export interface RequestOptions extends UniApp.RequestOptions {
    data?: any      // 可选参数 data，表示请求体数据，可以是任何类型。
    pem?: boolean   // 可选参数 pem (Page Error Management)，布尔类型，指示是否由页面自行处理请求错误。
    sld?: boolean   // 可选参数 sld (Show Loading)，布尔类型，指示是否在请求期间显示加载提示。
}

/**
 * 定义接口返回数据结构
 * 定义接口返回数据的结构 ResultData，继承自 UniApp.RequestSuccessCallbackResult。
 */
export interface ResultData extends UniApp.RequestSuccessCallbackResult {
    code: number    // 必须参数 code，表示接口返回的状态码，通常 200 表示成功。
    data: any       // 必须参数 data，表示接口返回的具体数据，可以是任何类型。
    msg: string     // 必须参数 msg，表示接口返回的消息或提示信息。
}

/**
 * http请求函数
 * @param {RequestOptions} options - 请求配置选项，包含 URL、方法、头部、数据等。
 * @returns {Promise<ResultData>} 返回一个 Promise 对象，该 Promise 在请求成功时解析为 ResultData 类型的数据。
 * 封装的核心 HTTP 请求函数。
 */
const request = (options: RequestOptions): Promise<ResultData> => {
    // 使用对象解构从 options 中提取参数，并设置默认值。
    let {
        url,                                                  // 请求的 URL。
        method = 'POST',                                        // 请求方法，默认为 'POST'。
        header = { 'content-type': 'application/json' },        // 请求头，默认为 JSON 类型。
        data = {},                                              // 请求数据，默认为空对象。
        pem = false,                                            // 是否页面处理错误，默认为 false (统一处理)。
        sld = false                                             // 是否显示 Loading，默认为 false。
    } = options
    header = getHeader(header, data)                            // 调用 getHeader 函数处理并获取最终的请求头。
    url = baseUrl + url                                         // 将基础 URL 和相对 URL 拼接成完整的请求地址。
    // 使用 URL、请求方法和序列化后的数据生成一个唯一的 key，用于请求缓存。
    const key = [url, method, qs.stringify(data)].join('&')

    // 检查当前请求是否已存在于 requestList 缓存中。
    if (requestList.has(key)) {
        // 如果存在，深拷贝缓存中的响应数据。
        const resData = cloneDeep(requestList.get(key))
        // 返回一个新的 Promise，并立即用缓存的数据解析它。
        return new Promise(resolve => {
            resolve(resData)
        })
    }

    // 如果请求不在缓存中，则创建一个新的 Promise 来处理实际的 HTTP 请求。
    return new Promise((resolve, reject) => {
        // 如果 sld 参数为 true，则显示全局的加载提示框。
        if (sld) {
            uni.showLoading({
                title: '加载中' // 加载提示框的标题。
            })
        }

        // 调用 uni-app 的 uni.request 方法发起网络请求。
        uni.request({
            url,            // 请求的完整 URL。
            header,         // 处理后的请求头。
            method,         // 请求方法 (GET, POST, etc.)。
            data,           // 请求体数据。
            timeout: 10000, // 设置请求超时时间为 10000 毫秒 (10 秒)。
            // 请求成功时的回调函数。
            success(res: any) { // res 是 uni-app 返回的响应对象。
                // 检查响应数据中的 code 是否为 200 (表示业务成功)。
                if (res.data.code == 200 || res.data.code == '000') {
                    // 深拷贝响应数据，防止后续操作修改原始数据。
                    const resData = cloneDeep(res.data)
                    // 将成功的响应数据存入 requestList 缓存。
                    requestList.set(key, resData)
                    // 隐藏加载提示框。
                    if (sld) {
                        uni.hideLoading()
                    }
                    // 使用响应数据解析 Promise。
                    // resolve(res.data)
                    resolve(res)
                // 检查响应数据中的 code 是否为 3401 (通常表示未授权或登录过期)。
                } else if (res.data.code == '003') {
                    if (sld) {
                        uni.hideLoading()
                    }
                    // 如果是 3401，则强制重新启动并跳转到授权页面。
                    uni.$router.reLaunch('/pages/sign_in/sign_in')
                } else {
                    if (sld) {
                        uni.hideLoading()
                    }
                    // 对于其他非 200 且非 3401 的错误码，调用 errorHandler 进行处理。
                    // 注意：这里传递 resolve 是为了在 errorHandler 中根据 pem 决定是否继续 Promise 链。
                    errorHandler(res, resolve, pem)
                }
            },
            // 请求失败时的回调函数 (例如网络错误、超时等)。
            fail: (err: any) => {
                if (sld) {
                    uni.hideLoading()
                }
                // 调用 errorHandler 处理请求失败的情况。
                errorHandler(err, reject, pem)
            },
            // 不论请求成功或失败，都会执行的回调函数。
            complete() {
                // 设置一个 1 秒的延迟。
                setTimeout(() => {
                    // 1 秒后从 requestList 缓存中删除当前请求的记录。
                    requestList.delete(key)
                }, 1000)
            }
        })
    })
}

/**
 * 接口错误数据处理函数
 * @param {ResultData | any} res - 接口返回的原始数据或错误对象。
 * @param {Function} callback - 根据情况可能是 resolve 或 reject 函数。
 * @param {boolean} pem - 指示是否由页面处理错误。
 * 用于统一处理请求成功但业务失败（code !== 200）或请求本身失败的情况。
 */
const errorHandler = (res: ResultData | any, callback: (arg: any) => void, pem: boolean) => {
    // 如果 pem 为 false，表示需要进行统一错误处理。
    if (!pem) {
        // 确定错误提示信息：优先使用 res.data.message，如果不存在则使用 res.msg。
        // res.hasOwnProperty('data') 用于区分是业务错误(有data)还是网络错误(无data)
        const title = res.hasOwnProperty('data') && res.data.message ? res.data.message : (res.msg || '请求失败')
        // 显示一个 uni-app 的消息提示框。
        // uni.showToast({
        //     title,          // 提示的标题。
        //     icon: 'none',   // 不显示图标。
        //     duration: 1000  // 提示持续显示 3000 毫秒 (3 秒)。
        // })
        // 即使统一处理了错误，也调用 callback (可能是 resolve 或 reject)，
        // 但传递的是原始的 res，让调用方知道发生了错误，但 Promise 本身可能不会被拒绝。
        // 如果希望 Promise 被拒绝，这里应该调用 reject(res)。
        callback(res)
    } else {
        // 如果 pem 为 true，表示错误由页面自行处理，直接调用 callback (可能是 resolve 或 reject)。
        callback(res)
    }
}

/**
 * 配置请求头部信息
 * @param {object} header - 原始传入的请求头对象。
 * @param {object} data - 请求的参数数据。
 * @returns {object} 返回合并了通用信息（如 token、时间戳）后的最终请求头对象。
 * 用于在每个请求发送前，动态添加或修改请求头信息。
 */
const getHeader = (header: object, data: object) => {
    // 可以在这里添加如加密等请求预处理逻辑。
    // console.log(data) // 打印请求参数，用于调试或加密处理。
    const reqtime = Date.now()                        // 获取当前时间戳。
    
    // 优先从 Pinia store 获取 token 和 sysVersion，如果获取不到则从本地存储获取
    let Authorization = ''
    let sysVersion = ''
    
    try {
        const permissionStore = usePermissionStore()
        Authorization = permissionStore.getToken || uni.getStorageSync('token') || ''
        sysVersion = permissionStore.getSysVersion || uni.getStorageSync('sysVersion') || ''
    } catch (error) {
        // 如果 store 不可用，则从本地存储获取
        Authorization = uni.getStorageSync('token') || ''
        sysVersion = uni.getStorageSync('sysVersion') || ''
    }
    
    // 创建一个包含通用请求头信息的对象。
    const target = {
        // 'x-access-token': Authorization,              // 设置 'x-access-token' 字段为获取到的 token。
        'Authorization': Authorization,           // 设置 'Authorization' 字段为获取到的 token。
        reqtime,
        'sys-version': sysVersion
    }
    // 使用 Object.assign 将原始 header 和通用 header (target) 合并。
    // 如果原始 header 中有相同字段，会被 target 中的覆盖。
    return Object.assign({}, target, header)
}

// 导出核心的 request 函数，使其可以在其他模块中被导入和使用。
export default request
