# 首页"我的应用"权限控制使用说明

## 概述

本项目实现了基于权限动态控制首页"我的应用"模块显示的功能。通过后端返回的权限列表，可以动态控制哪些应用显示，哪些应用隐藏。

## 实现原理

### 1. 权限数据结构

首页权限数据与TabBar权限数据分开存储：

- **TabBar权限**：`combinationName` 为 `'小程序'` 的权限项
- **首页权限**：`combinationName` 为 `'首页'` 的权限项

权限数据结构：
```typescript
interface Permission {
    modulename: string;        // 模块名称，如"市场利率"、"政策利率"等
    combinationName: string;   // 组合名称，"首页"
    moduletype: string;        // 模块类型
    showflag: string;         // 显示标志，"Y"表示显示，"N"表示隐藏
}
```

### 2. 应用配置

在首页组件中，通过 `myApps` 配置各个应用项：

```javascript
const myApps = reactive([
    { name: '市场利率', icon: '...', path: '...' },
    { name: '政策利率', icon: '...', path: '...' },
    { name: '债券收益率', icon: '...', path: '...' },
    // ... 其他应用配置
]);
```

### 3. 权限过滤逻辑

使用 `computed` 属性和权限工具函数实现权限过滤：

```javascript
// 计算显示的应用列表
const visibleApps = computed(() => {
    // 使用权限工具函数来获取可见的应用列表
    return getVisibleHomeApps(myApps);
});
```

权限工具函数 `getVisibleHomeApps` 的实现：
- 如果没有权限数据，显示所有应用
- 如果有权限数据，只显示 `showflag` 为 `'Y'` 的应用

## 使用方法

### 1. 后端权限配置

需要在后端配置相应的权限项，权限项的 `modulename` 需要与应用的 `name` 字段完全匹配：

- "市场利率" - 需要权限控制
- "政策利率" - 需要权限控制
- "债券收益率" - 需要权限控制
- "对标分析" - 需要权限控制
- "信用利差" - 需要权限控制
- "发行定价" - 需要权限控制
- "注册进度" - 需要权限控制
- "注册额度" - 需要权限控制
- "二级成交" - 需要权限控制
- "债市日历" - 需要权限控制

### 2. 权限数据存储

在用户登录成功后，权限数据会自动分类存储：

```javascript
// src/pages/sign_in/sign_in.vue
const permissionData = permissionList.data.data;

// 提取首页模块权限(combinationName为'首页'的权限)
const homePagePermissions = permissionData.filter(
    item => item.combinationName === '首页'
);

// 存储首页权限
uni.setStorageSync('homePagePermissions', homePagePermissions);
```

### 3. 页面实现

首页已经实现了权限控制，无需额外配置。应用列表会根据权限自动显示或隐藏。

## 工具函数

项目提供了以下权限工具函数（`src/utils/permission.ts`）：

```typescript
// 获取存储的首页权限列表
export const getStoredHomePagePermissions = (): Permission[]

// 检查是否有特定首页模块的权限
export const hasHomePageModulePermission = (moduleName: string): boolean

// 获取可见的首页应用列表
export const getVisibleHomeApps = (appList: Array<{name: string, [key: string]: any}>)
```

## 示例场景

假设后端返回的权限数据如下：

```json
[
    {
        "modulename": "市场利率",
        "combinationName": "首页",
        "moduletype": "menu",
        "showflag": "Y"
    },
    {
        "modulename": "政策利率",
        "combinationName": "首页",
        "moduletype": "menu",
        "showflag": "Y"
    },
    {
        "modulename": "债券收益率",
        "combinationName": "首页",
        "moduletype": "menu",
        "showflag": "N"
    },
    {
        "modulename": "对标分析",
        "combinationName": "首页",
        "moduletype": "menu",
        "showflag": "Y"
    }
]
```

最终首页"我的应用"模块会显示：
- ✅ 市场利率
- ✅ 政策利率
- ❌ 债券收益率（被隐藏）
- ✅ 对标分析
- ❌ 其他未配置权限的应用（被隐藏）

## 注意事项

1. **权限名称匹配**：权限的 `modulename` 必须与应用的 `name` 完全一致
2. **默认行为**：如果没有权限数据，默认显示所有应用
3. **权限刷新**：权限变更后需要重新登录才能生效
4. **权限类型**：只有 `combinationName` 为 `'首页'` 的权限才会影响"我的应用"模块
5. **模块类型**：`moduletype` 为 `'directory'` 的权限项会被忽略 