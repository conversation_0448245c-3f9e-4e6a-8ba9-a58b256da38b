<template>
    <view class="date-range-picker">
        <view class="modal-mask" v-if="showModal" @click="closeModal"></view>
        <view class="modal-container" v-if="showModal">


            <view class="modal-header">
                <view class="modal-title">选择日期区间</view>
            </view>
            <view class="modal-content">
                <view class="date-range-section">
                    <view class="date-picker-section" :class="{'active': selectedDateType === 'start'}" @click="switchDateType('start')">
                        <view class="date-label start-date">起始日期</view>
                        <view class="selected-date start-date-value">{{startDateString}}</view>
                        <view class="date-underline" v-if="selectedDateType === 'start'"></view>
                    </view>
                    <view class="date-picker-section" :class="{'active': selectedDateType === 'end'}" @click="switchDateType('end')">
                        <view class="date-label end-date">截止日期</view>
                        <view class="selected-date end-date-value">{{endDateString}}</view>
                        <view class="date-underline" v-if="selectedDateType === 'end'"></view>
                    </view>
                </view>
                
                <view class="picker-container">
                    <picker-view class="date-picker" indicator-style="height: 50px; background-color: rgba(255, 142, 43, 0.1);" :value="selectedDateIndexes" @change="onDatePickerChange">
                        <picker-view-column>
                            <view class="picker-item" v-for="(year, index) in years" :key="'year-'+index">{{year}}年</view>
                        </picker-view-column>
                        <picker-view-column>
                            <view class="picker-item" v-for="(month, index) in months" :key="'month-'+index">{{month}}月</view>
                        </picker-view-column>
                        <picker-view-column>
                            <view class="picker-item" v-for="(day, index) in days" :key="'day-'+index">{{day}}日</view>
                        </picker-view-column>
                    </picker-view>
                </view>
            </view>
            <view class="modal-footer">
                <view class="footer-btn-container">
                    <view class="modal-button reset-button" @click="resetDatePicker">重置</view>
                    <view class="modal-button confirm-button" @click="confirmDateSelection">确定</view>
                </view>
            </view>


			
        </view>
    </view>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, watch } from 'vue';

// 组件属性定义
const props = defineProps({
    // 是否显示弹窗
    visible: {
        type: Boolean,
        default: false
    },
    // 默认起始日期
    defaultStartDate: {
        type: Date,
        default: () => {
            const date = new Date();
            date.setFullYear(date.getFullYear() - 1);
            return date;
        }
    },
    // 默认结束日期
    defaultEndDate: {
        type: Date,
        default: () => new Date()
    }
});

// 定义组件事件
const emits = defineEmits(['update:visible', 'dateRangeChange', 'close']);

// 弹窗显示状态
const showModal = computed(() => props.visible);

// 日期选择相关
const selectedDateType = ref('start'); // 'start' 或 'end'
const startDate = ref(new Date(props.defaultStartDate));
const endDate = ref(new Date(props.defaultEndDate));

// 日期选择器数据
const years = ref([2022, 2023, 2024, 2025]);
const months = ref([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]);
const days = ref(Array.from({length: 31}, (_, i) => i + 1));

// 当前选中的日期索引
const selectedDateIndexes = ref([
    years.value.indexOf(startDate.value.getFullYear()),
    startDate.value.getMonth(),
    startDate.value.getDate() - 1
]);

// 格式化日期显示
const startDateString = computed(() => {
    return `${startDate.value.getFullYear()}年${startDate.value.getMonth()+1}月${startDate.value.getDate()}日`;
});

const endDateString = computed(() => {
    if (endDate.value.getFullYear() === 1970) {
        return "-年-月-日";
    }
    return `${endDate.value.getFullYear()}年${endDate.value.getMonth()+1}月${endDate.value.getDate()}日`;
});

// 监听props.visible的变化
watch(() => props.visible, (newValue) => {
    if (newValue) {
        // 弹窗显示时，重新初始化日期
        startDate.value = new Date(props.defaultStartDate);
        endDate.value = new Date(props.defaultEndDate);
        selectedDateType.value = 'start';
        
        // 更新选择器索引
        updateSelectedIndexes();
    }
});

// 更新选择器索引
const updateSelectedIndexes = () => {
    const date = selectedDateType.value === 'start' ? startDate.value : endDate.value;
    if (!date || date.getFullYear() === 1970) return;
    
    selectedDateIndexes.value = [
        years.value.indexOf(date.getFullYear()),
        months.value.indexOf(date.getMonth() + 1),
        days.value.indexOf(date.getDate())
    ];
};

// 切换日期选择类型
const switchDateType = (type) => {
    selectedDateType.value = type;
    updateSelectedIndexes();
};

// 关闭弹窗
const closeModal = () => {
    emits('update:visible', false);
    emits('close');
};

// 日期选择器变更事件
const onDatePickerChange = (e) => {
    const values = e.detail.value;
    selectedDateIndexes.value = values;
    
    const selectedYear = years.value[values[0]];
    const selectedMonth = months.value[values[1]];
    const selectedDay = days.value[values[2]];
    
    if (selectedDateType.value === 'start') {
        startDate.value = new Date(selectedYear, selectedMonth - 1, selectedDay);
    } else {
        endDate.value = new Date(selectedYear, selectedMonth - 1, selectedDay);
    }
};

// 重置日期选择
const resetDatePicker = () => {
    startDate.value = new Date(props.defaultStartDate);
    endDate.value = new Date(props.defaultEndDate);
    selectedDateType.value = 'start';
    
    // 重置选择器索引
    updateSelectedIndexes();
    
    // 关闭弹窗
    closeModal();
};

// 确认日期选择
const confirmDateSelection = () => {
    if (endDate.value.getFullYear() === 1970) {
        // 如果截止日期未设置，使用当前日期
        endDate.value = new Date();
    }
    
    // 格式化日期 YYYY-MM-DD
    const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };
    
    // 生成日期范围数组并触发变更事件
    const dateRange = [formatDate(startDate.value), formatDate(endDate.value)];
    emits('dateRangeChange', dateRange);
    closeModal();
};
</script>

<style lang="scss" scoped>
.date-range-picker {
    position: relative;
}

.modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 999;
}

.modal-container {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    overflow: hidden;
    z-index: 1000;
    // padding-bottom: env(safe-area-inset-bottom);
    animation: slideUp 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx;
}

.modal-title {
    font-size: 34rpx;
    font-weight: bold;
    color: #333;
}

.modal-content {
    padding: 0 30rpx 30rpx;
}

.date-range-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30rpx;
    padding: 0 20rpx;
}

.date-picker-section {
    flex: 1;
    text-align: center;
    position: relative;
    padding-bottom: 15rpx;
}

.date-picker-section.active .date-label,
.date-picker-section.active .selected-date {
    color: #FF8E2B;
    font-weight: 500;
}

.date-underline {
    position: absolute;
    bottom: 0;
    left: 35%;
    width: 30%;
    height: 4rpx;
    background-color: #FF8E2B;
    border-radius: 3rpx;
}

.date-label {
    font-size: 32rpx;
    color: #333;
    margin-bottom: 15rpx;
}

.start-date {
    color: #333;
}

.selected-date {
    font-size: 34rpx;
    font-weight: normal;
}

.start-date-value {
    color: #333;
}

.date-picker-section.active .start-date-value {
    color: #FF8E2B;
}

.end-date-value {
    color: #666;
}

.date-picker-section.active .end-date-value {
    color: #FF8E2B;
}

.picker-container {
    height: 250px;
    width: 100%;
    margin-top: 20rpx;
}

.date-picker {
    width: 100%;
    height: 100%;
}

.picker-item {
    line-height: 50px;
    text-align: center;
    font-size: 32rpx;
    color: #333;
}

/* 修改选中项样式 */
.date-picker :deep(.uni-picker-view-indicator) {
    height: 50px;
}

.date-picker :deep(.uni-picker-view-indicator):before,
.date-picker :deep(.uni-picker-view-indicator):after {
    display: none;
}

.date-picker :deep(.uni-picker-view-content .picker-item) {
    transition: all 0.3s;
}

.date-picker :deep(.uni-picker-view-content .picker-item.selected),
.date-picker :deep(.uni-picker-view-indicator .picker-item) {
    color: #FF8E2B;
    font-weight: bold;
}

.footer-btn-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.modal-button {
    color: #fff;
    font-size: 32rpx;
    padding: 22rpx 0;
    border-radius: 10rpx;
    text-align: center;
    flex: 1;
}

.reset-button {
    background-color: #f5f5f5;
    color: #333;
    margin-right: 20rpx;
}

.confirm-button {
    background-color: #FFA83C;
}

.modal-footer {
    padding: 20rpx 30rpx 40rpx;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}
</style>