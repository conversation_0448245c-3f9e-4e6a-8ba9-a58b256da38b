<template>
    <view class="container">

        <!-- 搜索框 -->
        <view class="search-box">
            <view class="search-input">
                <uni-icons type="search" size="18" color="#999"></uni-icons>
                <input type="text" placeholder="李宁有限公司" v-model="searchCompany" placeholder-class="placeholder" />
            </view>
            <text class="cancel-btn" @tap="goBack">取消</text>
        </view>

        <!-- 企业卡片区域 -->
        <view class="content-wrapper">
            <scroll-view class="scrollable-content" scroll-y :show-scrollbar="false" enhanced>
                <view class="issuer-item">
                    <view class="issuer-top-row">
                        <view class="bond-name-row">
                            <text class="bond-name">李宁有限公司</text>
                            <text class="tag-text issuer-tag">城投</text>
                        </view>
                        <view class="add-btn" @tap="addCompany">
                            <uni-icons type="plus" size="16" color="#FF9900"></uni-icons>
                            <text class="add-text">添加</text>
                        </view>
                    </view>
                    <view class="issuer-details-row">
                        <view class="detail-column">
                            <text class="detail-value">AAA</text>
                            <text class="detail-label">主体评级</text>
                        </view>
                        <view class="detail-column">
                            <text class="detail-value">北京</text>
                            <text class="detail-label">地区</text>
                        </view>
                        <view class="detail-column">
                            <text class="detail-value">市级国企</text>
                            <text class="detail-label">主体性质</text>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import CustomHead from '@/components/head/head.vue';

// 搜索输入
const searchCompany = ref('');

// 添加企业
const addCompany = () => {
    uni.showToast({
        title: '已添加企业',
        icon: 'success',
        duration: 2000
    });
};

// 返回上一页
const goBack = () => {
    uni.navigateBack();
};

onMounted(() => {
    // 页面加载时的逻辑
});
</script>

<style lang="scss" scoped>
/* 页面容器 */
.container {
    padding: 0 20rpx 40rpx 20rpx;
    height: 100vh;
    box-sizing: border-box;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}


/* 搜索框 */
.search-box {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
}

.search-input {
    flex: 1;
    height: 72rpx;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 36rpx;
    display: flex;
    align-items: center;
    padding: 0 24rpx;
    backdrop-filter: blur(10rpx);
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.search-input uni-icons {
    margin-right: 10rpx;
}

.search-input input {
    flex: 1;
    height: 100%;
    font-size: 28rpx;
}

.search-input .placeholder {
    color: #999999;
}

.cancel-btn {
    padding: 0 20rpx;
    font-size: 28rpx;
    color: #ff9500;
}

/* 内容区域 */
.content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    margin-top: 20rpx;
}

/* 滚动区域 */
.scrollable-content {
    width: 100%;
    height: 100%;
}

/* 发行人列表项样式 */
.issuer-item {
    background-color: #fff;
    padding: 30rpx 20rpx;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0px 7px 14px 0px rgba(219,219,219,0.48);
}

.issuer-top-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;
}

.bond-name-row {
    display: flex;
    align-items: center;
}

.bond-name {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-right: 20rpx;
}

.tag-text {
    font-size: 24rpx;
    padding: 2rpx 16rpx;
    background: linear-gradient(to right, #C8BEFF, #856FFE);
    color: #FFFFFF;
    border-bottom-left-radius: 22rpx;
    border-top-right-radius: 22rpx;
    white-space: nowrap;
}

/* 城投标签特殊样式 */
.issuer-tag {
    background: linear-gradient(to right, #FFD28A, #FF9500);
}

.add-btn {
    width: auto;
    height: 60rpx;
    border-radius: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FF9900;
    padding: 0 20rpx;
}

.add-text {
    font-size: 26rpx;
    color: #FF9900;
    margin-left: 4rpx;
}

.issuer-details-row {
    display: flex;
    justify-content: space-between;
}

.detail-column {
    display: flex;
    flex-direction: column;
    align-items: left;
}

.detail-value {
    font-size: 28rpx;
    color: #333;
    font-weight: bold;
    margin-bottom: 6rpx;
}

.detail-label {
    font-size: 24rpx;
    color: #999;
}
</style> 