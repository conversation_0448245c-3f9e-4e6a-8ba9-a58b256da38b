<template>
    <view class="container">

        <!-- 搜索框 -->
        <view class="search-box">
            <view class="search-input">
                <uni-icons type="search" size="18" color="#999"></uni-icons>
                <input type="text" placeholder="请输入企业名称" v-model="searchCompany" placeholder-class="placeholder" />
            </view>
            <text class="cancel-btn" @tap="goBack">取消</text>
        </view>

        <!-- 企业卡片区域 -->
        <view class="content-wrapper">
            <!-- 加载状态 -->
            <view v-if="isLoading" class="loading-container">
                <uni-icons type="spinner-cycle" size="32" color="#FF9900"></uni-icons>
                <text class="loading-text">搜索中...</text>
            </view>

            <!-- 搜索提示 -->
            <view v-else-if="!hasSearched && !searchCompany.trim()" class="search-tip">
                <uni-icons type="search" size="48" color="#ccc"></uni-icons>
                <text class="tip-text">请输入企业名称进行搜索</text>
            </view>

            <!-- 无搜索结果 -->
            <view v-else-if="hasSearched && companyList.length === 0 && !isLoading" class="no-result">
                <uni-icons type="info" size="48" color="#ccc"></uni-icons>
                <text class="no-result-text">未找到相关企业</text>
            </view>

            <!-- 企业列表 -->
            <scroll-view v-else class="scrollable-content" scroll-y :show-scrollbar="false" enhanced>
                <view v-for="(company, index) in companyList" :key="index" class="issuer-item">
                    <view class="issuer-top-row">
                        <view class="bond-name-row">
                            <text class="bond-name">{{ company.bmEntName || company.entName || '未知企业' }}</text>
                            <text v-if="company.entType" class="tag-text issuer-tag">{{ company.entType }}</text>
                        </view>
                        <view class="add-btn" @tap="addCompany(company)">
                            <uni-icons type="plus" size="16" color="#FF9900"></uni-icons>
                            <text class="add-text">添加</text>
                        </view>
                    </view>
                    <view class="issuer-details-row">
                        <view class="detail-column">
                            <text class="detail-value">{{ company.creditRating || '--' }}</text>
                            <text class="detail-label">主体评级</text>
                        </view>
                        <view class="detail-column">
                            <text class="detail-value">{{ company.region || company.area || '--' }}</text>
                            <text class="detail-label">地区</text>
                        </view>
                        <view class="detail-column">
                            <text class="detail-value">{{ company.entNature || company.subjectProperty || '--' }}</text>
                            <text class="detail-label">主体性质</text>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { getBenchmarkCompanyList } from '@/api/benchmarkAnalysis';
import { usePermissionStore } from '@/stores/permission';

// 定义企业数据类型
interface CompanyItem {
    bmEntId?: string;
    bmEntName?: string;
    entName?: string;
    entId?: string;
    entType?: string;
    creditRating?: string;
    region?: string;
    area?: string;
    entNature?: string;
    subjectProperty?: string;
    bmEntOrgCode?: string;
    entOrgCode?: string;
    [key: string]: any;
}

// 获取用户信息
const permissionStore = usePermissionStore();
const userInfo = permissionStore.getUserInfo;

// 搜索输入
const searchCompany = ref('');

// 企业列表数据
const companyList = ref<CompanyItem[]>([]);
const isLoading = ref(false);
const hasSearched = ref(false);

// 防抖定时器
let debounceTimer: number | null = null;

// 防抖搜索函数
const debounceSearch = (searchText: string) => {
    if (debounceTimer) {
        clearTimeout(debounceTimer);
    }

    debounceTimer = setTimeout(() => {
        if (searchText.trim()) {
            searchCompanies(searchText.trim());
        } else {
            // 清空搜索时重置列表
            companyList.value = [];
            hasSearched.value = false;
        }
    }, 500); // 500ms 防抖延迟
};

// 监听搜索输入变化
watch(searchCompany, (newValue) => {
    debounceSearch(newValue);
});

// 搜索企业
const searchCompanies = async (keyword: string) => {
    if (!keyword.trim()) return;

    isLoading.value = true;
    hasSearched.value = true;

    try {
        const params = {
            compName: keyword, // 企业名称搜索关键词
            entId: userInfo.outCompCode || '' // 从store获取企业ID
        };

        const res = await getBenchmarkCompanyList(params);
        console.log('企业搜索结果:', res);

        if (res.data && res.data.code === '000' && res.data.data) {
            companyList.value = res.data.data || [];
        } else {
            companyList.value = [];
            if (res.data && res.data.message) {
                uni.showToast({
                    title: res.data.message,
                    icon: 'none',
                    duration: 2000
                });
            }
        }
    } catch (error) {
        console.error('搜索企业失败:', error);
        companyList.value = [];
        uni.showToast({
            title: '搜索失败，请重试',
            icon: 'none',
            duration: 2000
        });
    } finally {
        isLoading.value = false;
    }
};

// 添加企业
const addCompany = (company: CompanyItem) => {
    console.log('添加企业:', company);
    uni.showToast({
        title: '已添加企业',
        icon: 'success',
        duration: 2000
    });

    // 可以在这里添加实际的添加企业逻辑
    // 比如调用添加企业的API接口
};

// 返回上一页
const goBack = () => {
    uni.navigateBack();
};

onMounted(() => {
    // 页面加载时的逻辑
    console.log('用户信息:', userInfo);
});
</script>

<style lang="scss" scoped>
/* 页面容器 */
.container {
    padding: 0 20rpx 40rpx 20rpx;
    height: 100vh;
    box-sizing: border-box;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}


/* 搜索框 */
.search-box {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
}

.search-input {
    flex: 1;
    height: 72rpx;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 36rpx;
    display: flex;
    align-items: center;
    padding: 0 24rpx;
    backdrop-filter: blur(10rpx);
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.search-input uni-icons {
    margin-right: 10rpx;
}

.search-input input {
    flex: 1;
    height: 100%;
    font-size: 28rpx;
}

.search-input .placeholder {
    color: #999999;
}

.cancel-btn {
    padding: 0 20rpx;
    font-size: 28rpx;
    color: #ff9500;
}

/* 内容区域 */
.content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    margin-top: 20rpx;
}

/* 滚动区域 */
.scrollable-content {
    width: 100%;
    height: 100%;
}

/* 发行人列表项样式 */
.issuer-item {
    background-color: #fff;
    padding: 30rpx 20rpx;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0px 7px 14px 0px rgba(219,219,219,0.48);
}

.issuer-top-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;
}

.bond-name-row {
    display: flex;
    align-items: center;
}

.bond-name {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-right: 20rpx;
}

.tag-text {
    font-size: 24rpx;
    padding: 2rpx 16rpx;
    background: linear-gradient(to right, #C8BEFF, #856FFE);
    color: #FFFFFF;
    border-bottom-left-radius: 22rpx;
    border-top-right-radius: 22rpx;
    white-space: nowrap;
}

/* 城投标签特殊样式 */
.issuer-tag {
    background: linear-gradient(to right, #FFD28A, #FF9500);
}

.add-btn {
    width: auto;
    height: 60rpx;
    border-radius: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FF9900;
    padding: 0 20rpx;
}

.add-text {
    font-size: 26rpx;
    color: #FF9900;
    margin-left: 4rpx;
}

.issuer-details-row {
    display: flex;
    justify-content: space-between;
}

.detail-column {
    display: flex;
    flex-direction: column;
    align-items: left;
}

.detail-value {
    font-size: 28rpx;
    color: #333;
    font-weight: bold;
    margin-bottom: 6rpx;
}

.detail-label {
    font-size: 24rpx;
    color: #999;
}

/* 加载状态样式 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
}

.loading-text {
    font-size: 28rpx;
    color: #999;
    margin-top: 20rpx;
}

/* 搜索提示样式 */
.search-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
}

.tip-text {
    font-size: 28rpx;
    color: #ccc;
    margin-top: 20rpx;
}

/* 无结果样式 */
.no-result {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
}

.no-result-text {
    font-size: 28rpx;
    color: #ccc;
    margin-top: 20rpx;
}
</style> 