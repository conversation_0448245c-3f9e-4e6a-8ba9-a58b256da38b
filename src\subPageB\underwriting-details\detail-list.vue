<template>
	<view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
		<!-- 固定的头部区域 -->
		<view class="fixed-header">
			<CustomHead :title="nameParam" />
		</view>
		<view class="content-wrapper">
			<!-- 内容大卡片 -->
			<view class="content-card">
				<!-- 卡片标题 -->
				<view class="card-header">
					<view class="title-wrapper">
						<view class="title-icon"></view>
						<text class="title-text">信息列表</text>
					</view>
				</view>
				
				<!-- 表格内容 -->
				<view class="table-wrapper">
					<SimpleTable
						:columns="tableColumns" 
						:data="detailList" 
						:stripe="false" 
						:border="false"
						:highlight="true" 
						:loading="isLoading"
						:hasMore="hasMore"
						height="calc(100vh - 300rpx)"
						:cell-style="cellStyle"
						:header-style="headerStyle"
						:show-header="true"
						@row-click="handleRowClick"
						@load-more="handleLoadMore">
					</SimpleTable>
					
					<!-- 无数据提示 -->
					<view v-if="!detailList.length && !isLoading" class="no-data-tip">
						<text class="no-data-text">暂无相关数据</text>
					</view>

					<!-- 没有更多数据提示 -->
					<view v-if="!hasMore && detailList.length > 0 && !isLoading" class="no-more-tip">
						<text class="no-more-text">已显示全部数据</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed } from 'vue';
import CustomHead from '@/components/head/head.vue';
import { getSql } from '@/api/common';
import { getCustomListHead } from '@/api/dataDict';
import SimpleTable from '@/components/SimpleTable/index.vue';
import { onLoad } from '@dcloudio/uni-app';
import { getAssetUrl } from '@/config/assets';

const tableColumns = ref([]);
const detailList = ref([]);
const nameParam = ref('');
const isLoading = ref(false);
const hasMore = ref(true);
const pageNo = ref(1);
const pageSize = ref(15);

const getCustomListHeadData = async () => {
    const params = {
        id: "a57acc4187e4464daf83c00d4bdafec3",
        moduleId: "1352320216532336640"
    }
    const res = await getCustomListHead(params);
    
    if (res.data && res.data.data && res.data.data.column) {
        const columnList = res.data.data.column;
        const visibleColumns = columnList
            .filter(col => col.defaultShow === 'Y')
            .sort((a, b) => a.order - b.order);
        
        tableColumns.value = visibleColumns.map((col) => {
            const isUnderwriterColumn = 
                col.columnCode && (
                    col.columnCode.toLowerCase().includes('underwriter') ||
                    col.columnCode.toLowerCase().includes('undertaker') ||
                    col.columnCode.toLowerCase().includes('承销')
                ) ||
                col.columnTitle && (
                    col.columnTitle.includes('承销商') ||
                    col.columnTitle.includes('承销') ||
                    col.columnTitle.includes('主承销商')
                );
            
            return {
                name: col.columnCode,
                label: col.columnTitle,
                width: col.width || (isUnderwriterColumn ? 320 : 200),
                align: col.align || 'left',
                fixed: isUnderwriterColumn,
                formatter: col.formatter,
                emptyString: '--'
            };
        });
    }
};

onLoad((option) => {
	if (option.name) {
		nameParam.value = option.name;
        initializeData();
	}
});

const initializeData = async () => {
    await getCustomListHeadData();
    await getDetailList();
};

const getDetailList = async (loadMore = false) => {
    if (isLoading.value) return;
    
    isLoading.value = true;
    
    const data = {
        params: {
            name: nameParam.value,
            bondTypes:[],
            ownedModuleid: "1352320216532336640",
            ccid: "a57acc4187e4464daf83c00d4bdafec3"
        },
        page: {
            pageNo: loadMore ? pageNo.value : 1,
            pageSize: pageSize.value,
            sort: null,
            direction: null
        }
    };
    
    try {
        const res = await getSql(data);
        
        if (res.data && res.data.data && res.data.data.pageInfo) {
            const apiData = res.data.data.pageInfo.list;
            const total = res.data.data.pageInfo.total || 0;
            
            if (loadMore) {
                detailList.value = [...detailList.value, ...apiData];
            } else {
                detailList.value = apiData;
                pageNo.value = 1;
            }
            
            const currentTotal = detailList.value.length;
            hasMore.value = currentTotal < total;
            
        } else {
            if (!loadMore) {
                detailList.value = [];
            }
            hasMore.value = false;
        }
    } catch (error) {
        console.error('获取明细列表失败', error);
        uni.showToast({
            title: '获取明细列表失败',
            icon: 'none'
        });
        
        if (loadMore && pageNo.value > 1) {
            pageNo.value -= 1;
        }
    } finally {
        isLoading.value = false;
    }
};

const handleLoadMore = async () => {
    if (!hasMore.value || isLoading.value) return;
    
    pageNo.value += 1;
    await getDetailList(true);
};

const handleRowClick = (row) => {
    // 行点击业务逻辑
};

const cellStyle = ({ column }) => {
    if (column.fixed) {
        return {
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            maxWidth: '300rpx',
            fontWeight: '500'
        };
    }
    
    if (column.name && (
        column.name === 'sInfoName' || 
        column.label && (column.label.includes('债券简称') || column.label.includes('简称'))
    )) {
        return {
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            maxWidth: '300rpx',
            fontWeight: '500'
        };
    }
    
    if (column.name && (column.name.includes('amount') || column.name.includes('Amount'))) {
        return {
            fontFamily: "'DIN Alternate', Arial, sans-serif",
            textAlign: 'right'
        };
    }
    
    if (column.name && column.name.includes('status')) {
        return {
            textAlign: 'center'
        };
    }
    
    return {};
};

const headerStyle = {
    backgroundColor: '#F8F8F8',
    color: '#333',
    fontWeight: 'bold',
    fontSize: '28rpx',
    borderBottom: '2rpx solid #E8E8E8'
};
</script>

<style lang="scss" scoped>
.container {
	width: 100%;
	height: 100vh;
	background-color: #F5F7FA;
	background-size: 100% auto;
	background-repeat: no-repeat;
	background-position: top center;
	display: flex;
	flex-direction: column;
}

.fixed-header {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 100;
}

.content-wrapper {
	flex: 1;
	margin-top: 168rpx;
	padding: 20rpx 30rpx 30rpx;
	box-sizing: border-box;
	height: calc(100vh - 168rpx);
	display: flex;
	flex-direction: column;
}

.content-card {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 30rpx;
	flex: 1;
	display: flex;
	flex-direction: column;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
	padding: 0 10rpx;
}

.title-wrapper {
	display: flex;
	align-items: center;
}

.title-icon {
	width: 48rpx;
	height: 52rpx;
	position: relative;
	top: -10rpx;

	&::before {
		content: '';
		position: absolute;
		inset: 0;
		background: linear-gradient(135deg, rgba(133, 111, 254, 0.6) 0%, rgba(255, 237, 221, 0) 100%);
		clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
	}
}

.title-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	transform: translateX(-25rpx);
}

.table-wrapper {
	flex: 1;
	overflow: hidden;
	position: relative;
}

:deep(.simple-table) {
	height: 100%;
}

.no-data-tip {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 80rpx 0 60rpx 0;
	
	
	.no-data-text {
		font-size: 28rpx;
		color: #999999;
	}
}

.no-more-tip {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 30rpx 0;
	
	.no-more-text {
		font-size: 24rpx;
		color: #c0c4cc;
	}
}
</style> 