<template>
    <view class="rate-chart">
        <!-- 有数据时显示图表 -->
        <view v-if="props.chartData && props.chartData.length > 0" :style="{ width: '100%', height: height }">
            <LimeEchart ref="chartRef" style="width: 100%; height: 100%;" @finished="onChartFinished" />
        </view>
        <!-- 无数据时显示空状态 -->
        <view v-else class="empty-state" :style="{ height: height }">
            <view class="empty-content">
                <text class="empty-text">暂无数据</text>
                <text class="empty-desc">图表数据加载中或暂无相关数据</text>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, watch } from 'vue';
// 引入echarts（Vue3小程序需要用require）
// #ifdef MP
const echarts = require('../../uni_modules/lime-echart/static/echarts.min.js');
// #endif
// #ifndef MP  
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min.js';
// #endif
import LimeEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart.vue';

// Props定义
const props = defineProps({
    // 图表数据：[{ date: '2024/01/08', value: 3.1500, bpChange: 5.2 }, ...]
    chartData: {
        type: Array,
        default: () => []
    },
    // 图表高度
    height: {
        type: String,
        default: '460rpx'
    },
    // Y轴单位
    yAxisUnit: {
        type: String,
        default: '利率(%)'
    }
});

// 图表引用
const chartRef = ref(null);

// 定义事件
const emit = defineEmits(['point-touch']);

// 图表配置
const getChartOption = () => {
    const data = props.chartData;
    // 如果没有数据，返回基本配置以避免 ECharts 报错
    if (!data || !data.length) {
        return {
            grid: {
                left: '0',
                right: '40rpx',
                top: '15%',
                bottom: '5%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: [],
                axisLine: { show: false },
                axisTick: { show: false },
                axisLabel: { show: false }
            },
            yAxis: {
                type: 'value',
                name: props.yAxisUnit,
                nameLocation: 'end',
                nameTextStyle: {
                    color: '#666',
                    fontSize: 12,
                    align: 'right'
                },
                axisLine: { show: false },
                axisTick: { show: false },
                axisLabel: { show: false },
                splitLine: { show: false }
            },
            series: [{
                type: 'line',
                data: []
            }]
        };
    }
    
    // 提取数据
    const dates = data.map(item => item.date);     // 日期数组
    const values = data.map(item => item.value);   // 数值数组
    const maxIndex = values.indexOf(Math.max(...values));  // 最大值索引
    const minIndex = values.indexOf(Math.min(...values));  // 最小值索引
    
    return {
        // 图表绘图网格配置
        grid: {
            left: '0',              // 左边距
            right: '40rpx',         // 右边距，为Y轴单位留空间
            top: '15%',             // 上边距，为标题和单位留空间
            bottom: '5%',           // 下边距
            containLabel: true      // 包含坐标轴标签在内
        },
        // X轴配置（时间轴）
        xAxis: {
            type: 'category',                           // 类目轴类型
            data: dates,                                // X轴数据（日期数组）
            boundaryGap: false,                         // 坐标轴两边不留白
            // X轴线配置
            axisLine: { 
                show: true,                             // 显示轴线
                lineStyle: { color: '#E5E5E5' }         // 轴线颜色
            },
            // X轴刻度配置
            axisTick: {
                show: false                             // 隐藏刻度线
            },
            // X轴标签配置
            axisLabel: {
                color: '#666',                          // 标签文字颜色
                fontSize: 12,                           // 标签字体大小
                margin: 15,                             // 标签与轴线的距离
                // 只显示首尾两个时间点
                interval: function(index) { 
                    return index === 0 || index === dates.length - 1; 
                },
                // 格式化标签显示
                formatter: function(value, index) {
                    if (index === 0) {
                        return '{left|' + value + '}';   // 起始点左对齐
                    } else if (index === dates.length - 1) {
                        return '{right|' + value + '}';  // 末尾点右对齐
                    }
                    return value;
                },
                // 富文本样式配置
                rich: {
                    left: {
                        align: 'left'                   // 左对齐样式
                    },
                    right: {
                        align: 'right'                  // 右对齐样式
                    }
                }
            }
        },
        // Y轴配置（数值轴）
        yAxis: {
            type: 'value',                              // 数值轴类型
            name: props.yAxisUnit,                      // Y轴单位名称（如：利率(%)）
            nameLocation: 'end',                        // 单位显示在轴的末端（顶部）
            // Y轴单位文字样式
            nameTextStyle: {
                color: '#666',                          // 单位文字颜色
                fontSize: 12,                           // 单位字体大小
                align: 'right'                          // 单位右对齐
            },
            min: Math.min(...values),                   // Y轴最小值
            max: Math.max(...values),                   // Y轴最大值
            splitNumber: 4,                             // 分割段数（4个区间，5个刻度点）
            // Y轴线配置
            axisLine: { show: false },                  // 隐藏Y轴线
            // Y轴刻度配置
            axisTick: { show: false },                  // 隐藏Y轴刻度
            // Y轴标签配置
            axisLabel: {
                color: '#999',                          // 标签文字颜色（较浅）
                fontSize: 12,                           // 标签字体大小
                formatter: function(value) {            // 标签格式化：保留4位小数
                    return value.toFixed(4); 
                }
            },
            // Y轴网格线配置
            splitLine: { 
                show: true,                             // 显示网格线
                lineStyle: {                            // 网格线样式
                    color: '#F0F0F0',                   // 网格线颜色
                    type: 'dashed'                      // 虚线类型
                },
                interval: function(index) {
                    // 不显示第0条线（X轴位置），只显示中间的4条网格线
                    return index !== 0;
                }
            }
        },
        // 图表系列配置
        series: [{
            type: 'line',                               // 折线图类型
            data: values,                               // 数据数组（Y轴数值）
            smooth: true,                               // 平滑曲线
            // 线条样式
            lineStyle: { 
                color: '#FF9900',                       // 线条颜色（橙色）
                width: 1.2                                // 线条宽度
            },
            // 数据点样式
            itemStyle: { color: '#FF9900' },            // 数据点颜色
            symbol: 'none',                             // 默认不显示数据点标记
            symbolSize: 0,                              // 数据点大小为0
            // 鼠标悬停效果
            emphasis: {
                focus: 'series',                        // 聚焦当前系列
                itemStyle: {                            // 悬停时数据点样式
                    borderColor: '#FF9900',             // 边框颜色
                    borderWidth: 2,                     // 边框宽度
                    color: '#FF9900'                    // 填充颜色
                },
                symbol: 'circle',                       // 悬停时显示圆形标记
                symbolSize: 8                           // 悬停时标记大小
            },
            // 标记点配置（最高点和最低点）
            markPoint: {
                data: [
                    // 最高点标记
                    {
                        coord: [maxIndex, values[maxIndex]],    // 最高点坐标[x轴索引, y轴数值]
                        itemStyle: { color: '#E76056' },        // 标记点颜色（红色）
                        label: {
                            show: true,                         // 显示标签
                            position: 'inside',                 // 标签位置（相对于标记点）
                            offset: [0, -15],                   // 向上偏移15px
                            formatter: values[maxIndex].toFixed(4) + '%',  // 标签文本格式
                            color: '#E76056',                   // 标签文字颜色
                            fontSize: 12,                       // 标签字体大小
                            backgroundColor: 'rgba(255, 255, 255, 0.8)',  // 标签背景色
                            borderRadius: 4,                    // 标签圆角
                            padding: [2, 6]                     // 标签内边距
                        }
                    },
                    // 最低点标记
                    {
                        coord: [minIndex, values[minIndex]],    // 最低点坐标[x轴索引, y轴数值]
                        itemStyle: { color: '#52C41A' },        // 标记点颜色（绿色）
                        label: {
                            show: true,                         // 显示标签
                            position: 'inside',                 // 标签位置（相对于标记点）
                            offset: [0, 15],                    // 向下偏移15px
                            formatter: values[minIndex].toFixed(4) + '%',  // 标签文本格式
                            color: '#52C41A',                   // 标签文字颜色
                            fontSize: 12,                       // 标签字体大小
                            backgroundColor: 'rgba(255, 255, 255, 0.8)',  // 标签背景色
                            borderRadius: 4,                    // 标签圆角
                            padding: [2, 6]                     // 标签内边距
                        }
                    }
                ],
                symbolSize: 8                                   // 标记点大小
            }
        }],
        // 图例配置
        legend: { show: false },                        // 隐藏图例
        // 工具箱配置
        toolbox: { show: false },                       // 隐藏工具箱
        // 提示框配置
        tooltip: {
            trigger: 'axis',                            // 坐标轴触发
            // 指示器配置
            axisPointer: {
                type: 'line',                           // 直线指示器
                axis: 'x',                              // X轴方向（竖直线）
                lineStyle: {                            // 指示器线条样式
                    color: '#999',                      // 线条颜色
                    width: 1,                           // 线条宽度
                    type: 'dashed'                      // 虚线类型
                },
                label: {                                // 指示器标签（显示时间）
                    show: true,                         // 显示标签
                    backgroundColor: '#6a7985',         // 标签背景色
                    color: '#fff',                      // 标签文字颜色
                    fontSize: 12,                       // 标签字体大小
                    formatter: function(params) {       // 标签格式化
                        return params.value;            // 显示时间值
                    }
                }
            },
            // 提示框内容格式化
            formatter: function(params) {
                if (params && params.length > 0) {
                    const dataIndex = params[0].dataIndex;
                    const pointData = data[dataIndex];
                    if (pointData) {
                        // 构建触摸数据对象
                        const touchData = {
                            index: dataIndex,              // 数据索引
                            date: pointData.date,           // 日期
                            value: pointData.value,         // 数值
                            bpChange: pointData.bpChange || 'N/A',  // 涨跌BP值
                        };
                        // 通过emit传给父组件
                        emit('point-touch', touchData);
                    }
                }
                return '';                              // 不显示默认提示框内容
            },
            backgroundColor: 'transparent',             // 透明背景
            borderWidth: 0                              // 无边框
        }
    };
};

// 图表初始化
const onChartFinished = () => {
    setTimeout(() => {
        chartRef.value?.init(echarts, (chart) => {
            chart.setOption(getChartOption());
        });
    }, 100);
};

// 暴露更新图表的方法
const updateChart = () => {
    if (chartRef.value) {
        chartRef.value.setOption(getChartOption());
    }
};

// 监听数据变化，重新渲染图表
watch(() => props.chartData, () => {
    updateChart();
}, { deep: true });

defineExpose({ updateChart });
</script>

<style lang="scss" scoped>
.rate-chart {
    width: 100%;
}

.empty-state {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fafafa;
    border-radius: 8rpx;
    
    .empty-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        
        .empty-text {
            font-size: 32rpx;
            color: #999;
            margin-bottom: 16rpx;
            font-weight: 500;
        }
        
        .empty-desc {
            font-size: 24rpx;
            color: #ccc;
            text-align: center;
            line-height: 1.4;
        }
    }
}
</style> 