# 权限控制自定义TabBar使用说明

## 概述

本项目实现了基于权限动态控制TabBar显示的功能。通过后端返回的权限列表，可以动态控制哪些Tab显示，哪些Tab隐藏。

## 实现原理

### 1. 权限数据获取和存储

在用户登录成功后，通过调用 `getPermissionList` API 获取用户的权限列表，并按类型分别存储到本地：

```typescript
// src/pages/sign_in/sign_in.vue
const permissionList = await getPermissionList();
if (permissionList.data.code == '000') {
    // 分离权限数据
    const permissionData = permissionList.data.data;
    
    // 提取TabBar权限(combinationName为'小程序'的权限)
    const tabBarPermissions = permissionData.filter(item => item.combinationName === '小程序');
    
    // 提取首页模块权限(combinationName为'首页'的权限) 
    const homePagePermissions = permissionData.filter(item => item.combinationName === '首页');
    
    // 分别存储不同类型的权限
    uni.setStorageSync('tabBarPermissions', tabBarPermissions);
    uni.setStorageSync('homePagePermissions', homePagePermissions);
}
```

### 2. 权限数据结构

权限数据的结构如下：

```typescript
interface Permission {
    modulename: string;        // 模块名称，如"发行查询"、"自选债券"等
    combinationName: string;   // 组合名称，如"小程序"、"首页"
    moduletype: string;        // 模块类型
    showflag: string;         // 显示标志，"Y"表示显示，"N"表示隐藏
}
```

### 3. TabBar组件配置

在 `AppTabBar` 组件中，通过 `tabList` 配置各个Tab项：

```javascript
tabList: [
    {
        index: 0,
        pagePath: "pages/home/<USER>",
        iconPath: "/static/tarbar/home.png",
        selectedIconPath: "/static/tarbar/home1.png",
        text: "首页",
        alwaysShow: true  // 始终显示
    },
    {
        index: 1,
        pagePath: "pages/issuance/index",
        iconPath: "/static/tarbar/issuance.png",
        selectedIconPath: "/static/tarbar/issuance1.png",
        text: "发行查询",
        alwaysShow: false  // 根据权限控制
    },
    // ... 其他tab配置
]
```

### 4. 权限过滤逻辑

通过 `computed` 属性 `filteredTabList` 实现权限过滤：

```javascript
computed: {
    filteredTabList() {
        // 如果没有权限数据，返回必显示的tab
        if (!this.permissionData || this.permissionData.length === 0) {
            return this.tabList.filter(tab => tab.alwaysShow);
        }

        // 权限数据已经是过滤后的TabBar权限
        const tabPermissions = this.permissionData;

        return this.tabList.filter(tab => {
            // 首页和我的始终显示
            if (tab.alwaysShow) {
                return true;
            }
            
            // 查找对应的权限项
            const permission = tabPermissions.find(item => 
                item.modulename === tab.text && 
                item.moduletype !== 'directory'
            );
            
            return permission && permission.showflag === 'Y';
        });
    }
}
```

## 使用方法

### 1. 在页面中使用AppTabBar

在需要显示TabBar的页面中，引入并使用 `AppTabBar` 组件：

```vue
<template>
    <view class="container">
        <!-- 页面内容 -->
        
        <!-- 自定义tabBar -->
        <AppTabBar :selectNumber="0" :permissionData="permissionList" />
    </view>
</template>

<script setup>
import { ref } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import AppTabBar from '@/components/AppTabBar/index.vue';

// 权限数据
const permissionList = ref([]);

// 页面显示时获取TabBar权限数据
onShow(() => {
    permissionList.value = uni.getStorageSync('tabBarPermissions') || [];
});
</script>
```

### 2. selectNumber 参数说明

`selectNumber` 参数用于指定当前选中的Tab索引：

- 0: 首页
- 1: 发行查询
- 2: 自选债券
- 3: 付息兑付
- 4: 我的

### 3. 权限配置说明

需要在后端配置相应的权限项，权限项的 `modulename` 需要与TabBar中的 `text` 字段完全匹配：

- "首页" - 始终显示，无需权限
- "发行查询" - 需要权限控制
- "自选债券" - 需要权限控制
- "付息兑付" - 需要权限控制
- "我的" - 始终显示，无需权限

## 工具函数

项目提供了 `src/utils/permission.ts` 工具文件，包含以下功能：

```typescript
// 获取存储的TabBar权限列表
export const getStoredTabBarPermissions = (): Permission[]

// 获取存储的首页权限列表
export const getStoredHomePagePermissions = (): Permission[]

// 检查是否有特定TabBar模块的权限
export const hasTabBarModulePermission = (moduleName: string): boolean

// 获取可见的tab列表
export const getVisibleTabs = ()

// 获取首个可访问的tab页面路径
export const getFirstAccessibleTabPath = (): string
```

## 注意事项

1. **权限数据存储**：权限数据分两部分存储在本地Storage中
   - TabBar权限：key为 `tabBarPermissions`
   - 首页权限：key为 `homePagePermissions`

2. **权限刷新**：如果权限发生变化，需要重新登录或手动更新Storage中的权限数据

3. **默认显示**：如果没有权限数据或权限数据为空，只显示 `alwaysShow: true` 的Tab项

4. **权限匹配**：权限匹配时使用的是 `modulename` 与Tab的 `text` 字段进行匹配，需要确保名称完全一致

5. **页面跳转**：当点击没有权限的Tab时，不会进行跳转

## 示例场景

假设后端返回的权限数据如下：

```json
[
    {
        "modulename": "发行查询",
        "combinationName": "小程序",
        "moduletype": "menu",
        "showflag": "Y"
    },
    {
        "modulename": "自选债券",
        "combinationName": "小程序",
        "moduletype": "menu",
        "showflag": "N"
    },
    {
        "modulename": "市场利率",
        "combinationName": "首页",
        "moduletype": "menu",
        "showflag": "Y"
    }
]
```

处理后的存储数据：
- `tabBarPermissions`: 包含"发行查询"和"自选债券"的权限
- `homePagePermissions`: 包含"市场利率"的权限

最终TabBar会显示：首页、发行查询、我的（自选债券和付息兑付被隐藏） 