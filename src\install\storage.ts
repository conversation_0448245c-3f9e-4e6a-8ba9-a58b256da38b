const storage = {
  set(key: string, value: any) {
    try {
      uni.setStorageSync(key, value)
    } catch (e) {
      console.error(e)
    }
  },

  get(key: string) {
    let value
    try {
      value = uni.getStorageSync(key)
    } catch (e) {
      console.error(e)
    }
    return value
  },
  remove(key: string) {
    try {
      uni.removeStorageSync(key)
    } catch (e) {
      console.error(e)
    }
  },
  clear() {
    try {
      uni.clearStorageSync()
    } catch (e) {
      console.error(e)
    }
  },
  // 返回一个布尔值，表示 key 是否在本地缓存之中
  has(key: string) {
    let bean
    try {
      const res = uni.getStorageInfoSync()
      bean = res.keys.includes(key)
    } catch (e) {
      console.error(e)
    }
    return bean
  }
}

export default storage
