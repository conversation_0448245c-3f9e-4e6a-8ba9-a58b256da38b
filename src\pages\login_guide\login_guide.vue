<template>
    <view class="launch-container"
        :style="{ backgroundImage: `url('${getAssetUrl('/login_guide.png')}')` }">
        <image class="logo" :src="getAssetUrl('/launch/cdyhlogo.png')"></image>
        <!-- <button class="login-button" open-type="getRealNameAuth">立即登录</button> -->
        <button class="go-button" @click="handleGo">前往登录</button>
			
    </view>
</template>

<script lang="ts" setup>
import { getAssetUrl } from '@/config/assets';
const handleGo = () => {
    uni.navigateTo({
        url: '/pages/sign_in/sign_in',
    });
};


</script>

<style lang="scss" scoped>
.launch-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-size: cover;
    background-repeat: no-repeat;

    .logo {
        position: absolute;
        top: 100rpx;
        left: 40rpx;
        width: 220rpx;
        height: 55rpx;
    }

    .go-button {
        position: absolute;
        bottom: 200rpx;
        left: 0;
        right: 0;
        margin: auto;
        width: 480rpx;
        height: 90rpx;
        background: linear-gradient(135deg, #f1a924 0%, #FF9F43 100%);
        color: white;
        border-radius: 56rpx;
        font-size: 36rpx;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
		box-shadow: 0 4px 10px rgba(255, 159, 67, 0.2);
    }
}
</style>
