<template>
    <view class="container">
        <view class="fixed-header">
            <CustomHead title="历史定价" />
        </view>
        
        <scroll-view 
            scroll-y 
            class="history-list"
            @scrolltolower="loadMore"
            :show-scrollbar="false"
            :enhanced="true"
            lower-threshold="50"
        >
            <view v-for="(item, index) in historyData" :key="index" class="history-item">
                <view class="item-header">
                    <text class="query-date-label">查询日期:</text>
                    <text class="query-date-value">{{ formatTimestampToDate(item.queryDt) }}</text>
                </view>
                <view class="item-body">
                    <view class="info-row">
                        <text class="label">发行条件</text>
                        <text class="value">{{ item.processTerm || (item.term + ',' + item.bInfoIssuetypename + ',' + (item.isRenewal === 'Y' ? '可续期' : '不可续期')) || '--' }}</text>
                    </view>
                    <view class="info-row">
                        <text class="label">定价估值(%)</text>
                        <text class="value">{{ item.valuation ? parseFloat(item.valuation).toFixed(2) + '%' : '--' }}</text>
                    </view>
                    <view class="info-row">
                        <text class="label">估值日期</text>
                        <text class="value">{{ item.valuationDt || '--' }}</text>
                    </view>
                    <view class="info-row">
                        <text class="label">参考定价区间</text>
                        <text class="value">{{ item.priceRange || '--' }}</text>
                    </view>
                    <view class="info-row">
                        <text class="label">参考定价均值</text>
                        <text class="value" :class="{ 'value-highlight': item.priceAverage }">{{ item.priceAverage || '--' }}</text>
                    </view>
                </view>
            </view>
            
            <!-- 如果没有数据 -->
            <view v-if="historyData.length === 0 && !isLoading" class="no-data">
                <text>暂无历史记录</text>
            </view>
            
            <!-- 加载中 -->
            <view v-if="isLoading" class="loading-more">
                <text>加载中...</text>
            </view>
            
            <!-- 没有更多数据 -->
            <view v-if="historyData.length > 0 && !hasMore" class="no-more-data">
                <text>没有更多数据了</text>
            </view>
            
            <!-- 底部占位，确保能触发加载更多 -->
            <view class="bottom-space"></view>
        </scroll-view>
    </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import CustomHead from '@/components/head/head.vue';
import { getSql } from '@/api/common';
import { formatTimestampToDate } from '@/utils/calculation';

// 分页数据
const historyData = ref([]);
const pageSize = 10;
const currentPage = ref(1);
const hasMore = ref(true);
const isLoading = ref(false);
const totalCount = ref(0);

// 获取初始数据
onMounted(() => {
    getHistoryPricing();
});

const getHistoryPricing = () => {
    isLoading.value = true;
    
    const data = {    
        params: {
            ownedModuleid: '708631605142536192',
            ccid: '70474dbf54bb409aa94ad75d858a7582'
        },
        page: {
            pageNo: currentPage.value,
            pageSize: pageSize,
            sort: null,
            direction: null
        }
    }
    
    getSql(data).then(res => {
        console.log('获取历史定价数据', res);
        
        if (res.data && res.data.data && res.data.data.pageInfo) {
            const newData = res.data.data.pageInfo.list || [];
            // 转换数据为驼峰命名法
            const camelCaseData = newData;
            
            // 按照queryDt排序，时间最新的排在前面
            const sortedData = camelCaseData.sort((a, b) => {
                // 将queryDt转换为时间戳进行比较，降序排列
                const timeA = new Date(a.queryDt).getTime();
                const timeB = new Date(b.queryDt).getTime();
                return timeB - timeA; // 降序：最新的在前面
            });
            
            if (currentPage.value === 1) {
                historyData.value = sortedData;
            } else {
                historyData.value = [...historyData.value, ...sortedData];
            }
            
            totalCount.value = res.data.data.pageInfo.totalCount || 0;
            hasMore.value = historyData.value.length < totalCount.value;
        } else {
            // 接口返回异常情况处理
            if (currentPage.value === 1) {
                historyData.value = [];
            }
            hasMore.value = false;
        }
        
        isLoading.value = false;
    }).catch(err => {
        console.error('获取历史定价数据失败', err);
        isLoading.value = false;
        if (currentPage.value === 1) {
            historyData.value = [];
        }
        hasMore.value = false;
    });
}

// 上拉加载更多
const loadMore = () => {
    // 已经没有更多数据或正在加载中，不执行操作
    if (!hasMore.value || isLoading.value) {
        return;
    }
    
    console.log('触发加载更多...');
    currentPage.value++;
    getHistoryPricing();
};
</script>

<style>
/* 全局样式 */
::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

/* 针对scroll-view单独设置 */
.scroll-view-container ::-webkit-scrollbar {
  display: none !important;
}
</style>

<style lang="scss" scoped>
.container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
}

.fixed-header {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
}

.history-list {
    flex: 1;
    margin-top: 88px; /* 根据头部实际高度调整 */
    padding: 20rpx;
    box-sizing: border-box;
    height: calc(100vh - 88px); /* 确保高度正确 */
    /* 隐藏滚动条 */
    &::-webkit-scrollbar {
        display: none;
    }
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
}

.history-item {
    background-color: #fff;
    border-radius: 16rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
}

.item-header {
    padding: 24rpx 32rpx;
    background-color: #fafafa;
    display: flex;
    align-items: center;
    font-size: 28rpx;
}

.query-date-label {
    color: #333;
    font-weight: bold;
    margin-right: 16rpx;
}

.query-date-value {
    color: #666;
    font-weight: bold;
}

.item-body {
    padding: 24rpx 32rpx;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
    font-size: 26rpx;
}

.info-row:last-child {
    margin-bottom: 0;
}

.label {
    color: #999;
}

.value {
    color: #333;
    text-align: right;
}

.value-highlight {
    color: #FF8E2B;
}

.no-data {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300rpx;
    color: #999;
    font-size: 28rpx;
}

.loading-more, .no-more-data {
    text-align: center;
    padding: 20rpx 0;
    font-size: 26rpx;
    color: #999;
}

/* 添加底部空间确保能够触发加载更多 */
.bottom-space {
    height: 30rpx;
    width: 100%;
}
</style> 