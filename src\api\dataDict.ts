import request from '@/utils/request'
// 字典数据
export const getDataDict = (data: object) => {
	return request({
		url: '/api/dict/getDictItems/' + data,
		data
	})
}
// 同业存单期限字典
export const getCDTerm = (data: object) => {
	return request({
		method: 'POST',
		url: '/dict/detail/list?moduleid=',
		data
	})
}
// LPR期限字典
export const getLPRTerm = (clsno: string) => {
	return request({
		method: 'POST',
		url: '/dict?clsno='+clsno+'&moduleid=',
	})
}
// SHIBOR期限字典
export const getSHIBORTerm = (clsno: string) => {
	return request({
		method: 'POST',
		url: '/dict?clsno='+clsno+'&moduleid=',
	})
}
// 获取债券收益利率期限字典
export const getBondYieldTerm = (data: object) => {
	return request({
		method: 'POST',
		url: '/dict/detail/list?moduleid=',
		data
	})
}
// 通用获取自定义列sql数据
export const getSql = (data: object) => {
	return request({
		method: 'POST',
		url: '/tmCustomColumns/sql',
		data
	})
}
// 获取自定义列表头展示项
export const getCustomListHead = (data: object) => {
	return request({
		method: 'POST',
		url: '/tmCustomColumns/view',
		data
	})
}
// 获取信用利差曲线数据
export const getCreditSpread = (data: object) => {
	return request({
		method: 'POST',
		url: '/desktop/module/data',
		data
	})
}
// 查询债券类型
export const getBondType = (data: object) => {
	return request({
		method: 'POST',
		url: '/bond/bondDescription/queryDictBondType',
		data
	})
}
// 查询主体性质
export const getSubjectProperty = (data: object) => {
	return request({
		method: 'POST',
		url: '/dict/detail/page',
		data
	})
}
// 获取行业数据
export const getObtainData = (data: object) => {
	return request({
		method: 'POST',
		url: '/bond/bondDescription/queryBondCompindDict',
		data
	})
}
// 获取省市区
export const getArea = (data: object) => {
	return request({
		method: 'POST',
		url: '/web/common/regionLink',
		data
	})
}
// 获取发行查询列表
export const getIssuanceList = (data: object) => {
	return request({
		method: 'POST',
		url: '/bond/bondDescription/getBondSumTotal',
		data
	})
}
// 获取发行详情
export const getIssuanceDetail = (data: object) => {
	return request({
		method: 'POST',
		url: '/bond/bondDescription/queryBondDescriptionByWindCode',
		data
	})
}
