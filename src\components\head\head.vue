<template>
    <view class="custom-header">
        <view class="header-left" v-if="showBack">
            <view class="back-button" @click="handleBackClick">
                <uni-icons type="left" size="26"></uni-icons>
            </view>
        </view>
        <view class="header-title">
            <text>{{ title || '标题' }}</text>
        </view>
    </view>
</template>

<script lang="ts">
export default {
    name: 'CustomHead'
}
</script>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

// 定义组件属性
const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    // 是否显示返回按钮
    showBack: {
        type: Boolean,
        default: true
    },
    // 是否自定义返回逻辑
    customBack: {
        type: Boolean,
        default: false
    },
    // tabbar页面路径列表，用于判断是否返回到tabbar页面
    tabbarPages: {
        type: Array,
        default: () => []
    }
});

// 定义事件
const emit = defineEmits(['back']);

// 判断当前页面栈信息
const pageList = ref<any[]>([]);

onMounted(() => {
    // 获取当前页面栈
    pageList.value = getCurrentPages();
});

// 处理返回按钮点击事件
const handleBackClick = () => {
    // 如果有自定义返回逻辑，则触发back事件
    if (props.customBack) {
        emit('back');
        return;
    }

    // 获取页面栈长度
    const pagesLength = pageList.value.length;

    // 如果页面栈长度大于1，可以直接返回上一页
    if (pagesLength > 1) {
        uni.navigateBack({ delta: 1 });
        return;
    }

    // 如果页面栈长度为1，说明当前页面为首页或者是通过重定向打开的页面
    // 需要判断是否能返回到tabbar页面
    const tabbarPath = props.tabbarPages[0]; // 默认返回首页
    uni.switchTab({
        url: `/${tabbarPath}`
    });
};
</script>

<style lang="scss" scoped>
.custom-header {
    width: 100%;
    height: 88rpx;
    padding-top: 86rpx;
    position: relative;
    z-index: 999;

    .header-left {
        position: absolute;
        left: 0;
        bottom: 0;
        transform: translateY(-15%);
        height: 60rpx;
        z-index: 10;

        .back-button {
            width: 60rpx;
            height: 60rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    .header-title {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        transform: translateY(-50%);
        text-align: center;
        font-size: 32rpx;
        font-weight: 500;
        color: #333333;
        pointer-events: none;
    }
    
}
</style>
