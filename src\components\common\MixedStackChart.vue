<template>
	<view class="mixed-stack-chart-container" :style="{ width: '100%', height: `${actualChartHeight}px` }">
		<view class="chart-legend">
			<scroll-view scroll-x="true" :show-scrollbar="false" class="legend-scroll-view" ref="legendScrollView">
				<view class="legend-content">
					<view v-for="(item, index) in legendData" :key="index" class="legend-item" @tap="toggleSeries(index)" :class="{ 'legend-item-hidden': item.hidden }">
						<view class="legend-color" :style="{ backgroundColor: item.type === 'bar' ? (item.hidden ? 'transparent' : item.color) : 'transparent', borderColor: item.color, borderStyle: item.hidden ? 'dashed' : 'solid' }"></view>
						<text class="legend-label" :style="{ color: item.hidden ? '#999' : '#333', fontSize: '26rpx' }">{{ item.name }}</text>
					</view>
				</view>
			</scroll-view>
		</view>
		
		<view class="chart-content" :style="{ overflow: 'hidden', position: 'relative', height: `${actualChartHeight - 50}px` }">
			<canvas canvas-id="mixedStackChart" id="mixedStackChart" class="chart-canvas"
				:style="{ width: '100%', height: '100%' }"
				></canvas>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted, reactive, watch, nextTick, getCurrentInstance, computed, onUnmounted } from 'vue';

const props = defineProps({
	// 柱形图数据，格式：[{name: '系列名', data: [值1, 值2...]}, ...]
	barData: {
		type: Array,
		default: () => []
	},
	// 折线图数据，格式：[{name: '系列名', data: [值1, 值2...]}, ...]
	lineData: {
		type: Array,
		default: () => []
	},
	// X轴标签，格式：['公司1', '公司2', ...]
	xLabels: {
		type: Array,
		default: () => []
	},
	// 柱形图自定义颜色，格式：['#color1', '#color2', ...]
	barColors: {
		type: Array,
		default: () => ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
	},
	// 折线图自定义颜色，格式：['#color1', '#color2', ...]
	lineColors: {
		type: Array,
		default: () => ['#fc8452', '#9a60b4', '#ea7ccc', '#ff9856', '#8dc1a9']
	},
	// 图表基础高度
	chartHeight: {
		type: Number,
		default: 350
	},
	// 图表内边距
	padding: {
		type: Object,
		default: () => ({ top: 20, right: 50, bottom: 80, left: 50 })
	}
});

// 实际图表高度（自动调整）
const actualChartHeight = ref(props.chartHeight);

// 获取组件实例
const instance = getCurrentInstance();
// 图表上下文
const chartContext = ref(null);
// 画布尺寸
const canvasWidth = ref(0);
const canvasHeight = ref(0);
// 图表尺寸信息
const chartInfo = reactive({
	width: 0,
	height: 0,
	pixelRatio: 1,
	innerWidth: 0,
	innerHeight: 0,
	padding: props.padding,
	// 添加字体大小配置
	fonts: {
		axisLabel: 12, // 坐标轴标签字体大小
		valueLabel: 12, // 数值标签字体大小
		legendLabel: 13  // 图例标签字体大小
	}
});
// 图例数据
const legendData = ref([]);
// 是否隐藏某个系列
const hiddenSeries = ref({});
const legendScrollView = ref(null);
// 标记是否已初始化
const isInitialized = ref(false);
// 是否正在绘制
const isDrawing = ref(false);
// 是否需要重绘
const needsRedraw = ref(false);
// 重绘节流定时器
let redrawTimer = null;

// 有效的X轴标签数量
const xLabelsCount = computed(() => props.xLabels.length);

// 计算实际需要的图表高度
const calculateChartHeight = () => {
	// 获取最长标签长度
	const maxLabelLength = Math.max(...props.xLabels.map(label => String(label).length));
	
	// 估算标签占用的空间
	const labelSpace = Math.min(150, Math.max(80, maxLabelLength * 8 * Math.sin(Math.PI/4)));
	
	// 基础高度 + 标签所需空间
	return props.chartHeight + labelSpace - props.padding.bottom;
};

// 筛选未隐藏的柱状图数据，使用计算属性避免不必要的重复计算
const displayedBarData = computed(() => {
	const result = [];
	for (let i = 0; i < props.barData.length; i++) {
		if (!hiddenSeries.value[`bar_${i}`]) {
			result.push({ 
				data: props.barData[i].data,
				index: i 
			});
		}
	}
	return result;
});

// 筛选未隐藏的折线图数据
const displayedLineData = computed(() => {
	const result = [];
	for (let i = 0; i < props.lineData.length; i++) {
		if (!hiddenSeries.value[`line_${i}`]) {
			result.push({ 
				data: props.lineData[i].data,
				index: i 
			});
		}
	}
	return result;
});

// 优化后的初始化函数
const initCanvas = () => {
	try {
		if (!instance) return;
		
		// 获取容器尺寸
		const query = uni.createSelectorQuery().in(instance.proxy);
		query.select('.chart-content').boundingClientRect(data => {
			if (!data || typeof data.width !== 'number' || typeof data.height !== 'number') return;
			
			// 获取系统信息，用于适配不同设备的像素比
			const systemInfo = uni.getSystemInfoSync();
			// 设置像素比例
			const pixelRatio = systemInfo.pixelRatio || 2;
			
			// 计算X轴标签所需的底部空间
			const maxLabelLength = Math.max(...props.xLabels.map(label => String(label).length));
			const extraBottomPadding = Math.min(120, Math.max(80, maxLabelLength * 6));
			
			// 动态调整底部边距
			const dynamicPadding = {
				...props.padding,
				bottom: extraBottomPadding
			};
			
			// 检查尺寸是否有变化，避免不必要的重绘
			const sizeChanged = 
				chartInfo.width !== data.width || 
				chartInfo.height !== data.height ||
				JSON.stringify(chartInfo.padding) !== JSON.stringify(dynamicPadding);
			
			if (sizeChanged || !isInitialized.value) {
				// 更新图表信息
				chartInfo.width = data.width;
				chartInfo.height = data.height;
				chartInfo.pixelRatio = pixelRatio;
				chartInfo.innerWidth = chartInfo.width - dynamicPadding.left - dynamicPadding.right;
				chartInfo.innerHeight = chartInfo.height - dynamicPadding.top - dynamicPadding.bottom;
				chartInfo.padding = dynamicPadding;
				
				// 根据设备像素比调整字体大小
				// 基准字体大小
				const baseFontSize = 12;
				// 在小屏幕设备上稍微增大字体
				if (systemInfo.windowWidth < 375) {
					chartInfo.fonts.axisLabel = baseFontSize + 2;
					chartInfo.fonts.valueLabel = baseFontSize + 2;
					chartInfo.fonts.legendLabel = baseFontSize + 3;
				} else {
					chartInfo.fonts.axisLabel = baseFontSize;
					chartInfo.fonts.valueLabel = baseFontSize;
					chartInfo.fonts.legendLabel = baseFontSize + 1;
				}
				
				// 创建canvas上下文
				if (!chartContext.value) {
					const ctx = uni.createCanvasContext('mixedStackChart', instance.proxy);
					if (!ctx) return;
					chartContext.value = ctx;
				}
				
				// 准备图例数据
				prepareLegendData();
				
				// 标记为已初始化
				isInitialized.value = true;
				
				// 调用节流版的绘制函数
				throttledDrawChart();
			}
		}).exec();
	} catch (error) {
		console.error('初始化Canvas失败:', error);
	}
};

// 初始化图表
onMounted(() => {
	// 计算实际图表高度
	actualChartHeight.value = calculateChartHeight();
	
	// 延迟执行，确保DOM已渲染
	nextTick(initCanvas);
});

// 清理定时器
onUnmounted(() => {
	// 清理定时器
	if (redrawTimer) {
		clearTimeout(redrawTimer);
		redrawTimer = null;
	}
});

// 节流处理函数
const throttle = (fn, delay = 300) => {
	let lastCall = 0;
	return function(...args) {
		const now = Date.now();
		if (now - lastCall >= delay) {
			lastCall = now;
			return fn.apply(this, args);
		}
	};
};

// 节流版的绘图函数
const throttledDrawChart = () => {
	if (isDrawing.value) {
		// 如果正在绘制，标记需要重绘
		needsRedraw.value = true;
		return;
	}
	
	// 清除之前的定时器
	if (redrawTimer) {
		clearTimeout(redrawTimer);
	}
	
	// 设置新的定时器，延迟执行绘制
	redrawTimer = setTimeout(() => {
		if (isInitialized.value && chartContext.value) {
			drawChart();
			
			// 检查是否需要再次重绘
			if (needsRedraw.value) {
				needsRedraw.value = false;
				throttledDrawChart();
			}
		}
	}, 50);
};

// 监听数据变化，重绘图表
watch([() => props.barData, () => props.lineData, () => props.xLabels], () => {
	// 重新计算高度
	actualChartHeight.value = calculateChartHeight();
	prepareLegendData();
	
	// 使用节流版的绘制函数
	throttledDrawChart();
}, { deep: true });

// 准备图例数据
const prepareLegendData = () => {
	const result = [];
	
	// 添加柱形图图例
	for (let i = 0; i < props.barData.length; i++) {
		result.push({
			name: props.barData[i].name,
			color: props.barColors[i % props.barColors.length],
			type: 'bar',
			index: i,
			hidden: !!hiddenSeries.value[`bar_${i}`]
		});
	}
	
	// 添加折线图图例
	for (let i = 0; i < props.lineData.length; i++) {
		result.push({
			name: props.lineData[i].name,
			color: props.lineColors[i % props.lineColors.length],
			type: 'line',
			index: i,
			hidden: !!hiddenSeries.value[`line_${i}`]
		});
	}
	
	legendData.value = result;
};

// 计算数据的最大值和最小值 - 使用计算属性优化性能
const dataRanges = computed(() => {
	const count = xLabelsCount.value;
	
	// 计算柱形图数据范围
	let barMax = 0;
	
	// 如果没有显示的数据，给一个默认值
	if (displayedBarData.value.length === 0) {
		barMax = 100;
	} else {
		// 计算堆叠柱形图的最大值
		for (let i = 0; i < count; i++) {
			let sum = 0;
			for (let j = 0; j < displayedBarData.value.length; j++) {
				const barSeries = displayedBarData.value[j];
				// 确保数据点索引不超出范围
				if (i < barSeries.data.length && barSeries.data[i]) {
					sum += barSeries.data[i];
				}
			}
			barMax = Math.max(barMax, sum);
		}
	}
	
	// 计算折线图数据范围
	let lineMax = 0;
	
	// 如果没有显示的数据，给一个默认值
	if (displayedLineData.value.length === 0) {
		lineMax = 100;
	} else {
		for (let i = 0; i < displayedLineData.value.length; i++) {
			const lineSeries = displayedLineData.value[i];
			// 只考虑与X轴标签数量匹配的数据点
			const validData = lineSeries.data.slice(0, count);
			
			for (let j = 0; j < validData.length; j++) {
				const value = validData[j];
				if (value !== undefined && value !== null) {
					lineMax = Math.max(lineMax, value);
				}
			}
		}
	}
	
	// 向上取整，使Y轴刻度更美观
	barMax = Math.ceil(barMax / 10) * 10 || 100; // 避免为0
	lineMax = Math.ceil(lineMax / 10) * 10 || 100; // 避免为0
	
	return { barMax, lineMax };
});

// 绘制图表
const drawChart = () => {
	if (!chartContext.value || !isInitialized.value) return;
	
	// 标记正在绘制
	isDrawing.value = true;
	
	try {
		const ctx = chartContext.value;
		const { barMax, lineMax } = dataRanges.value;
		
		// 清空画布
		ctx.clearRect(0, 0, chartInfo.width, chartInfo.height);
		ctx.setFillStyle('#ffffff');
		ctx.fillRect(0, 0, chartInfo.width, chartInfo.height);
		
		// 计算每个项目宽度
		const count = xLabelsCount.value;
		const itemWidth = count > 0 ? chartInfo.innerWidth / count : chartInfo.innerWidth;
		
		// 绘制网格线
		drawGrid(barMax);
		
		// 绘制Y轴刻度标签
		drawYAxisLabels(barMax, lineMax);
		
		// 绘制X轴和X轴标签
		drawXAxis(itemWidth);
		
		// 绘制柱形图
		drawBarChart(barMax, itemWidth);
		
		// 绘制折线图
		drawLineChart(lineMax, itemWidth);
		
		// 启用阴影缓存优化
		ctx.draw(true, () => {
			// 绘制完成，取消标记
			isDrawing.value = false;
		});
	} catch (error) {
		console.error('绘制图表失败:', error);
		isDrawing.value = false;
	}
};

// 绘制网格线
const drawGrid = (barMax) => {
	const ctx = chartContext.value;
	if (!ctx) return;
	
	const step = chartInfo.innerHeight / 5; // 5等分网格线
	
	// 小程序setLineDash方法不同，自定义虚线
	const drawDashLine = (x1, y1, x2, y2, dashLen = 4) => {
		const dashNum = Math.floor(Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1)) / dashLen);
		const dashX = (x2 - x1) / dashNum;
		const dashY = (y2 - y1) / dashNum;
		
		ctx.beginPath();
		for (let i = 0; i < dashNum; i += 2) {
			ctx.moveTo(x1 + dashX * i, y1 + dashY * i);
			ctx.lineTo(x1 + dashX * (i + 1), y1 + dashY * (i + 1));
		}
		ctx.stroke();
	};
	
	ctx.setStrokeStyle('#E0E0E0');
	ctx.setLineWidth(1);
	
	// 横向网格线
	for (let i = 1; i <= 5; i++) {
		const y = props.padding.top + chartInfo.innerHeight - i * step;
		drawDashLine(props.padding.left, y, props.padding.left + chartInfo.innerWidth, y);
		
		// 左侧Y轴刻度值
		ctx.setFillStyle('#666666');
		ctx.setTextAlign('right');
		ctx.setTextBaseline('middle');
		ctx.setFontSize(chartInfo.fonts.axisLabel);
		ctx.fillText((barMax / 5 * i).toFixed(0), props.padding.left - 10, y);
	}
};

// 绘制Y轴刻度标签
const drawYAxisLabels = (barMax, lineMax) => {
	const ctx = chartContext.value;
	if (!ctx) return;
	
	// 右侧Y轴刻度标签（折线图）
	ctx.setFillStyle('#666666');
	ctx.setTextAlign('left');
	ctx.setFontSize(chartInfo.fonts.axisLabel);
	
	const step = chartInfo.innerHeight / 5;
	for (let i = 1; i <= 5; i++) {
		const y = props.padding.top + chartInfo.innerHeight - i * step;
		ctx.setTextBaseline('middle');
		ctx.fillText((lineMax / 5 * i).toFixed(0), props.padding.left + chartInfo.innerWidth + 10, y);
	}
};

// 绘制X轴和X轴标签
const drawXAxis = (itemWidth) => {
	const ctx = chartContext.value;
	if (!ctx) return;
	
	// 绘制X轴线
	ctx.beginPath();
	ctx.setStrokeStyle('#666666');
	ctx.setLineWidth(1);
	const xAxisY = props.padding.top + chartInfo.innerHeight;
	ctx.moveTo(props.padding.left, xAxisY);
	ctx.lineTo(props.padding.left + chartInfo.innerWidth, xAxisY);
	ctx.stroke();
	
	// 绘制X轴标签
	ctx.setFillStyle('#666666');
	ctx.setTextAlign('right');
	ctx.setTextBaseline('middle');
	// X轴字体小一点
	ctx.setFontSize(chartInfo.fonts.axisLabel - 2);
	
	// 设置倾斜角度（-45度）
	const angle = -Math.PI / 4;
	
	// 获取系统信息
	const systemInfo = uni.getSystemInfoSync();
	// 750设计稿下的rpx转px
	const rpxToPx = (rpx) => {
		return rpx * systemInfo.windowWidth / 750;
	};
	
	// 给X轴标签一个合适的间距，使用rpx单位
	const labelPadding = rpxToPx(20); // 20rpx转为px
	
	// 截断文本函数，超过maxLength个字符的文本将显示为前maxLength个字符加...
	const truncateText = (text, maxLength = 5) => {
		if (!text) return '';
		text = String(text);
		return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
	};
	
	for (let i = 0; i < props.xLabels.length; i++) {
		const x = props.padding.left + i * itemWidth + itemWidth / 2;
		const y = props.padding.top + chartInfo.innerHeight + labelPadding;
		
		// 处理标签文本，限制长度
		const labelText = truncateText(props.xLabels[i]);
		
		// 保存当前状态
		ctx.save();
		// 移动到文本位置
		ctx.translate(x, y);
		// 旋转Canvas
		ctx.rotate(angle);
		// 绘制文本（在原点0,0）
		ctx.fillText(labelText, 0, 0);
		// 恢复Canvas状态
		ctx.restore();
	}
};

// 绘制柱形图
const drawBarChart = (barMax, itemWidth) => {
	const ctx = chartContext.value;
	if (!ctx) return;
	
	const barWidth = itemWidth * 0.6; // 柱宽为项目宽度的60%
	const count = xLabelsCount.value;
	const displayedBars = displayedBarData.value;
	
	// 遍历X轴标签
	for (let xIndex = 0; xIndex < count; xIndex++) {
		let stackHeight = 0;
		const centerX = props.padding.left + xIndex * itemWidth + itemWidth / 2;
		
		for (let j = 0; j < displayedBars.length; j++) {
			const series = displayedBars[j];
			const seriesIndex = series.index;
			
			if (xIndex < series.data.length && series.data[xIndex] !== undefined && series.data[xIndex] !== null) {
				const value = series.data[xIndex];
				const height = (value / barMax) * chartInfo.innerHeight;
				const color = props.barColors[seriesIndex % props.barColors.length];
				
				ctx.setFillStyle(color);
				const barX = centerX - barWidth / 2;
				const barY = props.padding.top + chartInfo.innerHeight - stackHeight - height;
				
				// 绘制柱形
				ctx.fillRect(barX, barY, barWidth, height);
				
				// 累加堆叠高度
				stackHeight += height;
			}
		}
	}
};

// 绘制折线图
const drawLineChart = (lineMax, itemWidth) => {
	const ctx = chartContext.value;
	if (!ctx) return;
	
	const count = xLabelsCount.value;
	const displayedLines = displayedLineData.value;
	
	for (let i = 0; i < displayedLines.length; i++) {
		const series = displayedLines[i];
		const seriesIndex = series.index;
		const color = props.lineColors[seriesIndex % props.lineColors.length];
		
		ctx.setStrokeStyle(color);
		ctx.setLineWidth(2);
		
		// 确保数据点与X轴标签数量匹配
		const validData = series.data.slice(0, count);
		
		// 绘制折线
		ctx.beginPath();
		let isFirstPoint = true;
		
		for (let dataIndex = 0; dataIndex < validData.length; dataIndex++) {
			const value = validData[dataIndex];
			
			if (value !== undefined && value !== null) {
				const x = props.padding.left + dataIndex * itemWidth + itemWidth / 2;
				const y = props.padding.top + chartInfo.innerHeight - (value / lineMax) * chartInfo.innerHeight;
				
				if (isFirstPoint) {
					ctx.moveTo(x, y);
					isFirstPoint = false;
				} else {
					ctx.lineTo(x, y);
				}
			}
		}
		
		ctx.stroke();
		
		// 绘制点
		for (let dataIndex = 0; dataIndex < validData.length; dataIndex++) {
			const value = validData[dataIndex];
			
			if (value !== undefined && value !== null) {
				const x = props.padding.left + dataIndex * itemWidth + itemWidth / 2;
				const y = props.padding.top + chartInfo.innerHeight - (value / lineMax) * chartInfo.innerHeight;
				
				ctx.setFillStyle(color);
				ctx.beginPath();
				ctx.arc(x, y, 4, 0, Math.PI * 2);
				ctx.fill();
			}
		}
	}
};

// 图例交互 - 切换系列显示/隐藏
const toggleSeries = (index) => {
	const item = legendData.value[index];
	if (!item) return;
	
	const seriesKey = `${item.type}_${item.index}`;
	
	// 更新隐藏系列状态
	if (hiddenSeries.value[seriesKey]) {
		delete hiddenSeries.value[seriesKey];
		item.hidden = false;
	} else {
		hiddenSeries.value[seriesKey] = true;
		item.hidden = true;
	}
	
	// 使用节流版的绘制函数
	throttledDrawChart();
};

// 暴露方法让父组件可以手动触发重绘
defineExpose({
	renderChart: throttledDrawChart
});
</script>

<style lang="scss" scoped>
.mixed-stack-chart-container {
	width: 100%;
	display: flex;
	flex-direction: column;
	
	.chart-legend {
		width: 100%;
		height: 40px;
		overflow: hidden;
		margin-bottom: 10px;
		
		.legend-scroll-view {
			width: 100%;
			height: 100%;
			white-space: nowrap;
		}
		
		.legend-content {
			display: inline-flex;
			align-items: center;
			height: 100%;
			padding: 0 10px;
		}
		
		.legend-item {
			display: flex;
			align-items: center;
			margin-right: 15px;
			padding: 5px;
			border-radius: 3px;
			transition: all 0.2s ease;
			
			&:active {
				background-color: rgba(0, 0, 0, 0.05);
			}
			
			&.legend-item-hidden {
				opacity: 0.6;
			}
			
			.legend-color {
				width: 16px;
				height: 16px;
				margin-right: 5px;
				border-radius: 3px;
				border: 1px solid transparent;
			}
			
			.legend-label {
				font-size: 26rpx;
				color: #333;
			}
		}
	}
	
	.chart-content {
		width: 100%;
		flex: 1;
		position: relative;
		
		.chart-canvas {
			display: block;
			width: 100%;
			height: 100%;
		}
	}
}
</style>
