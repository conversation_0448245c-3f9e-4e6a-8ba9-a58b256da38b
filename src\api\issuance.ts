import request from '@/utils/request'

// 获取发行定价数据
export const getIssuancePricing = (data: object) => {
	return request({
		method: 'POST',
		url: '/issuance/pricing/list',
		data
	})
}

//计算发行定价
export const calculateIssuancePricing = (data: object) => {
	return request({
		method: 'POST',
		url: '/issueManage/issuePricing/queryRealtimePricing',
		data
	})
}
// 获取发行定价详情
export const getIssuancePricingDetail = (data: object) => {
	return request({
		method: 'POST',
		url: '/issuance/pricing/detail',
		data
	})
}

// 获取发行定价筛选选项
export const getIssuancePricingOptions = (data: object) => {
	return request({
		method: 'GET',
		url: '/issuance/pricing/options',
		data
	})
}

// 添加自选
export const addToFavorite = (data: object) => {
	return request({
		method: 'POST',
		url: '/issuance/favorite/add',
		data
	})
}

// 取消自选
export const removeFromFavorite = (data: object) => {
	return request({
		method: 'POST',
		url: '/issuance/favorite/remove',
		data
	})
} 