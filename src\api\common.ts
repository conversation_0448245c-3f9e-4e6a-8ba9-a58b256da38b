import request from '@/utils/request'
// 用户登录
export const getLogin = (data: object) => {
	return request({
		url: '/api/auth/login',
		data
	})
}
// 用户信息
export const getUserInfo = (data: object) => {
	return request({
		url: '/api/auth/me',
		data
	})
}
//免费登录
export const getFreeLogin = (data: object) => {
	return request({
		method: 'POST',
		url: '/system/user/login_noPassword',
		data
	})
}
// pc登录
export const getPcLogin = (data: object) => {
	return request({
		method: 'POST',
		url: '/system/user/login',
		data
	})
}
// 通用获取sql接口
export const getSql = (data: object) => {
	return request({
		method: 'POST',
		url: '/tmCustomColumns/sql',
		data
	})
}
// 查看所有登录人有权限的菜单和按钮
export const getPermissionList = (data: object) => {
	return request({
		url: '/mp/system/common/module/list',
		data
	})
}
// 获取小程序用户信息
export const getMpUserInfo = (data: object) => {
	return request({
		url: '/mp/system/common/userInfo',
		data
	})
}
// 获取债市咨询和专题研究
export const getMarketConsultation = () => {
	return request({
		method: 'POST',
		url: '/cockpit/interestratecockpit/information',
	})
}
// 获取债券类型分析
export const getBondTypeAnalysis = () => {
	return request({
		method: 'POST',
		url: '/cockpit/projectcockpit/bondsOverviewAnalysis?type=01',
	})
}
// 债券概览
export const getBondOverview = (data: object) => {
	return request({
		method: 'POST',
		url: '/issueManage/issueProductChoose/queryBondInfo',
		data
	})
}

// 获取债权类型
export const getBondType = () => {
	return request({
		method: 'POST',
		url: '/common/queryBondType/queryAllBondType',
	})
}


