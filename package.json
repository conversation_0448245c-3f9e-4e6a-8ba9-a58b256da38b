{"name": "uni-vue3-pinna", "version": "0.0.1", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:app-harmony": "uni -p app-harmony", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:app-harmony": "uni build -p app-harmony", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4040520250104002", "@dcloudio/uni-app-harmony": "3.0.0-4040520250104002", "@dcloudio/uni-app-plus": "3.0.0-4040520250104002", "@dcloudio/uni-components": "3.0.0-4040520250104002", "@dcloudio/uni-h5": "3.0.0-4040520250104002", "@dcloudio/uni-mp-alipay": "3.0.0-4040520250104002", "@dcloudio/uni-mp-baidu": "3.0.0-4040520250104002", "@dcloudio/uni-mp-jd": "3.0.0-4040520250104002", "@dcloudio/uni-mp-kuaishou": "3.0.0-4040520250104002", "@dcloudio/uni-mp-lark": "3.0.0-4040520250104002", "@dcloudio/uni-mp-qq": "3.0.0-4040520250104002", "@dcloudio/uni-mp-toutiao": "3.0.0-4040520250104002", "@dcloudio/uni-mp-weixin": "3.0.0-4040520250104002", "@dcloudio/uni-mp-xhs": "3.0.0-4040520250104002", "@dcloudio/uni-quickapp-webview": "3.0.0-4040520250104002", "@dcloudio/uni-ui": "^1.5.7", "dayjs": "^1.11.13", "echarts": "^5.6.0", "lodash": "^4.17.21", "pinia": "^2.0.17", "pinia-plugin-persistedstate": "3.2.1", "qs": "6.5.3", "sortablejs": "^1.15.6", "uchart": "^0.0.1", "vant": "^4.9.18", "vue": "3.5.11", "vue-i18n": "^9.14.2"}, "devDependencies": {"@babel/plugin-transform-react-jsx": "^7.25.9", "@dcloudio/types": "^3.4.14", "@dcloudio/uni-automator": "3.0.0-4040520250104002", "@dcloudio/uni-cli-shared": "3.0.0-4040520250104002", "@dcloudio/uni-stacktracey": "3.0.0-4040520250104002", "@dcloudio/vite-plugin-uni": "3.0.0-4040520250104002", "@rollup/plugin-babel": "^6.0.4", "@rushstack/eslint-patch": "^1.6.1", "@types/node": "^20.10.6", "@uni-helper/uni-app-types": "^0.5.12", "@uni-helper/uni-ui-types": "^0.5.11", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/runtime-core": "^3.5.13", "@vue/tsconfig": "^0.1.3", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.19.2", "husky": "^8.0.0", "less": "^4.3.0", "miniprogram-api-typings": "^3.12.2", "prettier": "^3.3.3", "sass": "1.78.0", "sass-loader": "^7.3.1", "typescript": "^4.9.4", "unplugin-auto-import": "^0.17.6", "vite": "5.2.8", "vue-tsc": "^1.0.24"}}