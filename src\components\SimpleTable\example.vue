<template>
    <view class="example-page">
        <view class="header">
            <text class="title">SimpleTable 替换 zb-table 示例</text>
        </view>

        <!-- 使用SimpleTable替换zb-table -->
        <view class="table-container">
            <SimpleTable ref="simpleTableRef" :columns="tableColumns" :data="bondsList" :loading="isLoading"
                :hasMore="hasMore" height="850rpx" :highlight="true" :stripe="false" :border="false"
                :cell-style="cellStyle" @rowClick="showBondDetail" @cellClick="onCellClick"
                @loadMore="handleLoadMore" />
            
            <!-- 无数据提示 -->
            <view v-if="!bondsList.length && !isLoading" class="no-data-tip">
                <text class="no-data-text">暂无相关数据</text>
            </view>

            <!-- 没有更多数据提示 -->
            <view v-if="!hasMore && bondsList.length > 0 && !isLoading" class="no-more-tip">
                <text class="no-more-text">已显示全部数据</text>
            </view>
        </view>

        <!-- 操作按钮 -->
        <view class="actions">
            <button @click="resetTable">重置表格</button>
            <button @click="addData">添加数据</button>
            <button @click="toggleLoading">切换加载状态</button>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import SimpleTable from './index.vue'
import { getAssetUrl } from '@/config/assets';

// 表格引用
const simpleTableRef = ref()

// 表格状态
const isLoading = ref(false)
const hasMore = ref(true)
const bondsList = ref<any[]>([])

// 表格配置（参考原页面的使用方式）
const tableColumns = ref([
    {
        name: 'name',
        label: '债券简称',
        width: 320,
        fixed: true, // 第一列固定
        emptyString: '--'
    },
    {
        name: 'companyName',
        label: '发行人',
        width: 280,
        emptyString: '--'
    },
    {
        name: 'bondType',
        label: '债券类型',
        width: 200,
        align: 'center' as const,
        emptyString: '--'
    },
    {
        name: 'amount',
        label: '发行规模',
        width: 200,
        align: 'right' as const,
        formatter: (value: any) => {
            return value ? `${value}亿元` : '--'
        }
    },
    {
        name: 'rate',
        label: '票面利率',
        width: 180,
        align: 'center' as const,
        formatter: (value: any) => {
            return value ? `${value}%` : '--'
        }
    },
    {
        name: 'term',
        label: '期限',
        width: 150,
        align: 'center' as const,
        emptyString: '--'
    },
    {
        name: 'rating',
        label: '主体评级',
        width: 180,
        align: 'center' as const,
        emptyString: '--'
    },
    {
        name: 'debtRating',
        label: '债项评级',
        width: 180,
        align: 'center' as const,
        emptyString: '--'
    },
    {
        name: 'issueDate',
        label: '发行日期',
        width: 200,
        align: 'center' as const,
        emptyString: '--'
    }
])

// 模拟数据
const mockData = [
    {
        objectId: '1',
        name: '21国开10',
        companyName: '国家开发银行',
        bondType: '政策性金融债',
        amount: 30,
        rate: 3.25,
        term: '10年',
        rating: 'AAA',
        debtRating: 'AAA',
        issueDate: '2021-06-15'
    },
    {
        objectId: '2',
        name: '22工行01',
        companyName: '中国工商银行股份有限公司',
        bondType: '商业银行债券',
        amount: 50,
        rate: 3.45,
        term: '5年',
        rating: 'AAA',
        debtRating: 'AAA',
        issueDate: '2022-03-20'
    },
    {
        objectId: '3',
        name: '21平安银行02',
        companyName: '平安银行股份有限公司',
        bondType: '同业存单',
        amount: 20,
        rate: 2.95,
        term: '3年',
        rating: 'AAA-',
        debtRating: 'AAA-',
        issueDate: '2021-09-10'
    },
    {
        objectId: '4',
        name: '22招商银行01',
        companyName: '招商银行股份有限公司',
        bondType: '金融债券',
        amount: 40,
        rate: 3.15,
        term: '5年',
        rating: 'AAA',
        debtRating: 'AAA',
        issueDate: '2022-01-25'
    },
    {
        objectId: '5',
        name: '21中石化SCP001',
        companyName: '中国石油化工股份有限公司',
        bondType: '超短期融资券',
        amount: 80,
        rate: 3.80,
        term: '270天',
        rating: 'AAA',
        debtRating: 'A-1',
        issueDate: '2021-08-15'
    }
]

// 单元格样式（参考原页面的cellStyle函数）
const cellStyle = ({ row, column, rowIndex, columnIndex }) => {
    if (column.name === 'name') {
        return {
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            maxWidth: '300rpx'
        }
    }
    return {}
}

// 显示债券详情（参考原页面的showBondDetail函数）
const showBondDetail = (row: any, index: number) => {
    // 实现跳转到债券详情页的逻辑
    uni.navigateTo({
        url: `/subPageA/bond-detail/index?objectId=${row.objectId}`,
    })
}

// 单元格点击事件
const onCellClick = (row: any, column: any, rowIndex: number, colIndex: number) => {
    // 单元格点击处理逻辑
}

// 加载更多数据
const handleLoadMore = () => {
    if (isLoading.value || !hasMore.value) {
        return
    }
    
    isLoading.value = true
    
    // 模拟异步加载数据
    setTimeout(() => {
        // 添加更多模拟数据
        const newData = mockData.map((item, index) => ({
            ...item,
            objectId: `${bondsList.value.length + index + 1}`,
            name: `${item.name}_${bondsList.value.length + index + 1}`
        }))
        
        bondsList.value.push(...newData)
        isLoading.value = false
        
        // 模拟数据加载完毕
        if (bondsList.value.length >= 20) {
            hasMore.value = false
        }
    }, 1000)
}

// 重置表格
const resetTable = () => {
    bondsList.value = [...mockData]
    hasMore.value = true
    isLoading.value = false
    simpleTableRef.value?.clearSelection()
}

// 添加数据
const addData = () => {
    const newItem = {
        objectId: `new_${Date.now()}`,
        name: `新债券_${bondsList.value.length + 1}`,
        companyName: '新发行人',
        bondType: '企业债',
        amount: Math.floor(Math.random() * 100) + 10,
        rate: (Math.random() * 5 + 2).toFixed(2),
        term: '3年',
        rating: 'AA',
        debtRating: 'AA',
        issueDate: '2024-01-01'
    }
    bondsList.value.unshift(newItem)
}

// 切换加载状态
const toggleLoading = () => {
    isLoading.value = !isLoading.value
}

// 页面加载时初始化数据
onMounted(() => {
    bondsList.value = [...mockData]
})
</script>

<style lang="scss" scoped>
.example-page {
    padding: 20rpx;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.header {
    margin-bottom: 30rpx;

    .title {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
    }
}

.table-container {
    background-color: #ffffff;
    border-radius: 20rpx;
    padding: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
    margin-bottom: 30rpx;
}

.actions {
    display: flex;
    gap: 20rpx;
    justify-content: center;

    button {
        padding: 20rpx 40rpx;
        background-color: #007aff;
        color: white;
        border: none;
        border-radius: 10rpx;
        font-size: 28rpx;

        &:active {
            opacity: 0.8;
        }
    }
}

.no-data-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx;


    .no-data-text {
        font-size: 28rpx;
        color: #999;
    }
}

.no-more-tip {
    display: flex;
    justify-content: center;
    padding: 20rpx;

    .no-more-text {
        font-size: 28rpx;
        color: #999;
    }
}
</style>